[{"D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js": "1", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js": "2", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js": "3", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx": "4", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx": "5", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx": "6", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js": "7", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "8", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "9", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "10", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "11", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx": "12", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx": "13", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "14", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "15", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "16", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "17", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "18", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "19", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "20", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx": "21", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "22", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "23", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "24", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js": "25", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js": "26", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "27", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx": "28", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "29", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "30", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "31", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "32", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "33", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx": "34", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "35", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "36", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "37", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "38", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "39", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "40", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "41", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "42", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "43", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "44", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "45", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "46", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "47", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "48", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "49", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "50", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "51", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "52", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "53", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "54", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "55", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx": "56", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "57", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "58", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "59", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx": "60", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "61", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx": "62", "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx": "63", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js": "64", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js": "65", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js": "66", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js": "67", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js": "68", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js": "69", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx": "70", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx": "71", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx": "72", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx": "73", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "74", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx": "75", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "76", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "77", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx": "78", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx": "79", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "80", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx": "81", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx": "82", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "83", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "84", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx": "85", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "86", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx": "87", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "88", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx": "89", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "90", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "91", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "92", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "93", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "94", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "95", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx": "96", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "97", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "98", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "99", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx": "100", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "101", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "102", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "103", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "104", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx": "105", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx": "106", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx": "107", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx": "108", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx": "109", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx": "110", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx": "111", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx": "112", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx": "113", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx": "114", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "115", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "116", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx": "117", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "118", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx": "119", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "120", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "121", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "122", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx": "123", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx": "124", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx": "125", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "126", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "127", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "128", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "129", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "130", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "131", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "132", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "133", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "134", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "135", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "136", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx": "137", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "138", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "139", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "140", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "141", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "142", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx": "143", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "144", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "145", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "146", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx": "147", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "148", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "149", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "150", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "151", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "152", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "153", "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "154", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js": "155", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js": "156", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js": "157", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js": "158", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js": "159", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js": "160", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx": "161", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx": "162", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx": "163", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx": "164", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx": "165", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx": "166", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx": "167", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx": "168", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx": "169", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx": "170", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx": "171", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx": "172", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx": "173", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx": "174", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx": "175", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx": "176", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx": "177", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx": "178", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx": "179", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx": "180", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx": "181", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx": "182", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx": "183", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx": "184", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx": "185", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx": "186", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx": "187", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx": "188", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx": "189", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx": "190", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx": "191", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx": "192", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx": "193", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx": "194", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx": "195", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx": "196", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx": "197", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx": "198", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx": "199", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx": "200", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx": "201", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx": "202", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx": "203", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx": "204", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx": "205", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx": "206", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx": "207", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx": "208", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx": "209", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx": "210", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx": "211", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx": "212", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx": "213", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx": "214", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx": "215", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx": "216", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx": "217", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx": "218", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx": "219", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx": "220", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx": "221", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx": "222", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx": "223", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx": "224", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx": "225", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx": "226", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx": "227", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx": "228", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js": "229", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx": "230", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Patients.jsx": "231", "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Lab.jsx": "232"}, {"size": 535, "mtime": 1745523871993, "results": "233", "hashOfConfig": "234"}, {"size": 344, "mtime": 1745593572980, "results": "235", "hashOfConfig": "234"}, {"size": 362, "mtime": 1745521607447, "results": "236", "hashOfConfig": "234"}, {"size": 25522, "mtime": 1746026848966, "results": "237", "hashOfConfig": "234"}, {"size": 4449, "mtime": 1746028375819, "results": "238", "hashOfConfig": "234"}, {"size": 16619, "mtime": 1746027908524, "results": "239", "hashOfConfig": "234"}, {"size": 41067, "mtime": 1746027767487, "results": "240", "hashOfConfig": "234"}, {"size": 15000, "mtime": 1746027574659, "results": "241", "hashOfConfig": "234"}, {"size": 12368, "mtime": 1745533141636, "results": "242", "hashOfConfig": "234"}, {"size": 13375, "mtime": 1745936744323, "results": "243", "hashOfConfig": "234"}, {"size": 12529, "mtime": 1745533076800, "results": "244", "hashOfConfig": "234"}, {"size": 8477, "mtime": 1745593861157, "results": "245", "hashOfConfig": "234"}, {"size": 16004, "mtime": 1745936541582, "results": "246", "hashOfConfig": "234"}, {"size": 25091, "mtime": 1745537009810, "results": "247", "hashOfConfig": "234"}, {"size": 19988, "mtime": 1745737631275, "results": "248", "hashOfConfig": "234"}, {"size": 17000, "mtime": 1745737806371, "results": "249", "hashOfConfig": "234"}, {"size": 10462, "mtime": 1745736150751, "results": "250", "hashOfConfig": "234"}, {"size": 10639, "mtime": 1745737746063, "results": "251", "hashOfConfig": "234"}, {"size": 0, "mtime": 1745579442828, "results": "252", "hashOfConfig": "234"}, {"size": 0, "mtime": 1745579449398, "results": "253", "hashOfConfig": "234"}, {"size": 4794, "mtime": 1745937254876, "results": "254", "hashOfConfig": "234"}, {"size": 17070, "mtime": 1745938807584, "results": "255", "hashOfConfig": "234"}, {"size": 0, "mtime": 1745579393455, "results": "256", "hashOfConfig": "234"}, {"size": 14691, "mtime": 1745777641172, "results": "257", "hashOfConfig": "234"}, {"size": 8330, "mtime": 1745869519257, "results": "258", "hashOfConfig": "234"}, {"size": 2667, "mtime": 1745659005519, "results": "259", "hashOfConfig": "234"}, {"size": 38724, "mtime": 1745692735196, "results": "260", "hashOfConfig": "234"}, {"size": 11723, "mtime": 1745617858943, "results": "261", "hashOfConfig": "234"}, {"size": 12455, "mtime": 1745617379798, "results": "262", "hashOfConfig": "234"}, {"size": 17414, "mtime": 1745781818524, "results": "263", "hashOfConfig": "234"}, {"size": 27311, "mtime": 1745686427761, "results": "264", "hashOfConfig": "234"}, {"size": 72447, "mtime": 1745704882940, "results": "265", "hashOfConfig": "234"}, {"size": 29816, "mtime": 1745790566641, "results": "266", "hashOfConfig": "234"}, {"size": 33179, "mtime": 1745623491029, "results": "267", "hashOfConfig": "234"}, {"size": 24357, "mtime": 1745655454135, "results": "268", "hashOfConfig": "234"}, {"size": 13189, "mtime": 1745869227266, "results": "269", "hashOfConfig": "234"}, {"size": 28031, "mtime": 1745696382431, "results": "270", "hashOfConfig": "234"}, {"size": 20716, "mtime": 1745593781630, "results": "271", "hashOfConfig": "234"}, {"size": 0, "mtime": 1745265060947, "results": "272", "hashOfConfig": "234"}, {"size": 34793, "mtime": 1745593788566, "results": "273", "hashOfConfig": "234"}, {"size": 0, "mtime": 1745781813233, "results": "274", "hashOfConfig": "234"}, {"size": 33183, "mtime": 1745593804781, "results": "275", "hashOfConfig": "234"}, {"size": 6170, "mtime": 1743975365356, "results": "276", "hashOfConfig": "234"}, {"size": 6117, "mtime": 1743975382403, "results": "277", "hashOfConfig": "234"}, {"size": 13795, "mtime": 1744193211886, "results": "278", "hashOfConfig": "234"}, {"size": 15608, "mtime": 1744192669925, "results": "279", "hashOfConfig": "234"}, {"size": 54388, "mtime": 1745274429676, "results": "280", "hashOfConfig": "234"}, {"size": 33472, "mtime": 1745792982698, "results": "281", "hashOfConfig": "234"}, {"size": 3201, "mtime": 1745616939355, "results": "282", "hashOfConfig": "234"}, {"size": 10848, "mtime": 1745781804706, "results": "283", "hashOfConfig": "234"}, {"size": 3380, "mtime": 1745617128650, "results": "284", "hashOfConfig": "234"}, {"size": 4227, "mtime": 1745266557952, "results": "285", "hashOfConfig": "234"}, {"size": 4300, "mtime": 1745260321900, "results": "286", "hashOfConfig": "234"}, {"size": 8692, "mtime": 1744048711130, "results": "287", "hashOfConfig": "234"}, {"size": 24004, "mtime": 1745789621184, "results": "288", "hashOfConfig": "234"}, {"size": 5842, "mtime": 1745909321963, "results": "289", "hashOfConfig": "234"}, {"size": 3575, "mtime": 1745869264459, "results": "290", "hashOfConfig": "234"}, {"size": 8028, "mtime": 1745867656061, "results": "291", "hashOfConfig": "234"}, {"size": 13592, "mtime": 1745867635690, "results": "292", "hashOfConfig": "234"}, {"size": 6292, "mtime": 1745869103376, "results": "293", "hashOfConfig": "234"}, {"size": 11714, "mtime": 1745869124338, "results": "294", "hashOfConfig": "234"}, {"size": 13725, "mtime": 1745869505266, "results": "295", "hashOfConfig": "234"}, {"size": 18292, "mtime": 1745916840861, "results": "296", "hashOfConfig": "234"}, {"size": 535, "mtime": 1745523871993, "results": "297", "hashOfConfig": "298"}, {"size": 362, "mtime": 1745521607447, "results": "299", "hashOfConfig": "298"}, {"size": 344, "mtime": 1745593572980, "results": "300", "hashOfConfig": "298"}, {"size": 10910, "mtime": 1747475995764, "results": "301", "hashOfConfig": "298"}, {"size": 50383, "mtime": 1747404842150, "results": "302", "hashOfConfig": "298"}, {"size": 2667, "mtime": 1745659005519, "results": "303", "hashOfConfig": "298"}, {"size": 5850, "mtime": 1747428149748, "results": "304", "hashOfConfig": "298"}, {"size": 10684, "mtime": 1747425747846, "results": "305", "hashOfConfig": "298"}, {"size": 12527, "mtime": 1747425232861, "results": "306", "hashOfConfig": "298"}, {"size": 25785, "mtime": 1747325877026, "results": "307", "hashOfConfig": "298"}, {"size": 12683, "mtime": 1747424765007, "results": "308", "hashOfConfig": "298"}, {"size": 18324, "mtime": 1747494439022, "results": "309", "hashOfConfig": "298"}, {"size": 17132, "mtime": 1747424064804, "results": "310", "hashOfConfig": "298"}, {"size": 25328, "mtime": 1747478452947, "results": "311", "hashOfConfig": "298"}, {"size": 18234, "mtime": 1747423594113, "results": "312", "hashOfConfig": "298"}, {"size": 3589, "mtime": 1747425356350, "results": "313", "hashOfConfig": "298"}, {"size": 22212, "mtime": 1747494311372, "results": "314", "hashOfConfig": "298"}, {"size": 14883, "mtime": 1747494399105, "results": "315", "hashOfConfig": "298"}, {"size": 0, "mtime": 1745579442828, "results": "316", "hashOfConfig": "298"}, {"size": 11180, "mtime": 1747494346747, "results": "317", "hashOfConfig": "298"}, {"size": 15376, "mtime": 1747479345968, "results": "318", "hashOfConfig": "298"}, {"size": 0, "mtime": 1745579393455, "results": "319", "hashOfConfig": "298"}, {"size": 17301, "mtime": 1747479769046, "results": "320", "hashOfConfig": "298"}, {"size": 0, "mtime": 1745579449398, "results": "321", "hashOfConfig": "298"}, {"size": 40674, "mtime": 1747514654819, "results": "322", "hashOfConfig": "298"}, {"size": 46027, "mtime": 1747514862877, "results": "323", "hashOfConfig": "298"}, {"size": 27784, "mtime": 1747476930996, "results": "324", "hashOfConfig": "298"}, {"size": 19974, "mtime": 1747476730513, "results": "325", "hashOfConfig": "298"}, {"size": 28285, "mtime": 1747465927821, "results": "326", "hashOfConfig": "298"}, {"size": 27390, "mtime": 1747475380406, "results": "327", "hashOfConfig": "298"}, {"size": 38168, "mtime": 1747466995840, "results": "328", "hashOfConfig": "298"}, {"size": 62529, "mtime": 1747466715266, "results": "329", "hashOfConfig": "298"}, {"size": 21853, "mtime": 1746816821604, "results": "330", "hashOfConfig": "298"}, {"size": 46169, "mtime": 1747476516386, "results": "331", "hashOfConfig": "298"}, {"size": 72726, "mtime": 1747477357174, "results": "332", "hashOfConfig": "298"}, {"size": 85588, "mtime": 1747503514872, "results": "333", "hashOfConfig": "298"}, {"size": 42190, "mtime": 1747519685845, "results": "334", "hashOfConfig": "298"}, {"size": 37741, "mtime": 1747521640488, "results": "335", "hashOfConfig": "298"}, {"size": 56736, "mtime": 1747520158742, "results": "336", "hashOfConfig": "298"}, {"size": 20139, "mtime": 1747545610769, "results": "337", "hashOfConfig": "298"}, {"size": 54586, "mtime": 1747521219705, "results": "338", "hashOfConfig": "298"}, {"size": 9955, "mtime": 1747521450018, "results": "339", "hashOfConfig": "298"}, {"size": 34793, "mtime": 1745593788566, "results": "340", "hashOfConfig": "298"}, {"size": 6117, "mtime": 1743975382403, "results": "341", "hashOfConfig": "298"}, {"size": 33183, "mtime": 1745593804781, "results": "342", "hashOfConfig": "298"}, {"size": 15608, "mtime": 1744192669925, "results": "343", "hashOfConfig": "298"}, {"size": 13795, "mtime": 1744193211886, "results": "344", "hashOfConfig": "298"}, {"size": 0, "mtime": 1745781813233, "results": "345", "hashOfConfig": "298"}, {"size": 49478, "mtime": 1747337212647, "results": "346", "hashOfConfig": "298"}, {"size": 20716, "mtime": 1745593781630, "results": "347", "hashOfConfig": "298"}, {"size": 6170, "mtime": 1743975365356, "results": "348", "hashOfConfig": "298"}, {"size": 101715, "mtime": 1747543720582, "results": "349", "hashOfConfig": "298"}, {"size": 3543, "mtime": 1747464059605, "results": "350", "hashOfConfig": "298"}, {"size": 4449, "mtime": 1746028375819, "results": "351", "hashOfConfig": "298"}, {"size": 15112, "mtime": 1747427483008, "results": "352", "hashOfConfig": "298"}, {"size": 16661, "mtime": 1747426879726, "results": "353", "hashOfConfig": "298"}, {"size": 3924, "mtime": 1747464099492, "results": "354", "hashOfConfig": "298"}, {"size": 9285, "mtime": 1747476809201, "results": "355", "hashOfConfig": "298"}, {"size": 4038, "mtime": 1747517788574, "results": "356", "hashOfConfig": "298"}, {"size": 8692, "mtime": 1744048711130, "results": "357", "hashOfConfig": "298"}, {"size": 4433, "mtime": 1746555422128, "results": "358", "hashOfConfig": "298"}, {"size": 4227, "mtime": 1745266557952, "results": "359", "hashOfConfig": "298"}, {"size": 6776, "mtime": 1746130339157, "results": "360", "hashOfConfig": "298"}, {"size": 3694, "mtime": 1746555400983, "results": "361", "hashOfConfig": "298"}, {"size": 32962, "mtime": 1746489951667, "results": "362", "hashOfConfig": "298"}, {"size": 46904, "mtime": 1747543535318, "results": "363", "hashOfConfig": "298"}, {"size": 21956, "mtime": 1746178385159, "results": "364", "hashOfConfig": "298"}, {"size": 52633, "mtime": 1746471592266, "results": "365", "hashOfConfig": "298"}, {"size": 45474, "mtime": 1746383841136, "results": "366", "hashOfConfig": "298"}, {"size": 1366, "mtime": 1746184118198, "results": "367", "hashOfConfig": "298"}, {"size": 15581, "mtime": 1747543564076, "results": "368", "hashOfConfig": "298"}, {"size": 14747, "mtime": 1747475215108, "results": "369", "hashOfConfig": "298"}, {"size": 53245, "mtime": 1747474059613, "results": "370", "hashOfConfig": "298"}, {"size": 23518, "mtime": 1747516218200, "results": "371", "hashOfConfig": "298"}, {"size": 38503, "mtime": 1747474312377, "results": "372", "hashOfConfig": "298"}, {"size": 9088, "mtime": 1747474414469, "results": "373", "hashOfConfig": "298"}, {"size": 19305, "mtime": 1747474671635, "results": "374", "hashOfConfig": "298"}, {"size": 14545, "mtime": 1746383814479, "results": "375", "hashOfConfig": "298"}, {"size": 1606, "mtime": 1746380988907, "results": "376", "hashOfConfig": "298"}, {"size": 22427, "mtime": 1747476255874, "results": "377", "hashOfConfig": "298"}, {"size": 59674, "mtime": 1747544383255, "results": "378", "hashOfConfig": "298"}, {"size": 3136, "mtime": 1747501530874, "results": "379", "hashOfConfig": "298"}, {"size": 18202, "mtime": 1747501681339, "results": "380", "hashOfConfig": "298"}, {"size": 11061, "mtime": 1747427987728, "results": "381", "hashOfConfig": "298"}, {"size": 33942, "mtime": 1747544346809, "results": "382", "hashOfConfig": "298"}, {"size": 21921, "mtime": 1747508196749, "results": "383", "hashOfConfig": "298"}, {"size": 19707, "mtime": 1747508361654, "results": "384", "hashOfConfig": "298"}, {"size": 3855, "mtime": 1747506687993, "results": "385", "hashOfConfig": "298"}, {"size": 12486, "mtime": 1747510357937, "results": "386", "hashOfConfig": "298"}, {"size": 13064, "mtime": 1747544432606, "results": "387", "hashOfConfig": "298"}, {"size": 18365, "mtime": 1747468109841, "results": "388", "hashOfConfig": "298"}, {"size": 535, "mtime": 1745523871993, "results": "389", "hashOfConfig": "390"}, {"size": 362, "mtime": 1745521607447, "results": "391", "hashOfConfig": "390"}, {"size": 344, "mtime": 1745593572980, "results": "392", "hashOfConfig": "390"}, {"size": 10540, "mtime": 1750629530529, "results": "393", "hashOfConfig": "390"}, {"size": 2667, "mtime": 1745659005519, "results": "394", "hashOfConfig": "390"}, {"size": 50244, "mtime": 1748027195662, "results": "395", "hashOfConfig": "390"}, {"size": 11061, "mtime": 1747427987728, "results": "396", "hashOfConfig": "390"}, {"size": 5850, "mtime": 1747428149748, "results": "397", "hashOfConfig": "390"}, {"size": 6776, "mtime": 1746130339157, "results": "398", "hashOfConfig": "390"}, {"size": 26294, "mtime": 1748027939369, "results": "399", "hashOfConfig": "390"}, {"size": 12887, "mtime": 1748031791438, "results": "400", "hashOfConfig": "390"}, {"size": 16450, "mtime": 1748016139440, "results": "401", "hashOfConfig": "390"}, {"size": 11059, "mtime": 1748031616139, "results": "402", "hashOfConfig": "390"}, {"size": 18211, "mtime": 1748029564302, "results": "403", "hashOfConfig": "390"}, {"size": 11180, "mtime": 1747494346747, "results": "404", "hashOfConfig": "390"}, {"size": 25328, "mtime": 1747478452947, "results": "405", "hashOfConfig": "390"}, {"size": 22212, "mtime": 1747494311372, "results": "406", "hashOfConfig": "390"}, {"size": 17301, "mtime": 1747479769046, "results": "407", "hashOfConfig": "390"}, {"size": 15376, "mtime": 1747479345968, "results": "408", "hashOfConfig": "390"}, {"size": 23096, "mtime": 1748045293068, "results": "409", "hashOfConfig": "390"}, {"size": 19974, "mtime": 1747476730513, "results": "410", "hashOfConfig": "390"}, {"size": 44909, "mtime": 1748039359645, "results": "411", "hashOfConfig": "390"}, {"size": 28285, "mtime": 1747465927821, "results": "412", "hashOfConfig": "390"}, {"size": 67871, "mtime": 1750611248956, "results": "413", "hashOfConfig": "390"}, {"size": 14747, "mtime": 1747475215108, "results": "414", "hashOfConfig": "390"}, {"size": 27784, "mtime": 1747476930996, "results": "415", "hashOfConfig": "390"}, {"size": 59020, "mtime": 1750617206651, "results": "416", "hashOfConfig": "390"}, {"size": 46171, "mtime": 1750605874307, "results": "417", "hashOfConfig": "390"}, {"size": 72726, "mtime": 1747477357174, "results": "418", "hashOfConfig": "390"}, {"size": 27390, "mtime": 1747475380406, "results": "419", "hashOfConfig": "390"}, {"size": 28023, "mtime": 1750606100154, "results": "420", "hashOfConfig": "390"}, {"size": 26506, "mtime": 1748045700910, "results": "421", "hashOfConfig": "390"}, {"size": 38815, "mtime": 1750627196998, "results": "422", "hashOfConfig": "390"}, {"size": 42190, "mtime": 1747519685845, "results": "423", "hashOfConfig": "390"}, {"size": 10390, "mtime": 1750626720144, "results": "424", "hashOfConfig": "390"}, {"size": 20139, "mtime": 1747545610769, "results": "425", "hashOfConfig": "390"}, {"size": 56736, "mtime": 1747520158742, "results": "426", "hashOfConfig": "390"}, {"size": 66905, "mtime": 1750627064097, "results": "427", "hashOfConfig": "390"}, {"size": 93045, "mtime": 1750616892098, "results": "428", "hashOfConfig": "390"}, {"size": 47191, "mtime": 1748802395451, "results": "429", "hashOfConfig": "390"}, {"size": 100845, "mtime": 1748803051039, "results": "430", "hashOfConfig": "390"}, {"size": 45474, "mtime": 1746383841136, "results": "431", "hashOfConfig": "390"}, {"size": 21595, "mtime": 1748805302693, "results": "432", "hashOfConfig": "390"}, {"size": 52779, "mtime": 1748803389900, "results": "433", "hashOfConfig": "390"}, {"size": 50998, "mtime": 1748808216624, "results": "434", "hashOfConfig": "390"}, {"size": 21921, "mtime": 1747508196749, "results": "435", "hashOfConfig": "390"}, {"size": 34059, "mtime": 1750624269534, "results": "436", "hashOfConfig": "390"}, {"size": 25211, "mtime": 1750625077096, "results": "437", "hashOfConfig": "390"}, {"size": 3736, "mtime": 1750628652252, "results": "438", "hashOfConfig": "390"}, {"size": 3924, "mtime": 1747464099492, "results": "439", "hashOfConfig": "390"}, {"size": 3964, "mtime": 1748031168996, "results": "440", "hashOfConfig": "390"}, {"size": 16360, "mtime": 1748031453441, "results": "441", "hashOfConfig": "390"}, {"size": 11916, "mtime": 1748030716874, "results": "442", "hashOfConfig": "390"}, {"size": 21957, "mtime": 1750630474149, "results": "443", "hashOfConfig": "390"}, {"size": 10703, "mtime": 1748045792103, "results": "444", "hashOfConfig": "390"}, {"size": 55507, "mtime": 1748045908880, "results": "445", "hashOfConfig": "390"}, {"size": 40213, "mtime": 1748045992367, "results": "446", "hashOfConfig": "390"}, {"size": 62904, "mtime": 1748046208783, "results": "447", "hashOfConfig": "390"}, {"size": 21499, "mtime": 1748046110445, "results": "448", "hashOfConfig": "390"}, {"size": 4038, "mtime": 1747517788574, "results": "449", "hashOfConfig": "390"}, {"size": 8516, "mtime": 1750616824045, "results": "450", "hashOfConfig": "390"}, {"size": 18202, "mtime": 1747501681339, "results": "451", "hashOfConfig": "390"}, {"size": 1366, "mtime": 1746184118198, "results": "452", "hashOfConfig": "390"}, {"size": 15581, "mtime": 1747543564076, "results": "453", "hashOfConfig": "390"}, {"size": 3605, "mtime": 1748802994868, "results": "454", "hashOfConfig": "390"}, {"size": 1606, "mtime": 1746380988907, "results": "455", "hashOfConfig": "390"}, {"size": 14545, "mtime": 1746383814479, "results": "456", "hashOfConfig": "390"}, {"size": 4101, "mtime": 1750620805624, "results": "457", "hashOfConfig": "390"}, {"size": 12486, "mtime": 1747510357937, "results": "458", "hashOfConfig": "390"}, {"size": 13064, "mtime": 1747544432606, "results": "459", "hashOfConfig": "390"}, {"size": 18365, "mtime": 1747468109841, "results": "460", "hashOfConfig": "390"}, {"size": 18702, "mtime": 1748041839217, "results": "461", "hashOfConfig": "390"}, {"size": 3419, "mtime": 1748043262730, "results": "462", "hashOfConfig": "390"}, {"size": 5567, "mtime": 1748043390657, "results": "463", "hashOfConfig": "390"}, {"size": 10432, "mtime": 1748045669415, "results": "464", "hashOfConfig": "390"}, {"size": 15075, "mtime": 1748803110961, "results": "465", "hashOfConfig": "390"}, {"size": 31074, "mtime": 1750624629627, "results": "466", "hashOfConfig": "390"}, {"size": 10100, "mtime": 1750630433832, "results": "467", "hashOfConfig": "390"}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3igld", {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18v30mu", {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gwk9g1", {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dentlyzer\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\i18n.js", ["1164"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1165", "1166", "1167"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\Select.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\router.js", ["1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1177", "1178", "1179", "1180"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1181"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1182", "1183"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1184"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1185", "1186", "1187"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1188"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1189"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1190"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", ["1191", "1192", "1193"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1194"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1195"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1196", "1197", "1198"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1199"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\people.jsx", ["1200", "1201", "1202", "1203"], [], "D:\\Dentlyzer\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1204"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\i18n.js", ["1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1221", "1222", "1223"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Login.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\DentistInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Contact.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1224", "1225"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Select.jsx", ["1226", "1227"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\ClinicBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\pages\\Payment.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1228", "1229", "1230", "1231"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1232", "1233", "1234", "1235"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1236"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1237", "1238", "1239", "1240", "1241"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1242"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\XRay.jsx", ["1243"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1244", "1245", "1246"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1247", "1248"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1249", "1250", "1251", "1252"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1253"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1254", "1255", "1256", "1257", "1258"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1259"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1260", "1261", "1262", "1263"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1264"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Dashboard.jsx", ["1265"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\XRay.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Patients.jsx", ["1266"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\ToothChart.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Gallery.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Footer.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\PatientNav.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\dentist\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1267"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1268"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1269"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1270"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1271"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\History.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1272"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1273"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1274", "1275", "1276", "1277"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", ["1278"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1279", "1280", "1281"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\components\\Support.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1282", "1283"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1302", "1303", "1304", "1305", "1306", "1307", "1308"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1309"], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\index.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\reportWebVitals.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\App.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\router.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\context\\AuthContext.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\i18n.js", ["1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Support.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Profile.jsx", ["1326", "1327", "1328"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ForgotPassword.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Home.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityServices.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Contact.jsx", ["1329"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Login.jsx", ["1330"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\About.jsx", ["1331", "1332"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\TryAI.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityInfo.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityBook.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\pages\\UniversityConfirmation.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Dashboard.jsx", ["1333", "1334", "1335", "1336", "1337", "1338"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Gallery.jsx", ["1339"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Patients.jsx", ["1340", "1341", "1342", "1343", "1344", "1345"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Calendar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Analytics.jsx", ["1346", "1347", "1348", "1349", "1350"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sheets.jsx", ["1351"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientProfile.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Reviews.jsx", ["1352", "1353", "1354"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ReviewSteps.jsx", ["1355", "1356", "1357"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ToothChart.jsx", ["1358", "1359"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Appointments.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Consent.jsx", ["1360", "1361", "1362", "1363"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\History.jsx", ["1364"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Dashboard.jsx", ["1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\People.jsx", ["1375"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\News.jsx", ["1376"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Appointments.jsx", ["1377"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Reviews.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\Analytics.jsx", ["1378", "1379", "1380", "1381"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\Dashboard.jsx", ["1382", "1383", "1384", "1385"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Universities.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Dashboard.jsx", ["1386"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Clinics.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\News.jsx", ["1387"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Accounts.jsx", ["1388"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Analytics.jsx", ["1389", "1390", "1391"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Dashboard.jsx", ["1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Appointments.jsx", ["1410", "1411"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Analytics.jsx", ["1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Sidebar.jsx", ["1420"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Navbar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Loader.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Footer.jsx", ["1421"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\Navbar.jsx", ["1422", "1423", "1424"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PatientNav.jsx", ["1425"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\OperativeSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\FixedProsthodonticsSheet.jsx", ["1426"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\EndodonticSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\PeriodonticsSheet.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\RemovableProsthodonticsSheet.jsx", ["1427"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\admin\\AdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\ReviewStepsDisplay.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\supervisor\\SignatureManager.jsx", ["1428", "1429", "1430"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ConfirmModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\UniversityDetailsModal.jsx", ["1431"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\SuperAdminSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\SuccessModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\components\\ClinicDetailsModal.jsx", ["1432"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssistantSidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AssignStudentModal.jsx", ["1433"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\AppointmentDetailsModal.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\MedicalTab.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\ProcedureRequests.jsx", ["1434", "1435"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\ProcedureRequestsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\AppointmentsWidget.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\utils\\pdfUtils.js", [], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\superadmin\\Activity.jsx", ["1436"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\assistant\\Patients.jsx", ["1437", "1438", "1439", "1440"], [], "D:\\Dentlyzer_Final - Copy\\dentlyzer-frontend\\src\\student\\Lab.jsx", [], [], {"ruleId": "1441", "severity": 1, "message": "1442", "line": 354, "column": 9, "nodeType": "1443", "messageId": "1444", "endLine": 354, "endColumn": 15}, {"ruleId": "1445", "severity": 1, "message": "1446", "line": 8, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 20}, {"ruleId": "1445", "severity": 1, "message": "1449", "line": 8, "column": 22, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1451", "line": 47, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 47, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1452", "line": 48, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 48, "endColumn": 23}, {"ruleId": "1445", "severity": 1, "message": "1453", "line": 49, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 49, "endColumn": 23}, {"ruleId": "1445", "severity": 1, "message": "1454", "line": 51, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 51, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1455", "line": 52, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 52, "endColumn": 29}, {"ruleId": "1445", "severity": 1, "message": "1456", "line": 53, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 53, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1457", "line": 54, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 54, "endColumn": 19}, {"ruleId": "1445", "severity": 1, "message": "1458", "line": 55, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 55, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1459", "line": 56, "column": 8, "nodeType": "1447", "messageId": "1448", "endLine": 56, "endColumn": 27}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 9, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 56}, {"ruleId": "1445", "severity": 1, "message": "1462", "line": 9, "column": 58, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 73}, {"ruleId": "1445", "severity": 1, "message": "1463", "line": 10, "column": 38, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 55}, {"ruleId": "1445", "severity": 1, "message": "1464", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 20}, {"ruleId": "1465", "severity": 1, "message": "1466", "line": 132, "column": 6, "nodeType": "1467", "endLine": 132, "endColumn": 18, "suggestions": "1468"}, {"ruleId": "1445", "severity": 1, "message": "1469", "line": 725, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 725, "endColumn": 21}, {"ruleId": "1470", "severity": 1, "message": "1471", "line": 535, "column": 31, "nodeType": "1472", "endLine": 544, "endColumn": 33}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1474", "line": 9, "column": 86, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 93}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 10, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1475", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1476", "line": 1, "column": 38, "nodeType": "1447", "messageId": "1448", "endLine": 1, "endColumn": 48}, {"ruleId": "1445", "severity": 1, "message": "1469", "line": 609, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 609, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1477", "line": 635, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 635, "endColumn": 19}, {"ruleId": "1445", "severity": 1, "message": "1478", "line": 8, "column": 63, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 70}, {"ruleId": "1445", "severity": 1, "message": "1479", "line": 10, "column": 54, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 67}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 9, "column": 18, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1480", "line": 9, "column": 32, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 42}, {"ruleId": "1445", "severity": 1, "message": "1481", "line": 9, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 52}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 57, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 57, "endColumn": 13}, {"ruleId": "1483", "severity": 2, "message": "1484", "line": 22, "column": 85, "nodeType": "1485", "messageId": "1486", "endLine": 22, "endColumn": 94}, {"ruleId": "1483", "severity": 2, "message": "1487", "line": 24, "column": 91, "nodeType": "1485", "messageId": "1486", "endLine": 24, "endColumn": 102}, {"ruleId": "1483", "severity": 2, "message": "1488", "line": 25, "column": 94, "nodeType": "1485", "messageId": "1486", "endLine": 25, "endColumn": 102}, {"ruleId": "1465", "severity": 1, "message": "1489", "line": 67, "column": 6, "nodeType": "1467", "endLine": 67, "endColumn": 39, "suggestions": "1490"}, {"ruleId": "1465", "severity": 1, "message": "1489", "line": 81, "column": 6, "nodeType": "1467", "endLine": 81, "endColumn": 39, "suggestions": "1491"}, {"ruleId": "1441", "severity": 1, "message": "1492", "line": 338, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 338, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1493", "line": 339, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 339, "endColumn": 16}, {"ruleId": "1441", "severity": 1, "message": "1494", "line": 340, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 340, "endColumn": 19}, {"ruleId": "1441", "severity": 1, "message": "1495", "line": 341, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 341, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1496", "line": 342, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 342, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1497", "line": 343, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 343, "endColumn": 20}, {"ruleId": "1441", "severity": 1, "message": "1498", "line": 370, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 370, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1499", "line": 388, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 388, "endColumn": 21}, {"ruleId": "1441", "severity": 1, "message": "1500", "line": 397, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 397, "endColumn": 16}, {"ruleId": "1441", "severity": 1, "message": "1501", "line": 409, "column": 5, "nodeType": "1443", "messageId": "1444", "endLine": 409, "endColumn": 18}, {"ruleId": "1441", "severity": 1, "message": "1492", "line": 707, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 707, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1493", "line": 708, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 708, "endColumn": 20}, {"ruleId": "1441", "severity": 1, "message": "1494", "line": 709, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 709, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1495", "line": 710, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 710, "endColumn": 31}, {"ruleId": "1441", "severity": 1, "message": "1496", "line": 711, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 711, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1497", "line": 712, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 712, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 9, "column": 18, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1480", "line": 9, "column": 32, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 42}, {"ruleId": "1445", "severity": 1, "message": "1481", "line": 9, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 52}, {"ruleId": "1445", "severity": 1, "message": "1449", "line": 8, "column": 22, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1502", "line": 1, "column": 17, "nodeType": "1447", "messageId": "1448", "endLine": 1, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1503", "line": 4, "column": 41, "nodeType": "1447", "messageId": "1448", "endLine": 4, "endColumn": 53}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 9, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 56}, {"ruleId": "1445", "severity": 1, "message": "1462", "line": 9, "column": 58, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 73}, {"ruleId": "1445", "severity": 1, "message": "1463", "line": 10, "column": 38, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 55}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1474", "line": 9, "column": 86, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 93}, {"ruleId": "1445", "severity": 1, "message": "1504", "line": 9, "column": 95, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 103}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 10, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 213, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 213, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1505", "line": 9, "column": 96, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 111}, {"ruleId": "1445", "severity": 1, "message": "1506", "line": 25, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 25, "endColumn": 32}, {"ruleId": "1445", "severity": 1, "message": "1507", "line": 28, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 28, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1508", "line": 202, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 202, "endColumn": 26}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 215, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 215, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 23, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 27}, {"ruleId": "1445", "severity": 1, "message": "1509", "line": 9, "column": 43, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 53}, {"ruleId": "1445", "severity": 1, "message": "1510", "line": 10, "column": 19, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 25}, {"ruleId": "1465", "severity": 1, "message": "1511", "line": 53, "column": 6, "nodeType": "1467", "endLine": 53, "endColumn": 8, "suggestions": "1512"}, {"ruleId": "1465", "severity": 1, "message": "1511", "line": 78, "column": 6, "nodeType": "1467", "endLine": 78, "endColumn": 32, "suggestions": "1513"}, {"ruleId": "1465", "severity": 1, "message": "1466", "line": 132, "column": 6, "nodeType": "1467", "endLine": 132, "endColumn": 18, "suggestions": "1514"}, {"ruleId": "1445", "severity": 1, "message": "1469", "line": 726, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 726, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1515", "line": 11, "column": 33, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 47}, {"ruleId": "1445", "severity": 1, "message": "1516", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1517", "line": 12, "column": 31, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 44}, {"ruleId": "1445", "severity": 1, "message": "1518", "line": 40, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 40, "endColumn": 20}, {"ruleId": "1465", "severity": 1, "message": "1489", "line": 126, "column": 6, "nodeType": "1467", "endLine": 126, "endColumn": 39, "suggestions": "1519"}, {"ruleId": "1445", "severity": 1, "message": "1515", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1475", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1520", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 15}, {"ruleId": "1445", "severity": 1, "message": "1521", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 9}, {"ruleId": "1445", "severity": 1, "message": "1522", "line": 81, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 81, "endColumn": 14}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 87, "column": 6, "nodeType": "1467", "endLine": 87, "endColumn": 29, "suggestions": "1524"}, {"ruleId": "1445", "severity": 1, "message": "1525", "line": 14, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 10}, {"ruleId": "1445", "severity": 1, "message": "1526", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1528", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 19}, {"ruleId": "1465", "severity": 1, "message": "1529", "line": 72, "column": 6, "nodeType": "1467", "endLine": 72, "endColumn": 29, "suggestions": "1530"}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 110, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 110, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1531", "line": 5, "column": 37, "nodeType": "1447", "messageId": "1448", "endLine": 5, "endColumn": 43}, {"ruleId": "1445", "severity": 1, "message": "1532", "line": 2, "column": 103, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 110}, {"ruleId": "1445", "severity": 1, "message": "1533", "line": 25, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 25, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1534", "line": 257, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 257, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1535", "line": 91, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 91, "endColumn": 31}, {"ruleId": "1445", "severity": 1, "message": "1532", "line": 2, "column": 106, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 113}, {"ruleId": "1445", "severity": 1, "message": "1536", "line": 4, "column": 23, "nodeType": "1447", "messageId": "1448", "endLine": 4, "endColumn": 32}, {"ruleId": "1445", "severity": 1, "message": "1478", "line": 4, "column": 43, "nodeType": "1447", "messageId": "1448", "endLine": 4, "endColumn": 50}, {"ruleId": "1445", "severity": 1, "message": "1537", "line": 14, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 15}, {"ruleId": "1445", "severity": 1, "message": "1538", "line": 28, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 28, "endColumn": 23}, {"ruleId": "1445", "severity": 1, "message": "1539", "line": 3, "column": 25, "nodeType": "1447", "messageId": "1448", "endLine": 3, "endColumn": 38}, {"ruleId": "1445", "severity": 1, "message": "1538", "line": 31, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 31, "endColumn": 23}, {"ruleId": "1465", "severity": 1, "message": "1540", "line": 98, "column": 6, "nodeType": "1467", "endLine": 98, "endColumn": 24, "suggestions": "1541"}, {"ruleId": "1445", "severity": 1, "message": "1542", "line": 219, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 219, "endColumn": 28}, {"ruleId": "1445", "severity": 1, "message": "1543", "line": 26, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 26, "endColumn": 18}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 420, "column": 6, "nodeType": "1467", "endLine": 420, "endColumn": 29, "suggestions": "1544"}, {"ruleId": "1445", "severity": 1, "message": "1545", "line": 10, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 12}, {"ruleId": "1445", "severity": 1, "message": "1546", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1509", "line": 14, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 15, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 15, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1526", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1528", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 19}, {"ruleId": "1445", "severity": 1, "message": "1547", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1548", "line": 19, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 19, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1549", "line": 21, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1550", "line": 21, "column": 15, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1551", "line": 21, "column": 20, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1552", "line": 66, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 66, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1553", "line": 171, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 171, "endColumn": 36}, {"ruleId": "1445", "severity": 1, "message": "1554", "line": 185, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 185, "endColumn": 36}, {"ruleId": "1445", "severity": 1, "message": "1555", "line": 211, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 211, "endColumn": 35}, {"ruleId": "1445", "severity": 1, "message": "1556", "line": 232, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 232, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1557", "line": 268, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 268, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1558", "line": 323, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 323, "endColumn": 29}, {"ruleId": "1445", "severity": 1, "message": "1546", "line": 10, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1545", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 12}, {"ruleId": "1445", "severity": 1, "message": "1559", "line": 73, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 73, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1543", "line": 74, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 74, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1552", "line": 82, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 82, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 92, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 92, "endColumn": 13}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 153, "column": 6, "nodeType": "1467", "endLine": 153, "endColumn": 29, "suggestions": "1560"}, {"ruleId": "1465", "severity": 1, "message": "1561", "line": 29, "column": 6, "nodeType": "1467", "endLine": 29, "endColumn": 40, "suggestions": "1562"}, {"ruleId": "1441", "severity": 1, "message": "1492", "line": 336, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 336, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1493", "line": 337, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 337, "endColumn": 16}, {"ruleId": "1441", "severity": 1, "message": "1494", "line": 338, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 338, "endColumn": 19}, {"ruleId": "1441", "severity": 1, "message": "1495", "line": 339, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 339, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1496", "line": 340, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 340, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1497", "line": 341, "column": 11, "nodeType": "1443", "messageId": "1444", "endLine": 341, "endColumn": 20}, {"ruleId": "1441", "severity": 1, "message": "1498", "line": 368, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 368, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1499", "line": 386, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 386, "endColumn": 21}, {"ruleId": "1441", "severity": 1, "message": "1500", "line": 395, "column": 7, "nodeType": "1443", "messageId": "1444", "endLine": 395, "endColumn": 16}, {"ruleId": "1441", "severity": 1, "message": "1501", "line": 407, "column": 5, "nodeType": "1443", "messageId": "1444", "endLine": 407, "endColumn": 18}, {"ruleId": "1441", "severity": 1, "message": "1492", "line": 702, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 702, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1493", "line": 703, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 703, "endColumn": 20}, {"ruleId": "1441", "severity": 1, "message": "1494", "line": 704, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 704, "endColumn": 23}, {"ruleId": "1441", "severity": 1, "message": "1495", "line": 705, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 705, "endColumn": 31}, {"ruleId": "1441", "severity": 1, "message": "1496", "line": 706, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 706, "endColumn": 27}, {"ruleId": "1441", "severity": 1, "message": "1497", "line": 707, "column": 15, "nodeType": "1443", "messageId": "1444", "endLine": 707, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 9, "column": 18, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1480", "line": 9, "column": 32, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 42}, {"ruleId": "1445", "severity": 1, "message": "1481", "line": 9, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 52}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 9, "column": 47, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 54}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 6, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 6, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1449", "line": 8, "column": 22, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 12, "column": 34, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 42}, {"ruleId": "1445", "severity": 1, "message": "1461", "line": 12, "column": 44, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 56}, {"ruleId": "1445", "severity": 1, "message": "1462", "line": 12, "column": 58, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 73}, {"ruleId": "1445", "severity": 1, "message": "1517", "line": 12, "column": 75, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 88}, {"ruleId": "1445", "severity": 1, "message": "1563", "line": 35, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 35, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1564", "line": 136, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 136, "endColumn": 28}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 213, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 213, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1565", "line": 9, "column": 18, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 28}, {"ruleId": "1445", "severity": 1, "message": "1474", "line": 9, "column": 86, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 93}, {"ruleId": "1445", "severity": 1, "message": "1504", "line": 9, "column": 95, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 103}, {"ruleId": "1445", "severity": 1, "message": "1450", "line": 10, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1566", "line": 197, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 197, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1473", "line": 2, "column": 23, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 27}, {"ruleId": "1445", "severity": 1, "message": "1567", "line": 345, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 345, "endColumn": 30}, {"ruleId": "1445", "severity": 1, "message": "1568", "line": 358, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 358, "endColumn": 29}, {"ruleId": "1445", "severity": 1, "message": "1569", "line": 367, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 367, "endColumn": 33}, {"ruleId": "1445", "severity": 1, "message": "1570", "line": 409, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 409, "endColumn": 32}, {"ruleId": "1445", "severity": 1, "message": "1533", "line": 25, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 25, "endColumn": 22}, {"ruleId": "1445", "severity": 1, "message": "1506", "line": 25, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 25, "endColumn": 32}, {"ruleId": "1445", "severity": 1, "message": "1507", "line": 28, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 28, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1508", "line": 226, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 226, "endColumn": 26}, {"ruleId": "1445", "severity": 1, "message": "1510", "line": 10, "column": 19, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 25}, {"ruleId": "1465", "severity": 1, "message": "1511", "line": 53, "column": 6, "nodeType": "1467", "endLine": 53, "endColumn": 8, "suggestions": "1571"}, {"ruleId": "1465", "severity": 1, "message": "1511", "line": 78, "column": 6, "nodeType": "1467", "endLine": 78, "endColumn": 32, "suggestions": "1572"}, {"ruleId": "1465", "severity": 1, "message": "1466", "line": 132, "column": 6, "nodeType": "1467", "endLine": 132, "endColumn": 18, "suggestions": "1573"}, {"ruleId": "1445", "severity": 1, "message": "1469", "line": 726, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 726, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1536", "line": 4, "column": 23, "nodeType": "1447", "messageId": "1448", "endLine": 4, "endColumn": 32}, {"ruleId": "1445", "severity": 1, "message": "1478", "line": 4, "column": 43, "nodeType": "1447", "messageId": "1448", "endLine": 4, "endColumn": 50}, {"ruleId": "1445", "severity": 1, "message": "1537", "line": 14, "column": 11, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 15}, {"ruleId": "1445", "severity": 1, "message": "1538", "line": 30, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 30, "endColumn": 23}, {"ruleId": "1445", "severity": 1, "message": "1574", "line": 9, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 9, "endColumn": 27}, {"ruleId": "1445", "severity": 1, "message": "1545", "line": 10, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 12}, {"ruleId": "1445", "severity": 1, "message": "1525", "line": 11, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 10}, {"ruleId": "1445", "severity": 1, "message": "1515", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1475", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1520", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 15}, {"ruleId": "1445", "severity": 1, "message": "1521", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 9}, {"ruleId": "1445", "severity": 1, "message": "1526", "line": 19, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 19, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 20, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 20, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1522", "line": 82, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 82, "endColumn": 14}, {"ruleId": "1575", "severity": 1, "message": "1576", "line": 875, "column": 42, "nodeType": "1577", "messageId": "1578", "endLine": 875, "endColumn": 100}, {"ruleId": "1465", "severity": 1, "message": "1489", "line": 126, "column": 6, "nodeType": "1467", "endLine": 126, "endColumn": 39, "suggestions": "1579"}, {"ruleId": "1465", "severity": 1, "message": "1529", "line": 74, "column": 6, "nodeType": "1467", "endLine": 74, "endColumn": 29, "suggestions": "1580"}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 87, "column": 6, "nodeType": "1467", "endLine": 87, "endColumn": 29, "suggestions": "1581"}, {"ruleId": "1445", "severity": 1, "message": "1525", "line": 14, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 10}, {"ruleId": "1445", "severity": 1, "message": "1526", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1528", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 19}, {"ruleId": "1445", "severity": 1, "message": "1515", "line": 11, "column": 33, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 47}, {"ruleId": "1445", "severity": 1, "message": "1516", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1517", "line": 12, "column": 31, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 44}, {"ruleId": "1445", "severity": 1, "message": "1518", "line": 50, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 50, "endColumn": 20}, {"ruleId": "1445", "severity": 1, "message": "1462", "line": 5, "column": 24, "nodeType": "1447", "messageId": "1448", "endLine": 5, "endColumn": 39}, {"ruleId": "1445", "severity": 1, "message": "1482", "line": 107, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 107, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1531", "line": 5, "column": 37, "nodeType": "1447", "messageId": "1448", "endLine": 5, "endColumn": 43}, {"ruleId": "1445", "severity": 1, "message": "1582", "line": 11, "column": 96, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 102}, {"ruleId": "1445", "severity": 1, "message": "1525", "line": 11, "column": 117, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 124}, {"ruleId": "1445", "severity": 1, "message": "1583", "line": 11, "column": 126, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 141}, {"ruleId": "1445", "severity": 1, "message": "1545", "line": 10, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 12}, {"ruleId": "1445", "severity": 1, "message": "1546", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1509", "line": 14, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 14, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1527", "line": 15, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 15, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1526", "line": 16, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 16, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1528", "line": 17, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 17, "endColumn": 19}, {"ruleId": "1445", "severity": 1, "message": "1547", "line": 18, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 18, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1548", "line": 19, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 19, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1549", "line": 21, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1550", "line": 21, "column": 15, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1551", "line": 21, "column": 20, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1552", "line": 66, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 66, "endColumn": 18}, {"ruleId": "1445", "severity": 1, "message": "1553", "line": 171, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 171, "endColumn": 36}, {"ruleId": "1445", "severity": 1, "message": "1554", "line": 185, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 185, "endColumn": 36}, {"ruleId": "1445", "severity": 1, "message": "1555", "line": 211, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 211, "endColumn": 35}, {"ruleId": "1445", "severity": 1, "message": "1556", "line": 232, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 232, "endColumn": 24}, {"ruleId": "1445", "severity": 1, "message": "1557", "line": 268, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 268, "endColumn": 25}, {"ruleId": "1445", "severity": 1, "message": "1558", "line": 323, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 323, "endColumn": 29}, {"ruleId": "1445", "severity": 1, "message": "1543", "line": 26, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 26, "endColumn": 18}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 416, "column": 6, "nodeType": "1467", "endLine": 416, "endColumn": 29, "suggestions": "1584"}, {"ruleId": "1445", "severity": 1, "message": "1546", "line": 10, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 10, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1585", "line": 11, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 11, "endColumn": 16}, {"ruleId": "1445", "severity": 1, "message": "1545", "line": 12, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 12, "endColumn": 12}, {"ruleId": "1445", "severity": 1, "message": "1464", "line": 13, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 13, "endColumn": 13}, {"ruleId": "1445", "severity": 1, "message": "1586", "line": 21, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 21, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1587", "line": 22, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 22, "endColumn": 14}, {"ruleId": "1445", "severity": 1, "message": "1588", "line": 77, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 77, "endColumn": 22}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 163, "column": 6, "nodeType": "1467", "endLine": 163, "endColumn": 29, "suggestions": "1589"}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 8, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 8, "endColumn": 10}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 5, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 5, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1460", "line": 6, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 6, "endColumn": 17}, {"ruleId": "1445", "severity": 1, "message": "1590", "line": 69, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 69, "endColumn": 31}, {"ruleId": "1445", "severity": 1, "message": "1591", "line": 110, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 110, "endColumn": 17}, {"ruleId": "1465", "severity": 1, "message": "1592", "line": 41, "column": 6, "nodeType": "1467", "endLine": 41, "endColumn": 32, "suggestions": "1593"}, {"ruleId": "1445", "severity": 1, "message": "1534", "line": 292, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 292, "endColumn": 21}, {"ruleId": "1445", "severity": 1, "message": "1535", "line": 92, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 92, "endColumn": 31}, {"ruleId": "1445", "severity": 1, "message": "1538", "line": 31, "column": 10, "nodeType": "1447", "messageId": "1448", "endLine": 31, "endColumn": 23}, {"ruleId": "1465", "severity": 1, "message": "1540", "line": 98, "column": 6, "nodeType": "1467", "endLine": 98, "endColumn": 24, "suggestions": "1594"}, {"ruleId": "1445", "severity": 1, "message": "1542", "line": 219, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 219, "endColumn": 28}, {"ruleId": "1445", "severity": 1, "message": "1532", "line": 2, "column": 103, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 110}, {"ruleId": "1445", "severity": 1, "message": "1532", "line": 2, "column": 106, "nodeType": "1447", "messageId": "1448", "endLine": 2, "endColumn": 113}, {"ruleId": "1465", "severity": 1, "message": "1561", "line": 29, "column": 6, "nodeType": "1467", "endLine": 29, "endColumn": 40, "suggestions": "1595"}, {"ruleId": "1445", "severity": 1, "message": "1596", "line": 13, "column": 3, "nodeType": "1447", "messageId": "1448", "endLine": 13, "endColumn": 11}, {"ruleId": "1445", "severity": 1, "message": "1597", "line": 32, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 32, "endColumn": 17}, {"ruleId": "1465", "severity": 1, "message": "1598", "line": 33, "column": 6, "nodeType": "1467", "endLine": 33, "endColumn": 32, "suggestions": "1599"}, {"ruleId": "1445", "severity": 1, "message": "1597", "line": 41, "column": 9, "nodeType": "1447", "messageId": "1448", "endLine": 41, "endColumn": 17}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 104, "column": 6, "nodeType": "1467", "endLine": 104, "endColumn": 19, "suggestions": "1600"}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 118, "column": 6, "nodeType": "1467", "endLine": 118, "endColumn": 19, "suggestions": "1601"}, {"ruleId": "1465", "severity": 1, "message": "1523", "line": 160, "column": 6, "nodeType": "1467", "endLine": 160, "endColumn": 8, "suggestions": "1602"}, "no-dupe-keys", "Duplicate key 'select'.", "ObjectExpression", "unexpected", "no-unused-vars", "'FaLinkedin' is defined but never used.", "Identifier", "unusedVar", "'FaGithub' is defined but never used.", "'RiAiGenerate' is defined but never used.", "'DentistDashboard' is defined but never used.", "'DentistCalendar' is defined but never used.", "'DentistPatients' is defined but never used.", "'DentistAnalytics' is defined but never used.", "'DentistPatientProfile' is defined but never used.", "'DentistGallery' is defined but never used.", "'DentistXRay' is defined but never used.", "'Dentist<PERSON><PERSON><PERSON><PERSON>hart' is defined but never used.", "'DentistAppointments' is defined but never used.", "'FaTooth' is defined but never used.", "'FaUniversity' is defined but never used.", "'FaClinicMedical' is defined but never used.", "'MdHealthAndSafety' is defined but never used.", "'FaChartPie' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'upperTeethNumbers'. Either include it or remove the dependency array.", "ArrayExpression", ["1603"], "'toothColor' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Link' is defined but never used.", "'FaPills' is defined but never used.", "'FaNewspaper' is defined but never used.", "'useContext' is defined but never used.", "'surfaces' is assigned a value but never used.", "'FaTimes' is defined but never used.", "'FaFileMedical' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaIdCard' is defined but never used.", "'item' is assigned a value but never used.", "react/jsx-no-undef", "'FaUserAlt' is not defined.", "JSXIdentifier", "undefined", "'FaUserNurse' is not defined.", "'FaUserMd' is not defined.", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", ["1604"], ["1605"], "Duplicate key 'NationalID'.", "Duplicate key 'Age'.", "Duplicate key 'Gender'.", "Duplicate key '<PERSON><PERSON><PERSON><PERSON><PERSON>'.", "Duplicate key 'Occupation'.", "Duplicate key 'Address'.", "Duplicate key 'universityInfo'.", "Duplicate key 'universities'.", "Duplicate key 'clinics'.", "Duplicate key 'dentistInfo'.", "'useState' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'FaUpload' is defined but never used.", "'FaHourglassHalf' is defined but never used.", "'procedureTypeAnalytics' is assigned a value but never used.", "'showReviewModal' is assigned a value but never used.", "'handleReviewClick' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReviewSteps'. Either include it or remove the dependency array.", ["1606"], ["1607"], ["1608"], "'FaUserGraduate' is defined but never used.", "'FaCalendarCheck' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'allReviews' is assigned a value but never used.", ["1609"], "'FaArrowRight' is defined but never used.", "'FaBell' is defined but never used.", "'news' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1610"], "'FaUsers' is defined but never used.", "'FaUserNurse' is defined but never used.", "'FaUserMd' is defined but never used.", "'FaClipboardCheck' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", ["1611"], "'FaEdit' is defined but never used.", "'FaImage' is defined but never used.", "'savedSheetId' is assigned a value but never used.", "'renderSelect' is assigned a value but never used.", "'handleDeepNestedChange' is assigned a value but never used.", "'FaFileAlt' is defined but never used.", "'user' is assigned a value but never used.", "'canvasContext' is assigned a value but never used.", "'FaTimesCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSavedSignature'. Either include it or remove the dependency array.", ["1612"], "'toggleSignatureType' is assigned a value but never used.", "'students' is assigned a value but never used.", ["1613"], "'FaUserAlt' is defined but never used.", "'FaChartLine' is defined but never used.", "'FaExclamationTriangle' is defined but never used.", "'FaRegCalendarCheck' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Line' is defined but never used.", "'container' is assigned a value but never used.", "'appointmentsStatusChartData' is assigned a value but never used.", "'appointmentsByTypeChartData' is assigned a value but never used.", "'appointmentsByDayChartData' is assigned a value but never used.", "'pieChartOptions' is assigned a value but never used.", "'lineChartOptions' is assigned a value but never used.", "'downloadAppointments' is assigned a value but never used.", "'patients' is assigned a value but never used.", ["1614"], "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1615"], "'chronicDiseases' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'FaUserPlus' is defined but never used.", "'handleAddPatient' is assigned a value but never used.", "'appointmentStatusData' is assigned a value but never used.", "'appointmentTypesData' is assigned a value but never used.", "'appointmentsPerMonthData' is assigned a value but never used.", "'treatmentSheetTrendData' is assigned a value but never used.", ["1616"], ["1617"], ["1618"], "'printElementAsPDF' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", ["1619"], ["1620"], ["1621"], "'FaCogs' is defined but never used.", "'FaGraduationCap' is defined but never used.", ["1622"], "'FaCalendarAlt' is defined but never used.", "'FaMapMarkerAlt' is defined but never used.", "'FaBriefcase' is defined but never used.", "'appointments' is assigned a value but never used.", ["1623"], "'toggleServicesDropdown' is assigned a value but never used.", "'dropdown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLabRequests'. Either include it or remove the dependency array.", ["1624"], ["1625"], ["1626"], "'FaFilter' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchActivities'. Either include it or remove the dependency array.", ["1627"], ["1628"], ["1629"], ["1630"], {"desc": "1631", "fix": "1632"}, {"desc": "1633", "fix": "1634"}, {"desc": "1633", "fix": "1635"}, {"desc": "1636", "fix": "1637"}, {"desc": "1638", "fix": "1639"}, {"desc": "1631", "fix": "1640"}, {"desc": "1633", "fix": "1641"}, {"desc": "1642", "fix": "1643"}, {"desc": "1644", "fix": "1645"}, {"desc": "1646", "fix": "1647"}, {"desc": "1642", "fix": "1648"}, {"desc": "1642", "fix": "1649"}, {"desc": "1650", "fix": "1651"}, {"desc": "1636", "fix": "1652"}, {"desc": "1638", "fix": "1653"}, {"desc": "1631", "fix": "1654"}, {"desc": "1633", "fix": "1655"}, {"desc": "1644", "fix": "1656"}, {"desc": "1642", "fix": "1657"}, {"desc": "1642", "fix": "1658"}, {"desc": "1642", "fix": "1659"}, {"desc": "1660", "fix": "1661"}, {"desc": "1646", "fix": "1662"}, {"desc": "1650", "fix": "1663"}, {"desc": "1664", "fix": "1665"}, {"desc": "1666", "fix": "1667"}, {"desc": "1666", "fix": "1668"}, {"desc": "1669", "fix": "1670"}, "Update the dependencies array to be: [nationalId, upperTeethNumbers]", {"range": "1671", "text": "1672"}, "Update the dependencies array to be: [user, token, navigate, category, categories]", {"range": "1673", "text": "1674"}, {"range": "1675", "text": "1674"}, "Update the dependencies array to be: [fetchReviewSteps]", {"range": "1676", "text": "1677"}, "Update the dependencies array to be: [fetchReviewSteps, reviewData.procedureType]", {"range": "1678", "text": "1679"}, {"range": "1680", "text": "1672"}, {"range": "1681", "text": "1674"}, "Update the dependencies array to be: [user, token, navigate, fetchData]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [user, token, navigate, fetchNews]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [fetchSavedSignature, initialSignature]", {"range": "1686", "text": "1687"}, {"range": "1688", "text": "1683"}, {"range": "1689", "text": "1683"}, "Update the dependencies array to be: [isOpen, appointment, token, user, fetchStudents]", {"range": "1690", "text": "1691"}, {"range": "1692", "text": "1677"}, {"range": "1693", "text": "1679"}, {"range": "1694", "text": "1672"}, {"range": "1695", "text": "1674"}, {"range": "1696", "text": "1685"}, {"range": "1697", "text": "1683"}, {"range": "1698", "text": "1683"}, {"range": "1699", "text": "1683"}, "Update the dependencies array to be: [fetchLabRequests, nationalId, showLabPopup]", {"range": "1700", "text": "1701"}, {"range": "1702", "text": "1687"}, {"range": "1703", "text": "1691"}, "Update the dependencies array to be: [fetchActivities, filters, pagination.page]", {"range": "1704", "text": "1705"}, "Update the dependencies array to be: [user, token, fetchData]", {"range": "1706", "text": "1707"}, {"range": "1708", "text": "1707"}, "Update the dependencies array to be: [fetchData]", {"range": "1709", "text": "1710"}, [4827, 4839], "[nationalId, upperTeethNumbers]", [2682, 2715], "[user, token, navigate, category, categories]", [3328, 3361], [1741, 1743], "[fetchReviewSteps]", [2511, 2537], "[fetchReviewSteps, reviewData.procedureType]", [4827, 4839], [4697, 4730], [2932, 2955], "[user, token, navigate, fetchData]", [2276, 2299], "[user, token, navigate, fetchNews]", [3227, 3245], "[fetchSavedSignature, initialSignature]", [17029, 17052], [4536, 4559], [941, 975], "[isOpen, appointment, token, user, fetchStudents]", [1741, 1743], [2511, 2537], [4827, 4839], [4697, 4730], [2333, 2356], [2932, 2955], [17146, 17169], [4590, 4613], [1778, 1804], "[fetchLabRequests, nationalId, showLabPopup]", [3227, 3245], [941, 975], [1008, 1034], "[fetchActivities, filters, pagination.page]", [2959, 2972], "[user, token, fetchData]", [3339, 3352], [4670, 4672], "[fetchData]"]