{"ast": null, "code": "const variantPriorityOrder = [\"animate\", \"whileInView\", \"whileFocus\", \"whileHover\", \"whileTap\", \"whileDrag\", \"exit\"];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\nexport { variantPriorityOrder, variantProps };", "map": {"version": 3, "names": ["variantPriorityOrder", "variantProps"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs"], "sourcesContent": ["const variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\nexport { variantPriorityOrder, variantProps };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG,CACzB,SAAS,EACT,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,WAAW,EACX,MAAM,CACT;AACD,MAAMC,YAAY,GAAG,CAAC,SAAS,EAAE,GAAGD,oBAAoB,CAAC;AAEzD,SAASA,oBAAoB,EAAEC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}