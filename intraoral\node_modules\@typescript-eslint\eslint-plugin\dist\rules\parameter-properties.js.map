{"version": 3, "file": "parameter-properties.js", "sourceRoot": "", "sources": ["../../src/rules/parameter-properties.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAsBhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,mBAAmB,EACjB,gEAAgE;YAClE,uBAAuB,EACrB,oEAAoE;SACvE;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR,IAAI,EAAE;wBACJ,UAAU;wBACV,SAAS;wBACT,WAAW;wBACX,QAAQ;wBACR,kBAAkB;wBAClB,oBAAoB;wBACpB,iBAAiB;qBAClB;iBACF;aACF;YACD,WAAW,EAAE;gBACX;oBACE,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,KAAK,EAAE;4BACL,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE;gCACL,IAAI,EAAE,kBAAkB;6BACzB;4BACD,QAAQ,EAAE,CAAC;yBACZ;wBACD,MAAM,EAAE;4BACN,IAAI,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;yBAC/C;qBACF;oBACD,oBAAoB,EAAE,KAAK;iBAC5B;aACF;YACD,IAAI,EAAE,OAAO;SACd;KACF;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,gBAAgB;SACzB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,gBAAgB,EAAE,CAAC;QACzD;;;WAGG;QACH,SAAS,YAAY,CACnB,IAAgE;YAEhE,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACpC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC5B;YAED,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAa,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,KAAK,gBAAgB,EAAE;YAC/B,OAAO;gBACL,mBAAmB,CAAC,IAAI;oBACtB,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;oBAErC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;wBAC9B,0DAA0D;wBAC1D,IACE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BACjD,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACxD;4BACA,OAAO;yBACR;wBAED,MAAM,IAAI,GACR,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAC/C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;4BACrB,CAAC,CAAC,qDAAqD;gCACpD,IAAI,CAAC,SAAS,CAAC,IAA4B,CAAC,IAAI,CAAC;wBAExD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,qBAAqB;4BAChC,IAAI,EAAE;gCACJ,SAAS,EAAE,IAAI;6BAChB;yBACF,CAAC,CAAC;qBACJ;gBACH,CAAC;aACF,CAAC;SACH;QAQD,MAAM,wBAAwB,GAAiC,EAAE,CAAC;QAElE,SAAS,cAAc,CAAC,IAAY;YAClC,MAAM,mBAAmB,GACvB,wBAAwB,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,QAAQ,EAAE;gBACZ,OAAO,QAAQ,CAAC;aACjB;YAED,MAAM,OAAO,GAAkB,EAAE,CAAC;YAClC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACvC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,oBAAoB,CAC3B,aAA0C,EAC1C,oBAAyC;YAEzC,IACE,CAAC,aAAa,CAAC,cAAc;gBAC7B,CAAC,oBAAoB,CAAC,cAAc,EACpC;gBACA,OAAO,CACL,aAAa,CAAC,cAAc,KAAK,oBAAoB,CAAC,cAAc,CACrE,CAAC;aACH;YAED,OAAO,CACL,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC;gBAChD,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,CACxD,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mCAAmC;gBACjC,wBAAwB,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;YAC3C,CAAC;YAED,kDAAkD;gBAChD,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,GAAG,EAAG,CAAC;gBAE5D,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,mBAAmB,EAAE;oBAC/C,IACE,KAAK,CAAC,aAAa;wBACnB,KAAK,CAAC,qBAAqB;wBAC3B,KAAK,CAAC,oBAAoB;wBAC1B,oBAAoB,CAClB,KAAK,CAAC,aAAa,EACnB,KAAK,CAAC,oBAAoB,CAC3B,EACD;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE,IAAI;6BAChB;4BACD,SAAS,EAAE,yBAAyB;4BACpC,IAAI,EAAE,KAAK,CAAC,aAAa;yBAC1B,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;YAED,SAAS,CAAC,IAAI;gBACZ,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;oBAC/B,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;wBAClD,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBAC9C,CAAC,OAAO,CAAC,KAAK;wBACd,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EACtC;wBACA,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC;qBAC1D;iBACF;YACH,CAAC;YAED,sCAAsC,CACpC,IAA+B;;gBAE/B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACzC,IAAI,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;wBAChD,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,oBAAoB,GAAG,SAAS,CAAC;qBACjE;iBACF;gBAED,KAAK,MAAM,SAAS,IAAI,MAAA,MAAA,IAAI,CAAC,KAAK,CAAC,IAAI,0CAAE,IAAI,mCAAI,EAAE,EAAE;oBACnD,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBACrD,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB;wBACjE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;4BAC5B,sBAAc,CAAC,gBAAgB;wBACjC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;4BACnC,sBAAc,CAAC,cAAc;wBAC/B,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;4BACrC,sBAAc,CAAC,UAAU;wBAC3B,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC7D;wBACA,MAAM;qBACP;oBAED,cAAc,CACZ,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAChC,CAAC,qBAAqB,GAAG,SAAS,CAAC,UAAU,CAAC;iBAChD;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}