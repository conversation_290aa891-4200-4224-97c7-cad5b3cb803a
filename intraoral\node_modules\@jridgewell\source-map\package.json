{"name": "@jridgewell/source-map", "version": "0.3.6", "description": "Packages @jridgewell/trace-mapping and @jridgewell/gen-mapping into the familiar source-map API", "keywords": ["sourcemap", "source", "map"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "https://github.com/jridgewell/source-map", "main": "dist/source-map.cjs", "module": "dist/source-map.mjs", "types": "dist/types/source-map.d.ts", "exports": {".": [{"types": "./dist/types/source-map.d.ts", "browser": "./dist/source-map.umd.js", "require": "./dist/source-map.cjs", "import": "./dist/source-map.mjs"}, "./dist/source-map.cjs"], "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"prebuild": "rm -rf dist", "build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "test": "run-s -n test:lint test:only", "test:debug": "ts-mocha --inspect-brk", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "ts-mocha", "test:coverage": "c8 --reporter text --reporter html ts-mocha", "test:watch": "ts-mocha --watch", "prepublishOnly": "npm run preversion", "preversion": "run-s test build"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "10.0.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "ts-mocha": "10.0.0", "typescript": "4.5.5"}, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}