{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\PatientInfo.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaEdit, FaSave, FaTimes, FaUser, FaCalendarAlt, FaPhone, FaHistory } from 'react-icons/fa';\nimport './PatientInfo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientInfo = ({\n  patient\n}) => {\n  _s();\n  const [isEditing, setIsEditing] = useState(false);\n  const [patientData, setPatientData] = useState(patient);\n  const handleEdit = () => {\n    setIsEditing(true);\n  };\n  const handleSave = () => {\n    setIsEditing(false);\n    // In a real app, you would save to backend here\n  };\n  const handleCancel = () => {\n    setPatientData(patient);\n    setIsEditing(false);\n  };\n  const handleInputChange = (field, value) => {\n    setPatientData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const consultationHistory = [{\n    id: 1,\n    date: '2024-01-15',\n    type: 'Regular Checkup',\n    dentist: 'Dr. Smith',\n    status: 'Completed'\n  }, {\n    id: 2,\n    date: '2023-12-10',\n    type: 'Cavity Filling',\n    dentist: 'Dr. Johnson',\n    status: 'Completed'\n  }, {\n    id: 3,\n    date: '2023-11-05',\n    type: 'Cleaning',\n    dentist: 'Dr. Williams',\n    status: 'Completed'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaUser, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Patient Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), !isEditing ? /*#__PURE__*/_jsxDEV(motion.button, {\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        className: \"flex items-center px-4 py-2 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-full font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n        onClick: handleEdit,\n        children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n          className: \"mr-2 h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), \"Edit\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white rounded-full font-medium transition-all duration-300\",\n          onClick: handleSave,\n          children: [/*#__PURE__*/_jsxDEV(FaSave, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), \"Save\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-all duration-300\",\n          onClick: handleCancel,\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), \"Cancel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-500 mb-1\",\n            children: \"Patient ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: patientData.id,\n            onChange: e => handleInputChange('id', e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-900 font-medium\",\n            children: patientData.id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-500 mb-1\",\n            children: \"Full Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: patientData.name,\n            onChange: e => handleInputChange('name', e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-900 font-medium\",\n            children: patientData.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-500 mb-1\",\n            children: \"Age\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: patientData.age,\n            onChange: e => handleInputChange('age', e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-900 font-medium\",\n            children: [patientData.age, \" years\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-500 mb-1\",\n            children: \"Last Visit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: patientData.lastVisit,\n            onChange: e => handleInputChange('lastVisit', e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-900 font-medium\",\n            children: patientData.lastVisit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Current Consultation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center p-4 bg-white rounded-lg border border-green-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-green-800\",\n            children: \"Active Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-green-600\",\n            children: [\"Started: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaHistory, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Consultations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: consultationHistory.map(consultation => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.02\n          },\n          className: \"flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: consultation.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: [consultation.date, \" \\u2022 \", consultation.dentist]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\",\n            children: consultation.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, consultation.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(FaUser, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), \"Medical History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), \"Contact Dentist\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), \"Schedule Visit\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientInfo, \"gPFKxqWEgX0pxuo2cwO+9DzRA2g=\");\n_c = PatientInfo;\nexport default PatientInfo;\nvar _c;\n$RefreshReg$(_c, \"PatientInfo\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaEdit", "FaSave", "FaTimes", "FaUser", "FaCalendarAlt", "FaPhone", "FaHistory", "jsxDEV", "_jsxDEV", "PatientInfo", "patient", "_s", "isEditing", "setIsEditing", "patientData", "setPatientData", "handleEdit", "handleSave", "handleCancel", "handleInputChange", "field", "value", "prev", "consultationHistory", "id", "date", "type", "dentist", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "onClick", "onChange", "e", "target", "name", "age", "lastVisit", "Date", "toLocaleTimeString", "map", "consultation", "div", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/PatientInfo.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaEdit, FaSave, FaTimes, FaUser, FaCalendarAlt, FaPhone, FaHistory } from 'react-icons/fa';\r\nimport './PatientInfo.css';\r\n\r\nconst PatientInfo = ({ patient }) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [patientData, setPatientData] = useState(patient);\r\n\r\n  const handleEdit = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  const handleSave = () => {\r\n    setIsEditing(false);\r\n    // In a real app, you would save to backend here\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setPatientData(patient);\r\n    setIsEditing(false);\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setPatientData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const consultationHistory = [\r\n    {\r\n      id: 1,\r\n      date: '2024-01-15',\r\n      type: 'Regular Checkup',\r\n      dentist: '<PERSON><PERSON> <PERSON>',\r\n      status: 'Completed'\r\n    },\r\n    {\r\n      id: 2,\r\n      date: '2023-12-10',\r\n      type: '<PERSON><PERSON><PERSON>lling',\r\n      dentist: 'Dr<PERSON> <PERSON>',\r\n      status: 'Completed'\r\n    },\r\n    {\r\n      id: 3,\r\n      date: '2023-11-05',\r\n      type: 'Cleaning',\r\n      dentist: 'Dr. Williams',\r\n      status: 'Completed'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Edit Controls */}\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaUser className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Patient Details</h3>\r\n        </div>\r\n        {!isEditing ? (\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center px-4 py-2 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-full font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n            onClick={handleEdit}\r\n          >\r\n            <FaEdit className=\"mr-2 h-4 w-4\" />\r\n            Edit\r\n          </motion.button>\r\n        ) : (\r\n          <div className=\"flex space-x-2\">\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"flex items-center px-4 py-2 bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white rounded-full font-medium transition-all duration-300\"\r\n              onClick={handleSave}\r\n            >\r\n              <FaSave className=\"mr-2 h-4 w-4\" />\r\n              Save\r\n            </motion.button>\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-all duration-300\"\r\n              onClick={handleCancel}\r\n            >\r\n              <FaTimes className=\"mr-2 h-4 w-4\" />\r\n              Cancel\r\n            </motion.button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Patient Information Grid */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-500 mb-1\">Patient ID</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"text\"\r\n                value={patientData.id}\r\n                onChange={(e) => handleInputChange('id', e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-sm text-gray-900 font-medium\">{patientData.id}</p>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-500 mb-1\">Full Name</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"text\"\r\n                value={patientData.name}\r\n                onChange={(e) => handleInputChange('name', e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-sm text-gray-900 font-medium\">{patientData.name}</p>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-500 mb-1\">Age</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"number\"\r\n                value={patientData.age}\r\n                onChange={(e) => handleInputChange('age', e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-sm text-gray-900 font-medium\">{patientData.age} years</p>\r\n            )}\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-500 mb-1\">Last Visit</label>\r\n            {isEditing ? (\r\n              <input\r\n                type=\"date\"\r\n                value={patientData.lastVisit}\r\n                onChange={(e) => handleInputChange('lastVisit', e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-all duration-300\"\r\n              />\r\n            ) : (\r\n              <p className=\"text-sm text-gray-900 font-medium\">{patientData.lastVisit}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Current Consultation Status */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaCalendarAlt className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Current Consultation</h4>\r\n        </div>\r\n        <div className=\"flex items-center p-4 bg-white rounded-lg border border-green-200\">\r\n          <div className=\"w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse\"></div>\r\n          <div>\r\n            <p className=\"text-sm font-medium text-green-800\">Active Session</p>\r\n            <p className=\"text-xs text-green-600\">Started: {new Date().toLocaleTimeString()}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Consultations */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaHistory className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Consultations</h4>\r\n        </div>\r\n        <div className=\"space-y-3\">\r\n          {consultationHistory.map((consultation) => (\r\n            <motion.div\r\n              key={consultation.id}\r\n              whileHover={{ scale: 1.02 }}\r\n              className=\"flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n            >\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-900\">{consultation.type}</p>\r\n                <p className=\"text-xs text-gray-500\">{consultation.date} • {consultation.dentist}</p>\r\n              </div>\r\n              <span className=\"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\r\n                {consultation.status}\r\n              </span>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <h4 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Quick Actions</h4>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-md\"\r\n          >\r\n            <FaUser className=\"mr-2 h-4 w-4\" />\r\n            Medical History\r\n          </motion.button>\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n          >\r\n            <FaPhone className=\"mr-2 h-4 w-4\" />\r\n            Contact Dentist\r\n          </motion.button>\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n          >\r\n            <FaCalendarAlt className=\"mr-2 h-4 w-4\" />\r\n            Schedule Visit\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PatientInfo; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,QAAQ,gBAAgB;AACnG,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAACY,OAAO,CAAC;EAEvD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBH,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBJ,YAAY,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBH,cAAc,CAACL,OAAO,CAAC;IACvBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CN,cAAc,CAACO,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,mBAAmB,GAAG,CAC1B;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,aAAa;IACtBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtB,OAAA;MAAKqB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDtB,OAAA;QAAKqB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtB,OAAA;UAAKqB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EtB,OAAA,CAACL,MAAM;YAAC0B,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACN1B,OAAA;UAAIqB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,EACL,CAACtB,SAAS,gBACTJ,OAAA,CAACT,MAAM,CAACoC,MAAM;QACZC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAC1BR,SAAS,EAAC,oKAAoK;QAC9KU,OAAO,EAAEvB,UAAW;QAAAc,QAAA,gBAEpBtB,OAAA,CAACR,MAAM;UAAC6B,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,QAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,gBAEhB1B,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtB,OAAA,CAACT,MAAM,CAACoC,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,SAAS,EAAC,0IAA0I;UACpJU,OAAO,EAAEtB,UAAW;UAAAa,QAAA,gBAEpBtB,OAAA,CAACP,MAAM;YAAC4B,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,QAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB1B,OAAA,CAACT,MAAM,CAACoC,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,SAAS,EAAC,8HAA8H;UACxIU,OAAO,EAAErB,YAAa;UAAAY,QAAA,gBAEtBtB,OAAA,CAACN,OAAO;YAAC2B,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvDtB,OAAA;QAAKqB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDtB,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAOqB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACjFtB,SAAS,gBACRJ,OAAA;YACEkB,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEP,WAAW,CAACU,EAAG;YACtBgB,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,IAAI,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YACzDQ,SAAS,EAAC;UAAyI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpJ,CAAC,gBAEF1B,OAAA;YAAGqB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEhB,WAAW,CAACU;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAOqB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAChFtB,SAAS,gBACRJ,OAAA;YACEkB,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEP,WAAW,CAAC6B,IAAK;YACxBH,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,MAAM,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAC3DQ,SAAS,EAAC;UAAyI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpJ,CAAC,gBAEF1B,OAAA;YAAGqB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEhB,WAAW,CAAC6B;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACvE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAOqB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC1EtB,SAAS,gBACRJ,OAAA;YACEkB,IAAI,EAAC,QAAQ;YACbL,KAAK,EAAEP,WAAW,CAAC8B,GAAI;YACvBJ,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,KAAK,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAC1DQ,SAAS,EAAC;UAAyI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpJ,CAAC,gBAEF1B,OAAA;YAAGqB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAEhB,WAAW,CAAC8B,GAAG,EAAC,QAAM;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAC5E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAOqB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACjFtB,SAAS,gBACRJ,OAAA;YACEkB,IAAI,EAAC,MAAM;YACXL,KAAK,EAAEP,WAAW,CAAC+B,SAAU;YAC7BL,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,WAAW,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAChEQ,SAAS,EAAC;UAAyI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpJ,CAAC,gBAEF1B,OAAA;YAAGqB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEhB,WAAW,CAAC+B;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDtB,OAAA;QAAKqB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCtB,OAAA;UAAKqB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EtB,OAAA,CAACJ,aAAa;YAACyB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACN1B,OAAA;UAAIqB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACN1B,OAAA;QAAKqB,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAChFtB,OAAA;UAAKqB,SAAS,EAAC;QAAsD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5E1B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAGqB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpE1B,OAAA;YAAGqB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GAAC,WAAS,EAAC,IAAIgB,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDtB,OAAA;QAAKqB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCtB,OAAA;UAAKqB,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EtB,OAAA,CAACF,SAAS;YAACuB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN1B,OAAA;UAAIqB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACN1B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBP,mBAAmB,CAACyB,GAAG,CAAEC,YAAY,iBACpCzC,OAAA,CAACT,MAAM,CAACmD,GAAG;UAETd,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BR,SAAS,EAAC,qIAAqI;UAAAC,QAAA,gBAE/ItB,OAAA;YAAAsB,QAAA,gBACEtB,OAAA;cAAGqB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEmB,YAAY,CAACvB;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE1B,OAAA;cAAGqB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAEmB,YAAY,CAACxB,IAAI,EAAC,UAAG,EAACwB,YAAY,CAACtB,OAAO;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACN1B,OAAA;YAAMqB,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EACrFmB,YAAY,CAACrB;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA,GAVFe,YAAY,CAACzB,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDtB,OAAA;QAAIqB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E1B,OAAA;QAAKqB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDtB,OAAA,CAACT,MAAM,CAACoC,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LtB,OAAA,CAACL,MAAM;YAAC0B,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB1B,OAAA,CAACT,MAAM,CAACoC,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LtB,OAAA,CAACH,OAAO;YAACwB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB1B,OAAA,CAACT,MAAM,CAACoC,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LtB,OAAA,CAACJ,aAAa;YAACyB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAtOIF,WAAW;AAAA0C,EAAA,GAAX1C,WAAW;AAwOjB,eAAeA,WAAW;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}