// Type definitions for @testing-library/jest-dom 5.14
// Project: https://github.com/testing-library/jest-dom
// Definitions by: <PERSON> <https://github.com/gnapse>
//                 <PERSON> <https://github.com/jgoz>
//                 <PERSON> <https://github.com/smacpherson64>
//                 <PERSON> <https://github.com/Andrew<PERSON>eedham>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// Minimum TypeScript Version: 4.3

/// <reference types="jest" />

import { TestingLibraryMatchers } from './matchers';

declare global {
    namespace jest {
        interface Matchers<R = void, T = {}> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
    }
}

declare module 'expect' {
    interface Matchers<R = void, T = unknown> extends TestingLibraryMatchers<typeof expect.stringContaining, R> {}
}
