{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperAnnotateAsPure", "_core", "PURE_CALLS", "Set", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "CallExpression", "path", "isReactCall", "annotateAsPure", "<PERSON><PERSON><PERSON><PERSON>", "get", "isMemberExpression", "module", "methods", "method", "referencesImport", "object", "callee", "node", "computed", "t", "isIdentifier", "property", "propertyName", "has"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\n// Mapping of React top-level methods that are pure.\n// This plugin adds a /*#__PURE__#/ annotation to calls to these methods,\n// so that terser and other minifiers can safely remove them during dead\n// code elimination.\n// See https://reactjs.org/docs/react-api.html\nconst PURE_CALLS: [string, Set<string>][] = [\n  [\n    \"react\",\n    new Set([\n      \"cloneElement\",\n      \"createContext\",\n      \"createElement\",\n      \"createFactory\",\n      \"createRef\",\n      \"forwardRef\",\n      \"isValidElement\",\n      \"memo\",\n      \"lazy\",\n    ]),\n  ],\n  [\"react-dom\", new Set([\"createPortal\"])],\n];\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-react-pure-annotations\",\n    visitor: {\n      CallExpression(path) {\n        if (isReactCall(path)) {\n          annotateAsPure(path);\n        }\n      },\n    },\n  };\n});\n\nfunction isReactCall(path: NodePath<t.CallExpression>) {\n  // If the callee is not a member expression, then check if it matches\n  // a named import, e.g. `import {forwardRef} from 'react'`.\n  const calleePath = path.get(\"callee\");\n  if (!calleePath.isMemberExpression()) {\n    for (const [module, methods] of PURE_CALLS) {\n      for (const method of methods) {\n        if (calleePath.referencesImport(module, method)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  // Otherwise, check if the member expression's object matches\n  // a default import (`import React from 'react'`) or namespace\n  // import (`import * as React from 'react'), and check if the\n  // property matches one of the pure methods.\n  const object = calleePath.get(\"object\");\n  const callee = calleePath.node;\n  if (!callee.computed && t.isIdentifier(callee.property)) {\n    const propertyName = callee.property.name;\n    for (const [module, methods] of PURE_CALLS) {\n      if (\n        object.referencesImport(module, \"default\") ||\n        object.referencesImport(module, \"*\")\n      ) {\n        return methods.has(propertyName);\n      }\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAOA,MAAMG,UAAmC,GAAG,CAC1C,CACE,OAAO,EACP,IAAIC,GAAG,CAAC,CACN,cAAc,EACd,eAAe,EACf,eAAe,EACf,eAAe,EACf,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,MAAM,EACN,MAAM,CACP,CAAC,CACH,EACD,CAAC,WAAW,EAAE,IAAIA,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CACzC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,kCAAkC;IACxCC,OAAO,EAAE;MACPC,cAAcA,CAACC,IAAI,EAAE;QACnB,IAAIC,WAAW,CAACD,IAAI,CAAC,EAAE;UACrB,IAAAE,6BAAc,EAACF,IAAI,CAAC;QACtB;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AAEF,SAASC,WAAWA,CAACD,IAAgC,EAAE;EAGrD,MAAMG,UAAU,GAAGH,IAAI,CAACI,GAAG,CAAC,QAAQ,CAAC;EACrC,IAAI,CAACD,UAAU,CAACE,kBAAkB,CAAC,CAAC,EAAE;IACpC,KAAK,MAAM,CAACC,MAAM,EAAEC,OAAO,CAAC,IAAIlB,UAAU,EAAE;MAC1C,KAAK,MAAMmB,MAAM,IAAID,OAAO,EAAE;QAC5B,IAAIJ,UAAU,CAACM,gBAAgB,CAACH,MAAM,EAAEE,MAAM,CAAC,EAAE;UAC/C,OAAO,IAAI;QACb;MACF;IACF;IAEA,OAAO,KAAK;EACd;EAMA,MAAME,MAAM,GAAGP,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC;EACvC,MAAMO,MAAM,GAAGR,UAAU,CAACS,IAAI;EAC9B,IAAI,CAACD,MAAM,CAACE,QAAQ,IAAIC,WAAC,CAACC,YAAY,CAACJ,MAAM,CAACK,QAAQ,CAAC,EAAE;IACvD,MAAMC,YAAY,GAAGN,MAAM,CAACK,QAAQ,CAACnB,IAAI;IACzC,KAAK,MAAM,CAACS,MAAM,EAAEC,OAAO,CAAC,IAAIlB,UAAU,EAAE;MAC1C,IACEqB,MAAM,CAACD,gBAAgB,CAACH,MAAM,EAAE,SAAS,CAAC,IAC1CI,MAAM,CAACD,gBAAgB,CAACH,MAAM,EAAE,GAAG,CAAC,EACpC;QACA,OAAOC,OAAO,CAACW,GAAG,CAACD,YAAY,CAAC;MAClC;IACF;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}