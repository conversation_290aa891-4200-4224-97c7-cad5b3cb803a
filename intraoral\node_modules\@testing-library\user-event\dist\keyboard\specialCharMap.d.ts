/**
 * @deprecated This list of strings with special meaning is no longer necessary
 * as we've introduced a standardized way to describe any keystroke for `userEvent`.
 * @see https://testing-library.com/docs/ecosystem-user-event#keyboardtext-options
 */
export declare const specialCharMap: {
    readonly arrowLeft: "{arrowleft}";
    readonly arrowRight: "{arrowright}";
    readonly arrowDown: "{arrowdown}";
    readonly arrowUp: "{arrowup}";
    readonly enter: "{enter}";
    readonly escape: "{esc}";
    readonly delete: "{del}";
    readonly backspace: "{backspace}";
    readonly home: "{home}";
    readonly end: "{end}";
    readonly selectAll: "{selectall}";
    readonly space: "{space}";
    readonly whitespace: " ";
    readonly pageUp: "{pageUp}";
    readonly pageDown: "{pageDown}";
};
