{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaCamera, FaTimes } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const [mouthDetected, setMouthDetected] = useState(false);\n  const [analysisStatus, setAnalysisStatus] = useState('idle'); // idle, checking, analyzing, completed, no-mouth\n\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n      className: \"h-6 w-6 text-red-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 20\n    }, this),\n    'early-decay': /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n      className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 20\n    }, this),\n    'healthy tooth': /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      className: \"h-6 w-6 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 22\n    }, this)\n  };\n\n  // Simulate mouth detection validation\n  const validateMouthPresence = useCallback(imageSrc => {\n    return new Promise(resolve => {\n      // Simulate API call to check if mouth is present\n      setTimeout(() => {\n        // For demo purposes, we'll randomly determine if mouth is detected\n        // In real implementation, this would use a mouth detection model\n        const hasMouth = Math.random() > 0.3; // 70% chance of detecting mouth\n        resolve(hasMouth);\n      }, 1000);\n    });\n  }, []);\n  useEffect(() => {\n    // Reset status when image changes\n    if (currentImage) {\n      setAnalysisStatus('checking');\n      setMouthDetected(false);\n      setAnnotatedImage(null);\n\n      // Validate mouth presence\n      validateMouthPresence(currentImage).then(hasMouth => {\n        setMouthDetected(hasMouth);\n        if (!hasMouth) {\n          setAnalysisStatus('no-mouth');\n          onResults([]); // Clear any previous results\n        } else {\n          setAnalysisStatus('idle');\n        }\n      });\n    } else {\n      setAnalysisStatus('idle');\n      setMouthDetected(false);\n      setAnnotatedImage(null);\n    }\n  }, [currentImage, validateMouthPresence, onResults]);\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\n    results.forEach((result, index) => {\n      // Generate random box coordinates for demo\n      const x = Math.random() * (canvasWidth - 100);\n      const y = Math.random() * (canvasHeight - 100);\n      const width = 80 + Math.random() * 40;\n      const height = 80 + Math.random() * 40;\n\n      // Set box color based on class\n      const color = classColors[result.class] || '#0077B6';\n\n      // Draw bounding box\n      ctx.strokeStyle = color;\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\n      ctx.font = '14px Arial';\n      const textWidth = ctx.measureText(label).width;\n      ctx.fillStyle = color;\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.fillText(label, x + 5, y - 10);\n    });\n  }, [classColors]);\n  const generateAnnotatedImage = useCallback(results => {\n    // Create a canvas to draw the annotated image\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = 640;\n    canvas.height = 480;\n\n    // If we have a current image, use it as background\n    if (currentImage) {\n      const img = new Image();\n      img.onload = () => {\n        // Draw the original image\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n        // Draw detection boxes on top\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n\n        // Set the annotated image\n        setAnnotatedImage(canvas.toDataURL());\n      };\n      img.src = currentImage;\n    } else {\n      // Fallback to placeholder if no image\n      ctx.fillStyle = '#f5f5f5';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Draw detection boxes\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n      setAnnotatedImage(canvas.toDataURL());\n    }\n  }, [currentImage, drawDetectionBoxes]);\n  const handleDetectionResults = useCallback(results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\n  useEffect(() => {\n    // Only run detection if mouth is detected and analysis is requested\n    if (isAnalyzing && mouthDetected && currentImage) {\n      setAnalysisStatus('analyzing');\n      const mockResults = generateMockResults();\n      setTimeout(() => {\n        handleDetectionResults(mockResults);\n        setAnalysisStatus('completed');\n      }, 2000);\n    } else if (isAnalyzing && !mouthDetected) {\n      // If analysis is requested but no mouth detected, show error\n      setAnalysisStatus('no-mouth');\n      onResults([]); // Clear results\n    }\n  }, [isAnalyzing, mouthDetected, currentImage, handleDetectionResults, onResults]);\n  const generateMockResults = () => {\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\n    const numDetections = Math.floor(Math.random() * 3) + 1;\n    const results = [];\n    for (let i = 0; i < numDetections; i++) {\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\n      results.push({\n        class: randomClass,\n        confidence: Math.random() * 0.4 + 0.6,\n        // 60-100% confidence\n        bbox: {\n          x: Math.random() * 0.8,\n          y: Math.random() * 0.8,\n          width: Math.random() * 0.3 + 0.1,\n          height: Math.random() * 0.3 + 0.1\n        }\n      });\n    }\n    return results;\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  const getStatusMessage = () => {\n    switch (analysisStatus) {\n      case 'checking':\n        return 'Checking for mouth presence...';\n      case 'analyzing':\n        return 'Analyzing dental conditions...';\n      case 'no-mouth':\n        return 'No mouth detected in image';\n      case 'completed':\n        return 'Analysis completed';\n      default:\n        return 'Ready for analysis';\n    }\n  };\n  const getStatusIcon = () => {\n    switch (analysisStatus) {\n      case 'checking':\n        return /*#__PURE__*/_jsxDEV(FaCamera, {\n          className: \"h-5 w-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 16\n        }, this);\n      case 'analyzing':\n        return /*#__PURE__*/_jsxDEV(FaBrain, {\n          className: \"h-5 w-5 text-[#0077B6]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 16\n        }, this);\n      case 'no-mouth':\n        return /*#__PURE__*/_jsxDEV(FaTimes, {\n          className: \"h-5 w-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-5 w-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaEye, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaBrain, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), \"Model: best.pt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [getStatusIcon(), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"ml-3 text-[#0077B6] font-medium\",\n            children: getStatusMessage()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [mouthDetected && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), \"Mouth Detected\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), !mouthDetected && currentImage && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n              className: \"inline mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), \"No Mouth\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), analysisStatus === 'analyzing' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Analyzing image with YOLOv8...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), analysisStatus === 'no-mouth' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"h-5 w-5 text-red-500 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 font-medium\",\n              children: \"No mouth detected in the image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-600 text-sm\",\n              children: \"Please ensure the patient's mouth is clearly visible in the camera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), annotatedImage && mouthDetected && analysisStatus === 'completed' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Annotated Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\",\n          className: \"w-full h-auto max-h-64 object-contain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this), mouthDetected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Detection Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mr-3\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 17\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), mouthDetected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaClock, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Detections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 max-h-64 overflow-y-auto\",\n        children: detectionHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(FaEye, {\n            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 font-medium mb-2\",\n            children: \"No detections yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"Start analysis to begin detection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 15\n        }, this) : detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [detection.results.length, \" detection(s)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: new Date(detection.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: detection.results.slice(0, 3).map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [classIcons[result.class], /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 text-xs font-medium text-gray-600\",\n                  children: [(result.confidence * 100).toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 27\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 19\n          }, this)\n        }, detection.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"XSVXHibudNdu1pLnphnTl7Fkgu8=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "FaBrain", "FaChartBar", "FaClock", "FaImage", "FaRobot", "FaExclamationTriangle", "FaCheckCircle", "FaTimesCircle", "FaCalendarAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaEye", "FaCamera", "FaTimes", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "mouthDetected", "setMouthDetected", "analysisStatus", "setAnalysisStatus", "classColors", "classIcons", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "validateMouthPresence", "imageSrc", "Promise", "resolve", "setTimeout", "hasMouth", "Math", "random", "then", "drawDetectionBoxes", "ctx", "results", "canvasWidth", "canvasHeight", "for<PERSON>ach", "result", "index", "x", "y", "width", "height", "color", "class", "strokeStyle", "lineWidth", "strokeRect", "label", "confidence", "toFixed", "font", "textWidth", "measureText", "fillStyle", "fillRect", "fillText", "generateAnnotatedImage", "canvas", "document", "createElement", "getContext", "img", "Image", "onload", "drawImage", "toDataURL", "src", "handleDetectionResults", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "mockResults", "generateMockResults", "classes", "numDetections", "floor", "i", "randomClass", "length", "push", "bbox", "getConfidenceColor", "getStatusMessage", "getStatusIcon", "children", "div", "initial", "opacity", "scale", "animate", "alt", "Object", "entries", "map", "count", "whileHover", "replace", "detection", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaCamera, FaTimes } from 'react-icons/fa';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n  const [mouthDetected, setMouthDetected] = useState(false);\r\n  const [analysisStatus, setAnalysisStatus] = useState('idle'); // idle, checking, analyzing, completed, no-mouth\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ff6b6b',\r\n    'early-decay': '#ffd43b',\r\n    'healthy tooth': '#51cf66'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': <FaTimesCircle className=\"h-6 w-6 text-red-500\" />,\r\n    'early-decay': <FaExclamationTriangle className=\"h-6 w-6 text-yellow-500\" />,\r\n    'healthy tooth': <FaCheckCircle className=\"h-6 w-6 text-green-500\" />\r\n  };\r\n\r\n  // Simulate mouth detection validation\r\n  const validateMouthPresence = useCallback((imageSrc) => {\r\n    return new Promise((resolve) => {\r\n      // Simulate API call to check if mouth is present\r\n      setTimeout(() => {\r\n        // For demo purposes, we'll randomly determine if mouth is detected\r\n        // In real implementation, this would use a mouth detection model\r\n        const hasMouth = Math.random() > 0.3; // 70% chance of detecting mouth\r\n        resolve(hasMouth);\r\n      }, 1000);\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Reset status when image changes\r\n    if (currentImage) {\r\n      setAnalysisStatus('checking');\r\n      setMouthDetected(false);\r\n      setAnnotatedImage(null);\r\n      \r\n      // Validate mouth presence\r\n      validateMouthPresence(currentImage).then((hasMouth) => {\r\n        setMouthDetected(hasMouth);\r\n        if (!hasMouth) {\r\n          setAnalysisStatus('no-mouth');\r\n          onResults([]); // Clear any previous results\r\n        } else {\r\n          setAnalysisStatus('idle');\r\n        }\r\n      });\r\n    } else {\r\n      setAnalysisStatus('idle');\r\n      setMouthDetected(false);\r\n      setAnnotatedImage(null);\r\n    }\r\n  }, [currentImage, validateMouthPresence, onResults]);\r\n\r\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\r\n    results.forEach((result, index) => {\r\n      // Generate random box coordinates for demo\r\n      const x = Math.random() * (canvasWidth - 100);\r\n      const y = Math.random() * (canvasHeight - 100);\r\n      const width = 80 + Math.random() * 40;\r\n      const height = 80 + Math.random() * 40;\r\n\r\n      // Set box color based on class\r\n      const color = classColors[result.class] || '#0077B6';\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = color;\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\r\n      ctx.font = '14px Arial';\r\n      const textWidth = ctx.measureText(label).width;\r\n\r\n      ctx.fillStyle = color;\r\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.fillText(label, x + 5, y - 10);\r\n    });\r\n  }, [classColors]);\r\n\r\n  const generateAnnotatedImage = useCallback((results) => {\r\n    // Create a canvas to draw the annotated image\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n\r\n    // Set canvas size\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n\r\n    // If we have a current image, use it as background\r\n    if (currentImage) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        // Draw the original image\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n\r\n        // Draw detection boxes on top\r\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n        // Set the annotated image\r\n        setAnnotatedImage(canvas.toDataURL());\r\n      };\r\n      img.src = currentImage;\r\n    } else {\r\n      // Fallback to placeholder if no image\r\n      ctx.fillStyle = '#f5f5f5';\r\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n      // Draw detection boxes\r\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n      setAnnotatedImage(canvas.toDataURL());\r\n    }\r\n  }, [currentImage, drawDetectionBoxes]);\r\n\r\n  const handleDetectionResults = useCallback((results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n\r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\r\n\r\n  useEffect(() => {\r\n    // Only run detection if mouth is detected and analysis is requested\r\n    if (isAnalyzing && mouthDetected && currentImage) {\r\n      setAnalysisStatus('analyzing');\r\n      const mockResults = generateMockResults();\r\n      setTimeout(() => {\r\n        handleDetectionResults(mockResults);\r\n        setAnalysisStatus('completed');\r\n      }, 2000);\r\n    } else if (isAnalyzing && !mouthDetected) {\r\n      // If analysis is requested but no mouth detected, show error\r\n      setAnalysisStatus('no-mouth');\r\n      onResults([]); // Clear results\r\n    }\r\n  }, [isAnalyzing, mouthDetected, currentImage, handleDetectionResults, onResults]);\r\n\r\n  const generateMockResults = () => {\r\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\r\n    const numDetections = Math.floor(Math.random() * 3) + 1;\r\n    const results = [];\r\n\r\n    for (let i = 0; i < numDetections; i++) {\r\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\r\n      results.push({\r\n        class: randomClass,\r\n        confidence: Math.random() * 0.4 + 0.6, // 60-100% confidence\r\n        bbox: {\r\n          x: Math.random() * 0.8,\r\n          y: Math.random() * 0.8,\r\n          width: Math.random() * 0.3 + 0.1,\r\n          height: Math.random() * 0.3 + 0.1\r\n        }\r\n      });\r\n    }\r\n\r\n    return results;\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#51cf66';\r\n    if (confidence >= 0.6) return '#ffd43b';\r\n    return '#ff6b6b';\r\n  };\r\n\r\n  const getStatusMessage = () => {\r\n    switch (analysisStatus) {\r\n      case 'checking':\r\n        return 'Checking for mouth presence...';\r\n      case 'analyzing':\r\n        return 'Analyzing dental conditions...';\r\n      case 'no-mouth':\r\n        return 'No mouth detected in image';\r\n      case 'completed':\r\n        return 'Analysis completed';\r\n      default:\r\n        return 'Ready for analysis';\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = () => {\r\n    switch (analysisStatus) {\r\n      case 'checking':\r\n        return <FaCamera className=\"h-5 w-5 text-blue-500\" />;\r\n      case 'analyzing':\r\n        return <FaBrain className=\"h-5 w-5 text-[#0077B6]\" />;\r\n      case 'no-mouth':\r\n        return <FaTimes className=\"h-5 w-5 text-red-500\" />;\r\n      case 'completed':\r\n        return <FaCheckCircle className=\"h-5 w-5 text-green-500\" />;\r\n      default:\r\n        return <FaEye className=\"h-5 w-5 text-gray-500\" />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Model Info */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaBrain className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">AI Analysis</h3>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n            <FaRobot className=\"inline mr-1\" />\r\n            Model: best.pt\r\n          </span>\r\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\r\n            Classes: 3\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Analysis Status */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center\">\r\n            {getStatusIcon()}\r\n            <p className=\"ml-3 text-[#0077B6] font-medium\">{getStatusMessage()}</p>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            {mouthDetected && (\r\n              <span className=\"px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\">\r\n                <FaCheckCircle className=\"inline mr-1\" />\r\n                Mouth Detected\r\n              </span>\r\n            )}\r\n            {!mouthDetected && currentImage && (\r\n              <span className=\"px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium\">\r\n                <FaTimes className=\"inline mr-1\" />\r\n                No Mouth\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Loading spinner for analyzing state */}\r\n        {analysisStatus === 'analyzing' && (\r\n          <div className=\"mt-4 flex items-center justify-center\">\r\n            <div className=\"loading-spinner mr-3\"></div>\r\n            <p className=\"text-[#0077B6] font-medium\">Analyzing image with YOLOv8...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error message for no mouth detected */}\r\n        {analysisStatus === 'no-mouth' && (\r\n          <div className=\"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\r\n            <div className=\"flex items-center\">\r\n              <FaTimes className=\"h-5 w-5 text-red-500 mr-3\" />\r\n              <div>\r\n                <p className=\"text-red-700 font-medium\">No mouth detected in the image</p>\r\n                <p className=\"text-red-600 text-sm\">Please ensure the patient's mouth is clearly visible in the camera</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Annotated Image - Only show if mouth detected and analysis completed */}\r\n      {annotatedImage && mouthDetected && analysisStatus === 'completed' && (\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\"\r\n        >\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaImage className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Annotated Results</h4>\r\n          </div>\r\n          <div className=\"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\">\r\n            <img\r\n              src={annotatedImage}\r\n              alt=\"Annotated detection\"\r\n              className=\"w-full h-auto max-h-64 object-contain\"\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Detection Statistics - Only show if mouth detected */}\r\n      {mouthDetected && (\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaChartBar className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Detection Statistics</h4>\r\n          </div>\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n            {Object.entries(detectionStats).map(([className, count]) => (\r\n              <motion.div\r\n                key={className}\r\n                whileHover={{ scale: 1.05 }}\r\n                className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"mr-3\">{classIcons[className]}</div>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-700 capitalize\">{className.replace('-', ' ')}</p>\r\n                    <p className=\"text-2xl font-bold text-[#0077B6]\">{count}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Detection History - Only show if mouth detected */}\r\n      {mouthDetected && (\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaClock className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Detections</h4>\r\n          </div>\r\n\r\n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n            {detectionHistory.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <FaEye className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                <p className=\"text-gray-600 font-medium mb-2\">No detections yet</p>\r\n                <p className=\"text-gray-500 text-sm\">Start analysis to begin detection</p>\r\n              </div>\r\n            ) : (\r\n              detectionHistory.map((detection) => (\r\n                <motion.div\r\n                  key={detection.id}\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaTooth className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-900\">\r\n                          {detection.results.length} detection(s)\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {new Date(detection.timestamp).toLocaleTimeString()}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {detection.results.slice(0, 3).map((result, index) => (\r\n                        <div key={index} className=\"flex items-center\">\r\n                          {classIcons[result.class]}\r\n                          <span className=\"ml-1 text-xs font-medium text-gray-600\">\r\n                            {(result.confidence * 100).toFixed(0)}%\r\n                          </span>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,gBAAgB;AACtL,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC;IACnD8B,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;;EAE9D,MAAMmC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,eAAElB,OAAA,CAACP,aAAa;MAAC0B,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjE,aAAa,eAAEvB,OAAA,CAACT,qBAAqB;MAAC4B,SAAS,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5E,eAAe,eAAEvB,OAAA,CAACR,aAAa;MAAC2B,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtE,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGxC,WAAW,CAAEyC,QAAQ,IAAK;IACtD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9B;MACAC,UAAU,CAAC,MAAM;QACf;QACA;QACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACtCJ,OAAO,CAACE,QAAQ,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN9C,SAAS,CAAC,MAAM;IACd;IACA,IAAIqB,YAAY,EAAE;MAChBY,iBAAiB,CAAC,UAAU,CAAC;MAC7BF,gBAAgB,CAAC,KAAK,CAAC;MACvBL,iBAAiB,CAAC,IAAI,CAAC;;MAEvB;MACAe,qBAAqB,CAACpB,YAAY,CAAC,CAAC4B,IAAI,CAAEH,QAAQ,IAAK;QACrDf,gBAAgB,CAACe,QAAQ,CAAC;QAC1B,IAAI,CAACA,QAAQ,EAAE;UACbb,iBAAiB,CAAC,UAAU,CAAC;UAC7Bd,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACLc,iBAAiB,CAAC,MAAM,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,iBAAiB,CAAC,MAAM,CAAC;MACzBF,gBAAgB,CAAC,KAAK,CAAC;MACvBL,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC,EAAE,CAACL,YAAY,EAAEoB,qBAAqB,EAAEtB,SAAS,CAAC,CAAC;EAEpD,MAAM+B,kBAAkB,GAAGjD,WAAW,CAAC,CAACkD,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,KAAK;IAClFF,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACjC;MACA,MAAMC,CAAC,GAAGX,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIK,WAAW,GAAG,GAAG,CAAC;MAC7C,MAAMM,CAAC,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIM,YAAY,GAAG,GAAG,CAAC;MAC9C,MAAMM,KAAK,GAAG,EAAE,GAAGb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;MACrC,MAAMa,MAAM,GAAG,EAAE,GAAGd,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;;MAEtC;MACA,MAAMc,KAAK,GAAG5B,WAAW,CAACsB,MAAM,CAACO,KAAK,CAAC,IAAI,SAAS;;MAEpD;MACAZ,GAAG,CAACa,WAAW,GAAGF,KAAK;MACvBX,GAAG,CAACc,SAAS,GAAG,CAAC;MACjBd,GAAG,CAACe,UAAU,CAACR,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMM,KAAK,GAAG,GAAGX,MAAM,CAACO,KAAK,KAAK,CAACP,MAAM,CAACY,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;MAC1ElB,GAAG,CAACmB,IAAI,GAAG,YAAY;MACvB,MAAMC,SAAS,GAAGpB,GAAG,CAACqB,WAAW,CAACL,KAAK,CAAC,CAACP,KAAK;MAE9CT,GAAG,CAACsB,SAAS,GAAGX,KAAK;MACrBX,GAAG,CAACuB,QAAQ,CAAChB,CAAC,EAAEC,CAAC,GAAG,EAAE,EAAEY,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC;;MAE3C;MACApB,GAAG,CAACsB,SAAS,GAAG,OAAO;MACvBtB,GAAG,CAACwB,QAAQ,CAACR,KAAK,EAAET,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,EAAE,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,WAAW,CAAC,CAAC;EAEjB,MAAM0C,sBAAsB,GAAG3E,WAAW,CAAEmD,OAAO,IAAK;IACtD;IACA,MAAMyB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAM5B,GAAG,GAAG0B,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAH,MAAM,CAACjB,KAAK,GAAG,GAAG;IAClBiB,MAAM,CAAChB,MAAM,GAAG,GAAG;;IAEnB;IACA,IAAIxC,YAAY,EAAE;MAChB,MAAM4D,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB;QACAhC,GAAG,CAACiC,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;QAErD;QACAX,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAEyB,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;QAE7D;QACAnC,iBAAiB,CAACmD,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;MACvC,CAAC;MACDJ,GAAG,CAACK,GAAG,GAAGjE,YAAY;IACxB,CAAC,MAAM;MACL;MACA8B,GAAG,CAACsB,SAAS,GAAG,SAAS;MACzBtB,GAAG,CAACuB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEG,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;MAE/C;MACAX,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAEyB,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;MAE7DnC,iBAAiB,CAACmD,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAAChE,YAAY,EAAE6B,kBAAkB,CAAC,CAAC;EAEtC,MAAMqC,sBAAsB,GAAGtF,WAAW,CAAEmD,OAAO,IAAK;IACtD,MAAMoC,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCzC,OAAO,EAAEA,OAAO;MAChB0C,KAAK,EAAEzE,YAAY,IAAI;IACzB,CAAC;IAEDG,mBAAmB,CAACuE,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAGtE;IAAe,CAAC;IACtCyB,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;MACxByC,QAAQ,CAACzC,MAAM,CAACO,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFnC,iBAAiB,CAACqE,QAAQ,CAAC;;IAE3B;IACArB,sBAAsB,CAACxB,OAAO,CAAC;;IAE/B;IACAjC,SAAS,CAACiC,OAAO,CAAC;EACpB,CAAC,EAAE,CAAC/B,YAAY,EAAEM,cAAc,EAAER,SAAS,EAAEyD,sBAAsB,CAAC,CAAC;EAErE5E,SAAS,CAAC,MAAM;IACd;IACA,IAAIoB,WAAW,IAAIU,aAAa,IAAIT,YAAY,EAAE;MAChDY,iBAAiB,CAAC,WAAW,CAAC;MAC9B,MAAMiE,WAAW,GAAGC,mBAAmB,CAAC,CAAC;MACzCtD,UAAU,CAAC,MAAM;QACf0C,sBAAsB,CAACW,WAAW,CAAC;QACnCjE,iBAAiB,CAAC,WAAW,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM,IAAIb,WAAW,IAAI,CAACU,aAAa,EAAE;MACxC;MACAG,iBAAiB,CAAC,UAAU,CAAC;MAC7Bd,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACC,WAAW,EAAEU,aAAa,EAAET,YAAY,EAAEkE,sBAAsB,EAAEpE,SAAS,CAAC,CAAC;EAEjF,MAAMgF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAGtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,MAAMI,OAAO,GAAG,EAAE;IAElB,KAAK,IAAImD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,EAAE,EAAE;MACtC,MAAMC,WAAW,GAAGJ,OAAO,CAACrD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGoD,OAAO,CAACK,MAAM,CAAC,CAAC;MACvErD,OAAO,CAACsD,IAAI,CAAC;QACX3C,KAAK,EAAEyC,WAAW;QAClBpC,UAAU,EAAErB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAAE;QACvC2D,IAAI,EAAE;UACJjD,CAAC,EAAEX,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBW,CAAC,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCa,MAAM,EAAEd,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOI,OAAO;EAChB,CAAC;EAED,MAAMwD,kBAAkB,GAAIxC,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ7E,cAAc;MACpB,KAAK,UAAU;QACb,OAAO,gCAAgC;MACzC,KAAK,WAAW;QACd,OAAO,gCAAgC;MACzC,KAAK,UAAU;QACb,OAAO,4BAA4B;MACrC,KAAK,WAAW;QACd,OAAO,oBAAoB;MAC7B;QACE,OAAO,oBAAoB;IAC/B;EACF,CAAC;EAED,MAAM8E,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQ9E,cAAc;MACpB,KAAK,UAAU;QACb,oBAAOf,OAAA,CAACH,QAAQ;UAACsB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,WAAW;QACd,oBAAOvB,OAAA,CAACd,OAAO;UAACiC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,UAAU;QACb,oBAAOvB,OAAA,CAACF,OAAO;UAACqB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,WAAW;QACd,oBAAOvB,OAAA,CAACR,aAAa;UAAC2B,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D;QACE,oBAAOvB,OAAA,CAACJ,KAAK;UAACuB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtD;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKmB,SAAS,EAAC,WAAW;IAAA2E,QAAA,gBAExB9F,OAAA;MAAKmB,SAAS,EAAC,6EAA6E;MAAA2E,QAAA,gBAC1F9F,OAAA;QAAKmB,SAAS,EAAC,mBAAmB;QAAA2E,QAAA,gBAChC9F,OAAA;UAAKmB,SAAS,EAAC,6DAA6D;UAAA2E,QAAA,eAC1E9F,OAAA,CAACd,OAAO;YAACiC,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNvB,OAAA;UAAImB,SAAS,EAAC,sCAAsC;UAAA2E,QAAA,EAAC;QAAW;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNvB,OAAA;QAAKmB,SAAS,EAAC,YAAY;QAAA2E,QAAA,gBACzB9F,OAAA;UAAMmB,SAAS,EAAC,oFAAoF;UAAA2E,QAAA,gBAClG9F,OAAA,CAACV,OAAO;YAAC6B,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPvB,OAAA;UAAMmB,SAAS,EAAC,qFAAqF;UAAA2E,QAAA,EAAC;QAEtG;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKmB,SAAS,EAAC,oFAAoF;MAAA2E,QAAA,gBACjG9F,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAA2E,QAAA,gBAChD9F,OAAA;UAAKmB,SAAS,EAAC,mBAAmB;UAAA2E,QAAA,GAC/BD,aAAa,CAAC,CAAC,eAChB7F,OAAA;YAAGmB,SAAS,EAAC,iCAAiC;YAAA2E,QAAA,EAAEF,gBAAgB,CAAC;UAAC;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNvB,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAA2E,QAAA,GACzCjF,aAAa,iBACZb,OAAA;YAAMmB,SAAS,EAAC,wEAAwE;YAAA2E,QAAA,gBACtF9F,OAAA,CAACR,aAAa;cAAC2B,SAAS,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACA,CAACV,aAAa,IAAIT,YAAY,iBAC7BJ,OAAA;YAAMmB,SAAS,EAAC,oEAAoE;YAAA2E,QAAA,gBAClF9F,OAAA,CAACF,OAAO;cAACqB,SAAS,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLR,cAAc,KAAK,WAAW,iBAC7Bf,OAAA;QAAKmB,SAAS,EAAC,uCAAuC;QAAA2E,QAAA,gBACpD9F,OAAA;UAAKmB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CvB,OAAA;UAAGmB,SAAS,EAAC,4BAA4B;UAAA2E,QAAA,EAAC;QAA8B;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CACN,EAGAR,cAAc,KAAK,UAAU,iBAC5Bf,OAAA;QAAKmB,SAAS,EAAC,qDAAqD;QAAA2E,QAAA,eAClE9F,OAAA;UAAKmB,SAAS,EAAC,mBAAmB;UAAA2E,QAAA,gBAChC9F,OAAA,CAACF,OAAO;YAACqB,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDvB,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAGmB,SAAS,EAAC,0BAA0B;cAAA2E,QAAA,EAAC;YAA8B;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1EvB,OAAA;cAAGmB,SAAS,EAAC,sBAAsB;cAAA2E,QAAA,EAAC;YAAkE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLf,cAAc,IAAIK,aAAa,IAAIE,cAAc,KAAK,WAAW,iBAChEf,OAAA,CAACf,MAAM,CAAC8G,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAE;MACrCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;MAClC/E,SAAS,EAAC,0CAA0C;MAAA2E,QAAA,gBAEpD9F,OAAA;QAAKmB,SAAS,EAAC,wBAAwB;QAAA2E,QAAA,gBACrC9F,OAAA;UAAKmB,SAAS,EAAC,6DAA6D;UAAA2E,QAAA,eAC1E9F,OAAA,CAACX,OAAO;YAAC8B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNvB,OAAA;UAAImB,SAAS,EAAC,sCAAsC;UAAA2E,QAAA,EAAC;QAAiB;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNvB,OAAA;QAAKmB,SAAS,EAAC,sEAAsE;QAAA2E,QAAA,eACnF9F,OAAA;UACEqE,GAAG,EAAE7D,cAAe;UACpB4F,GAAG,EAAC,qBAAqB;UACzBjF,SAAS,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGAV,aAAa,iBACZb,OAAA;MAAKmB,SAAS,EAAC,0CAA0C;MAAA2E,QAAA,gBACvD9F,OAAA;QAAKmB,SAAS,EAAC,wBAAwB;QAAA2E,QAAA,gBACrC9F,OAAA;UAAKmB,SAAS,EAAC,6DAA6D;UAAA2E,QAAA,eAC1E9F,OAAA,CAACb,UAAU;YAACgC,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNvB,OAAA;UAAImB,SAAS,EAAC,sCAAsC;UAAA2E,QAAA,EAAC;QAAoB;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNvB,OAAA;QAAKmB,SAAS,EAAC,uCAAuC;QAAA2E,QAAA,EACnDO,MAAM,CAACC,OAAO,CAAC5F,cAAc,CAAC,CAAC6F,GAAG,CAAC,CAAC,CAACpF,SAAS,EAAEqF,KAAK,CAAC,kBACrDxG,OAAA,CAACf,MAAM,CAAC8G,GAAG;UAETU,UAAU,EAAE;YAAEP,KAAK,EAAE;UAAK,CAAE;UAC5B/E,SAAS,EAAC,6GAA6G;UAAA2E,QAAA,eAEvH9F,OAAA;YAAKmB,SAAS,EAAC,mBAAmB;YAAA2E,QAAA,gBAChC9F,OAAA;cAAKmB,SAAS,EAAC,MAAM;cAAA2E,QAAA,EAAE5E,UAAU,CAACC,SAAS;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDvB,OAAA;cAAA8F,QAAA,gBACE9F,OAAA;gBAAGmB,SAAS,EAAC,8CAA8C;gBAAA2E,QAAA,EAAE3E,SAAS,CAACuF,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FvB,OAAA;gBAAGmB,SAAS,EAAC,mCAAmC;gBAAA2E,QAAA,EAAEU;cAAK;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAVDJ,SAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAV,aAAa,iBACZb,OAAA;MAAKmB,SAAS,EAAC,0CAA0C;MAAA2E,QAAA,gBACvD9F,OAAA;QAAKmB,SAAS,EAAC,wBAAwB;QAAA2E,QAAA,gBACrC9F,OAAA;UAAKmB,SAAS,EAAC,6DAA6D;UAAA2E,QAAA,eAC1E9F,OAAA,CAACZ,OAAO;YAAC+B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNvB,OAAA;UAAImB,SAAS,EAAC,sCAAsC;UAAA2E,QAAA,EAAC;QAAiB;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAENvB,OAAA;QAAKmB,SAAS,EAAC,oCAAoC;QAAA2E,QAAA,EAChDxF,gBAAgB,CAACkF,MAAM,KAAK,CAAC,gBAC5BxF,OAAA;UAAKmB,SAAS,EAAC,kBAAkB;UAAA2E,QAAA,gBAC/B9F,OAAA,CAACJ,KAAK;YAACuB,SAAS,EAAC;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DvB,OAAA;YAAGmB,SAAS,EAAC,gCAAgC;YAAA2E,QAAA,EAAC;UAAiB;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnEvB,OAAA;YAAGmB,SAAS,EAAC,uBAAuB;YAAA2E,QAAA,EAAC;UAAiC;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,GAENjB,gBAAgB,CAACiG,GAAG,CAAEI,SAAS,iBAC7B3G,OAAA,CAACf,MAAM,CAAC8G,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAExD,CAAC,EAAE,CAAC;UAAG,CAAE;UAChC0D,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAExD,CAAC,EAAE;UAAE,CAAE;UAC9BtB,SAAS,EAAC,6GAA6G;UAAA2E,QAAA,eAEvH9F,OAAA;YAAKmB,SAAS,EAAC,mCAAmC;YAAA2E,QAAA,gBAChD9F,OAAA;cAAKmB,SAAS,EAAC,mBAAmB;cAAA2E,QAAA,gBAChC9F,OAAA;gBAAKmB,SAAS,EAAC,6DAA6D;gBAAA2E,QAAA,eAC1E9F,OAAA,CAACL,OAAO;kBAACwB,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNvB,OAAA;gBAAA8F,QAAA,gBACE9F,OAAA;kBAAGmB,SAAS,EAAC,mCAAmC;kBAAA2E,QAAA,GAC7Ca,SAAS,CAACxE,OAAO,CAACqD,MAAM,EAAC,eAC5B;gBAAA;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJvB,OAAA;kBAAGmB,SAAS,EAAC,uBAAuB;kBAAA2E,QAAA,EACjC,IAAIrB,IAAI,CAACkC,SAAS,CAAChC,SAAS,CAAC,CAACiC,kBAAkB,CAAC;gBAAC;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA;cAAKmB,SAAS,EAAC,6BAA6B;cAAA2E,QAAA,EACzCa,SAAS,CAACxE,OAAO,CAAC4C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACwB,GAAG,CAAC,CAAChE,MAAM,EAAEC,KAAK,kBAC/CxC,OAAA;gBAAiBmB,SAAS,EAAC,mBAAmB;gBAAA2E,QAAA,GAC3C5E,UAAU,CAACqB,MAAM,CAACO,KAAK,CAAC,eACzB9C,OAAA;kBAAMmB,SAAS,EAAC,wCAAwC;kBAAA2E,QAAA,GACrD,CAACvD,MAAM,CAACY,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAJCiB,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA7BDoF,SAAS,CAACnC,EAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BP,CACb;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CA/YIJ,aAAa;AAAA4G,EAAA,GAAb5G,aAAa;AAiZnB,eAAeA,aAAa;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}