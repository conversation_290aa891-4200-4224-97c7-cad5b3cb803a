{"name": "@typescript-eslint/visitor-keys", "version": "5.62.0", "description": "Visitor keys used to help traverse the TypeScript-ESTree AST", "keywords": ["eslint", "typescript", "estree"], "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["dist", "_ts3.4", "package.json", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/visitor-keys"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts3.4/dist", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts3.4 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "nx lint", "test": "jest --coverage", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"@types/eslint-visitor-keys": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<3.8": {"*": ["_ts3.4/*"]}}, "gitHead": "cba0d113bba1bbcee69149c954dc6bd4c658c714"}