{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\Sidebar.jsx\";\nimport React from 'react';\nimport { FaHome, FaVideo, FaUserAlt, FaChartLine, FaCog, Fa<PERSON>ooth } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isOpen,\n  setIsOpen\n}) => {\n  const navItems = [{\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 32\n    }, this),\n    path: '/'\n  }, {\n    name: 'Video Call',\n    icon: /*#__PURE__*/_jsxDEV(FaVideo, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 33\n    }, this),\n    path: '/video'\n  }, {\n    name: 'Patients',\n    icon: /*#__PURE__*/_jsxDEV(FaUserAlt, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 31\n    }, this),\n    path: '/patients'\n  }, {\n    name: 'Analytics',\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 32\n    }, this),\n    path: '/analytics'\n  }, {\n    name: 'Settings',\n    icon: /*#__PURE__*/_jsxDEV(FaCog, {\n      className: \"h-5 w-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 31\n    }, this),\n    path: '/settings'\n  }];\n  const user = {\n    name: 'Dentist',\n    role: 'dentist'\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\",\n      onClick: () => setIsOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 flex items-center justify-center\",\n        children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaTooth, {\n            className: \"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-xl font-bold text-[#0077B6]\",\n            children: \"Intraoral\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), !isOpen && /*#__PURE__*/_jsxDEV(FaTooth, {\n          className: \"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"flex-1 px-2 py-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full flex items-center p-3 text-[#333333] hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-lg transition-colors group\",\n              onClick: () => window.innerWidth < 768 && setIsOpen(false),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-[#333333] group-hover:text-[#0077B6]\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this), isOpen && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-[rgba(0,119,182,0.1)]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-[#333333]\",\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-[#333333] opacity-70\",\n              children: user.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "FaHome", "FaVideo", "FaUserAlt", "FaChartLine", "FaCog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "isOpen", "setIsOpen", "navItems", "name", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "user", "role", "children", "onClick", "map", "item", "window", "innerWidth", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/Sidebar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { FaHome, FaVideo, FaUserAlt, FaChartLine, FaCog, FaTooth } from 'react-icons/fa';\r\n\r\nconst Sidebar = ({ isOpen, setIsOpen }) => {\r\n  const navItems = [\r\n    { name: 'Dashboard', icon: <FaHome className=\"h-5 w-5\" />, path: '/' },\r\n    { name: 'Video Call', icon: <FaVideo className=\"h-5 w-5\" />, path: '/video' },\r\n    { name: 'Patients', icon: <FaUserAlt className=\"h-5 w-5\" />, path: '/patients' },\r\n    { name: 'Analytics', icon: <FaChartLine className=\"h-5 w-5\" />, path: '/analytics' },\r\n    { name: 'Settings', icon: <FaCog className=\"h-5 w-5\" />, path: '/settings' },\r\n  ];\r\n\r\n  const user = { name: 'Dentist', role: 'dentist' };\r\n\r\n  return (\r\n    <>\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n\r\n      <div\r\n        className={`fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'} bg-white shadow-lg flex flex-col`}\r\n      >\r\n        <div className=\"p-4 flex items-center justify-center\">\r\n          {isOpen && (\r\n            <div className=\"flex items-center justify-center\">\r\n              <FaTooth className=\"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\" />\r\n              <span className=\"ml-2 text-xl font-bold text-[#0077B6]\">Intraoral</span>\r\n            </div>\r\n          )}\r\n          {!isOpen && (\r\n            <FaTooth className=\"w-8 h-8 text-[#0077B6] transition-transform duration-300 hover:scale-110\" />\r\n          )}\r\n        </div>\r\n\r\n        <nav className=\"flex-1 px-2 py-4 overflow-y-auto\">\r\n          <ul className=\"space-y-2\">\r\n            {navItems.map((item) => (\r\n              <li key={item.name}>\r\n                <button\r\n                  className=\"w-full flex items-center p-3 text-[#333333] hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.05)] rounded-lg transition-colors group\"\r\n                  onClick={() => window.innerWidth < 768 && setIsOpen(false)}\r\n                >\r\n                  <span className=\"text-[#333333] group-hover:text-[#0077B6]\">\r\n                    {item.icon}\r\n                  </span>\r\n                  {isOpen && <span className=\"ml-3\">{item.name}</span>}\r\n                </button>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </nav>\r\n\r\n        <div className=\"p-4 border-t border-[rgba(0,119,182,0.1)]\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n              </svg>\r\n            </div>\r\n            {isOpen && (\r\n              <div className=\"ml-3\">\r\n                <p className=\"text-sm font-medium text-[#333333]\">{user.name}</p>\r\n                <p className=\"text-xs text-[#333333] opacity-70\">{user.role}</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Sidebar; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzF,MAAMC,OAAO,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAU,CAAC,KAAK;EACzC,MAAMC,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAER,OAAA,CAACP,MAAM;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAI,CAAC,EACtE;IAAEP,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAER,OAAA,CAACN,OAAO;MAACe,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,EAC7E;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAER,OAAA,CAACL,SAAS;MAACc,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAY,CAAC,EAChF;IAAEP,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAER,OAAA,CAACJ,WAAW;MAACa,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAa,CAAC,EACpF;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAER,OAAA,CAACH,KAAK;MAACY,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAY,CAAC,CAC7E;EAED,MAAMC,IAAI,GAAG;IAAER,IAAI,EAAE,SAAS;IAAES,IAAI,EAAE;EAAU,CAAC;EAEjD,oBACEhB,OAAA,CAAAE,SAAA;IAAAe,QAAA,GACGb,MAAM,iBACLJ,OAAA;MACES,SAAS,EAAC,qDAAqD;MAC/DS,OAAO,EAAEA,CAAA,KAAMb,SAAS,CAAC,KAAK;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,eAEDb,OAAA;MACES,SAAS,EAAE,yEAAyEL,MAAM,GAAG,oBAAoB,GAAG,yCAAyC,mCAAoC;MAAAa,QAAA,gBAEjMjB,OAAA;QAAKS,SAAS,EAAC,sCAAsC;QAAAQ,QAAA,GAClDb,MAAM,iBACLJ,OAAA;UAAKS,SAAS,EAAC,kCAAkC;UAAAQ,QAAA,gBAC/CjB,OAAA,CAACF,OAAO;YAACW,SAAS,EAAC;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChGb,OAAA;YAAMS,SAAS,EAAC,uCAAuC;YAAAQ,QAAA,EAAC;UAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CACN,EACA,CAACT,MAAM,iBACNJ,OAAA,CAACF,OAAO;UAACW,SAAS,EAAC;QAA0E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAChG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENb,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAQ,QAAA,eAC/CjB,OAAA;UAAIS,SAAS,EAAC,WAAW;UAAAQ,QAAA,EACtBX,QAAQ,CAACa,GAAG,CAAEC,IAAI,iBACjBpB,OAAA;YAAAiB,QAAA,eACEjB,OAAA;cACES,SAAS,EAAC,qIAAqI;cAC/IS,OAAO,EAAEA,CAAA,KAAMG,MAAM,CAACC,UAAU,GAAG,GAAG,IAAIjB,SAAS,CAAC,KAAK,CAAE;cAAAY,QAAA,gBAE3DjB,OAAA;gBAAMS,SAAS,EAAC,2CAA2C;gBAAAQ,QAAA,EACxDG,IAAI,CAACZ;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACNT,MAAM,iBAAIJ,OAAA;gBAAMS,SAAS,EAAC,MAAM;gBAAAQ,QAAA,EAAEG,IAAI,CAACb;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC,GATFO,IAAI,CAACb,IAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENb,OAAA;QAAKS,SAAS,EAAC,2CAA2C;QAAAQ,QAAA,eACxDjB,OAAA;UAAKS,SAAS,EAAC,mBAAmB;UAAAQ,QAAA,gBAChCjB,OAAA;YAAKS,SAAS,EAAC,iGAAiG;YAAAQ,QAAA,eAC9GjB,OAAA;cAAKuB,KAAK,EAAC,4BAA4B;cAACd,SAAS,EAAC,SAAS;cAACe,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAT,QAAA,eAC/GjB,OAAA;gBAAM2B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLT,MAAM,iBACLJ,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAQ,QAAA,gBACnBjB,OAAA;cAAGS,SAAS,EAAC,oCAAoC;cAAAQ,QAAA,EAAEF,IAAI,CAACR;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEb,OAAA;cAAGS,SAAS,EAAC,mCAAmC;cAAAQ,QAAA,EAAEF,IAAI,CAACC;YAAI;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACkB,EAAA,GAvEI5B,OAAO;AAyEb,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}