"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatternVisitor = void 0;
const pattern_visitor_1 = __importDefault(require("eslint-scope/lib/pattern-visitor"));
const PatternVisitor = pattern_visitor_1.default;
exports.PatternVisitor = PatternVisitor;
//# sourceMappingURL=PatternVisitor.js.map