{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\Navbar.jsx\";\nimport React from 'react';\nimport { FaSearch, FaHeadset, FaBell } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  toggleSidebar\n}) => {\n  var _user$name;\n  const user = {\n    name: 'Dentist',\n    role: 'dentist'\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between h-16 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: toggleSidebar,\n            className: \"text-[#333333] hover:text-[#0077B6] focus:outline-none\",\n            \"aria-label\": \"Toggle sidebar\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative max-w-xs w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search patients...\",\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex items-center text-[#333333] hover:text-[#0077B6] transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FaBell, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex items-center text-[#333333] hover:text-[#0077B6] transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(FaHeadset, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 w-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\",\n              children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0)) || 'D'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-[#333333] hidden md:inline\",\n              children: (user === null || user === void 0 ? void 0 : user.name) || 'Dentist'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "FaSearch", "FaHeadset", "FaBell", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "toggleSidebar", "_user$name", "user", "name", "role", "className", "children", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "char<PERSON>t", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/Navbar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { FaSearch, FaHeadset, FaBell } from 'react-icons/fa';\r\n\r\nconst Navbar = ({ toggleSidebar }) => {\r\n  const user = { name: 'Dentist', role: 'dentist' };\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)]\">\r\n      <div className=\"px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between h-16 items-center\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <button\r\n              onClick={toggleSidebar}\r\n              className=\"text-[#333333] hover:text-[#0077B6] focus:outline-none\"\r\n              aria-label=\"Toggle sidebar\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"h-6 w-6\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M4 6h16M4 12h16M4 18h16\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            <div className=\"relative max-w-xs w-full\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <FaSearch className=\"h-5 w-5 text-gray-400\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search patients...\"\r\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center space-x-4\">\r\n            <button className=\"flex items-center text-[#333333] hover:text-[#0077B6] transition-colors\">\r\n              <FaBell className=\"h-5 w-5\" />\r\n            </button>\r\n            <button className=\"flex items-center text-[#333333] hover:text-[#0077B6] transition-colors\">\r\n              <FaHeadset className=\"h-5 w-5\" />\r\n            </button>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"h-8 w-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]\">\r\n                {user?.name?.charAt(0) || 'D'}\r\n              </div>\r\n              <span className=\"text-sm font-medium text-[#333333] hidden md:inline\">\r\n                {user?.name || 'Dentist'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Navbar; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAA,IAAAC,UAAA;EACpC,MAAMC,IAAI,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAU,CAAC;EAEjD,oBACEN,OAAA;IAAQO,SAAS,EAAC,0DAA0D;IAAAC,QAAA,eAC1ER,OAAA;MAAKO,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCR,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDR,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CR,OAAA;YACES,OAAO,EAAEP,aAAc;YACvBK,SAAS,EAAC,wDAAwD;YAClE,cAAW,gBAAgB;YAAAC,QAAA,eAE3BR,OAAA;cACEU,KAAK,EAAC,4BAA4B;cAClCH,SAAS,EAAC,SAAS;cACnBI,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAL,QAAA,eAErBR,OAAA;gBACEc,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACTrB,OAAA;YAAKO,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCR,OAAA;cAAKO,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFR,OAAA,CAACJ,QAAQ;gBAACW,SAAS,EAAC;cAAuB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNrB,OAAA;cACEsB,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,oBAAoB;cAChChB,SAAS,EAAC;YAAoM;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/M,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrB,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CR,OAAA;YAAQO,SAAS,EAAC,yEAAyE;YAAAC,QAAA,eACzFR,OAAA,CAACF,MAAM;cAACS,SAAS,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACTrB,OAAA;YAAQO,SAAS,EAAC,yEAAyE;YAAAC,QAAA,eACzFR,OAAA,CAACH,SAAS;cAACU,SAAS,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACTrB,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CR,OAAA;cAAKO,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAC3G,CAAAJ,IAAI,aAAJA,IAAI,wBAAAD,UAAA,GAAJC,IAAI,CAAEC,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYqB,MAAM,CAAC,CAAC,CAAC,KAAI;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACNrB,OAAA;cAAMO,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAClE,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI;YAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GA3DIxB,MAAM;AA6DZ,eAAeA,MAAM;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}