{"name": "@adobe/css-tools", "version": "4.4.3", "description": "CSS parser / stringifier", "source": "src/index.ts", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "exports": {"import": "./dist/index.mjs", "types": "./dist/types.d.ts", "require": "./dist/index.cjs"}, "types": "./dist/types.d.ts", "type": "module", "files": ["dist", "Readme.md"], "devDependencies": {"@parcel/packager-ts": "2.15.0", "@parcel/transformer-typescript-types": "2.15.0", "@types/benchmark": "^2.1.1", "@types/bytes": "^3.1.5", "@types/jest": "^29.5.3", "@types/node": "^22.15.18", "benchmark": "^2.1.4", "bytes": "^3.1.0", "gts": "^6.0.2", "jest": "^29.6.2", "parcel": "^2.15.0", "ts-jest": "^29.1.1", "typescript": "^5.7.3"}, "scripts": {"benchmark": "node benchmark/index.mjs", "test": "jest", "clean": "gts clean && rm -rf ./dist", "build": "parcel build && node ./utils/fix-type-generation.cjs", "fix": "gts fix", "lint": "gts lint", "prepack": "npm run build", "prepare": "npm run build", "pretest": "npm run build", "posttest": "npm run lint"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/adobe/css-tools.git"}, "keywords": ["css", "parser", "stringifier", "stylesheet"]}