{"version": 3, "file": "is-correct-cwd.js", "sourceRoot": "", "sources": ["../../../../src/eslint-bulk-suppressions/cli/utils/is-correct-cwd.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAI3D,oCAEC;AAJD,4CAAoB;AAEpB,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,eAAe,CAAC,IAAI,YAAE,CAAC,UAAU,CAAC,GAAG,GAAG,gBAAgB,CAAC,CAAC;AACvF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport fs from 'fs';\n\nexport function isCorrectCwd(cwd: string): boolean {\n  return fs.existsSync(`${cwd}/.eslintrc.js`) || fs.existsSync(`${cwd}/.eslintrc.cjs`);\n}\n"]}