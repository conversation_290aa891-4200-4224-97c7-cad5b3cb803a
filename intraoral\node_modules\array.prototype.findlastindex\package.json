{"name": "array.prototype.findlastindex", "version": "1.2.6", "description": "An ESnext spec-compliant `Array.prototype.findLastIndex` shim/polyfill/replacement that works as far down as ES3.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "license": "MIT", "bugs": {"url": "https://github.com/es-shims/Array.prototype.findLastIndex/issues"}, "homepage": "https://github.com/es-shims/Array.prototype.findLastIndex#readme", "main": "index.js", "exports": {".": "./index.js", "./auto": "./auto.js", "./polyfill": "./polyfill.js", "./implementation": "./implementation.js", "./shim": "./shim.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "posttest": "npx npm@\">= 10.2\" audit --production", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/Array.prototype.findLastIndex.git"}, "keywords": ["Array.prototype.findLastIndex", "find", "findLast", "findLastIndex", "array", "ESnext", "shim", "polyfill", "last", "es-shim API"], "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "functions-have-names": "^1.2.3", "globalthis": "^1.0.4", "has-strict-mode": "^1.1.0", "hasown": "^2.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "testling": {"files": ["test/index.js", "test/shimmed.js"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}