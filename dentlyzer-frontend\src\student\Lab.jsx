import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFlask, FaUniversity, FaBuilding, FaCheck, FaTimes, FaClock, FaUser, FaCalendarAlt } from 'react-icons/fa';

const Lab = () => {
  const [labRequests, setLabRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    fetchLabRequests();
  }, []);

  const fetchLabRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/lab-requests/student', {
        headers: { Authorization: `Bearer ${token}` }
      });
      setLabRequests(response.data);
    } catch (error) {
      console.error('Error fetching lab requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'approved': return 'text-green-600 bg-green-100 border-green-200';
      case 'rejected': return 'text-red-600 bg-red-100 border-red-200';
      case 'completed': return 'text-blue-600 bg-blue-100 border-blue-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <FaClock className="h-4 w-4" />;
      case 'approved': return <FaCheck className="h-4 w-4" />;
      case 'rejected': return <FaTimes className="h-4 w-4" />;
      case 'completed': return <FaCheck className="h-4 w-4" />;
      default: return <FaFlask className="h-4 w-4" />;
    }
  };

  const getLabTypeIcon = (labType) => {
    return labType === 'university' ? 
      <FaUniversity className="h-5 w-5 text-blue-600" /> : 
      <FaBuilding className="h-5 w-5 text-green-600" />;
  };

  const filteredRequests = labRequests.filter(request => {
    if (filter === 'all') return true;
    return request.status === filter;
  });

  const getStatusCounts = () => {
    return {
      all: labRequests.length,
      pending: labRequests.filter(r => r.status === 'pending').length,
      approved: labRequests.filter(r => r.status === 'approved').length,
      rejected: labRequests.filter(r => r.status === 'rejected').length,
      completed: labRequests.filter(r => r.status === 'completed').length,
    };
  };

  const statusCounts = getStatusCounts();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-lg p-8"
        >
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <FaFlask className="h-8 w-8 text-[#FF6B35] mr-4" />
              <h1 className="text-3xl font-bold text-gray-800">Lab Requests</h1>
            </div>
            <div className="text-sm text-gray-500">
              Total Requests: {labRequests.length}
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="flex flex-wrap gap-2 mb-6">
            {[
              { key: 'all', label: 'All', count: statusCounts.all },
              { key: 'pending', label: 'Pending', count: statusCounts.pending },
              { key: 'approved', label: 'Approved', count: statusCounts.approved },
              { key: 'completed', label: 'Completed', count: statusCounts.completed },
              { key: 'rejected', label: 'Rejected', count: statusCounts.rejected },
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === key
                    ? 'bg-[#FF6B35] text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {label} ({count})
              </button>
            ))}
          </div>

          {/* Lab Requests List */}
          {filteredRequests.length === 0 ? (
            <div className="text-center py-12">
              <FaFlask className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-500 mb-2">
                {filter === 'all' ? 'No lab requests found' : `No ${filter} requests found`}
              </h3>
              <p className="text-gray-400">
                Lab requests will appear here once you submit them from patient profiles.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredRequests.map((request) => (
                <motion.div
                  key={request._id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      {getLabTypeIcon(request.labType)}
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-800">
                          {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}
                        </h3>
                        <p className="text-sm text-gray-600">Request ID: {request._id.slice(-8)}</p>
                      </div>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(request.status)}`}>
                      <div className="flex items-center">
                        {getStatusIcon(request.status)}
                        <span className="ml-1">{request.status.charAt(0).toUpperCase() + request.status.slice(1)}</span>
                      </div>
                    </span>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <FaUser className="h-4 w-4 mr-2" />
                      <span className="font-medium">Patient:</span>
                      <span className="ml-1">{request.patientName}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <FaCalendarAlt className="h-4 w-4 mr-2" />
                      <span className="font-medium">Submitted:</span>
                      <span className="ml-1">{new Date(request.submitDate).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {request.notes && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Notes:</span> {request.notes}
                      </p>
                    </div>
                  )}

                  {request.responseNotes && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Response:</span> {request.responseNotes}
                      </p>
                      {request.responseDate && (
                        <p className="text-xs text-gray-500 mt-1">
                          Responded on: {new Date(request.responseDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Lab;
