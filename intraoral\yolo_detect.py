import sys
import json
from ultralytics import YOLO

if len(sys.argv) < 3:
    print(json.dumps({"error": "Usage: python yolo_detect.py <model_path> <image_path>"}))
    sys.exit(1)

model_path = sys.argv[1]
image_path = sys.argv[2]

try:
    model = YOLO(model_path)
    results = model(image_path)
    detections = []
    names = model.names
    for r in results:
        boxes = r.boxes
        if boxes is not None and hasattr(boxes, 'xyxy'):
            for i in range(len(boxes)):
                box = boxes.xyxy[i].tolist()
                conf = boxes.conf[i].item()
                cls = int(boxes.cls[i].item())
                class_name = names[cls] if cls in names else str(cls)
                x1, y1, x2, y2 = box
                img_w, img_h = r.orig_shape[1], r.orig_shape[0]
                detections.append({
                    "class": class_name,
                    "confidence": conf,
                    "bbox": {
                        "x": x1 / img_w,
                        "y": y1 / img_h,
                        "width": (x2 - x1) / img_w,
                        "height": (y2 - y1) / img_h
                    }
                })
    print(json.dumps({"success": True, "results": detections}))
except Exception as e:
    print(json.dumps({"error": str(e)}))
    sys.exit(1) 