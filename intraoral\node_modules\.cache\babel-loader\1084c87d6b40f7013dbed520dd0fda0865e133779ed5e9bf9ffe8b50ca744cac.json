{"ast": null, "code": "import { addPointerEvent } from '../events/add-pointer-event.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { frame } from '../frameloop/frame.mjs';\nfunction addHoverEvent(node, isActive) {\n  const eventName = \"pointer\" + (isActive ? \"enter\" : \"leave\");\n  const callbackName = \"onHover\" + (isActive ? \"Start\" : \"End\");\n  const handleEvent = (event, info) => {\n    if (event.pointerType === \"touch\" || isDragActive()) return;\n    const props = node.getProps();\n    if (node.animationState && props.whileHover) {\n      node.animationState.setActive(\"whileHover\", isActive);\n    }\n    if (props[callbackName]) {\n      frame.update(() => props[callbackName](event, info));\n    }\n  };\n  return addPointerEvent(node.current, eventName, handleEvent, {\n    passive: !node.getProps()[callbackName]\n  });\n}\nclass HoverGesture extends Feature {\n  mount() {\n    this.unmount = pipe(addHoverEvent(this.node, true), addHoverEvent(this.node, false));\n  }\n  unmount() {}\n}\nexport { HoverGesture };", "map": {"version": 3, "names": ["addPointerEvent", "pipe", "isDragActive", "Feature", "frame", "addHoverEvent", "node", "isActive", "eventName", "callback<PERSON><PERSON>", "handleEvent", "event", "info", "pointerType", "props", "getProps", "animationState", "whileHover", "setActive", "update", "current", "passive", "HoverGesture", "mount", "unmount"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { addPointerEvent } from '../events/add-pointer-event.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction addHoverEvent(node, isActive) {\n    const eventName = \"pointer\" + (isActive ? \"enter\" : \"leave\");\n    const callbackName = \"onHover\" + (isActive ? \"Start\" : \"End\");\n    const handleEvent = (event, info) => {\n        if (event.pointerType === \"touch\" || isDragActive())\n            return;\n        const props = node.getProps();\n        if (node.animationState && props.whileHover) {\n            node.animationState.setActive(\"whileHover\", isActive);\n        }\n        if (props[callbackName]) {\n            frame.update(() => props[callbackName](event, info));\n        }\n    };\n    return addPointerEvent(node.current, eventName, handleEvent, {\n        passive: !node.getProps()[callbackName],\n    });\n}\nclass HoverGesture extends Feature {\n    mount() {\n        this.unmount = pipe(addHoverEvent(this.node, true), addHoverEvent(this.node, false));\n    }\n    unmount() { }\n}\n\nexport { HoverGesture };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iCAAiC;AACjE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,KAAK,QAAQ,wBAAwB;AAE9C,SAASC,aAAaA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACnC,MAAMC,SAAS,GAAG,SAAS,IAAID,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC;EAC5D,MAAME,YAAY,GAAG,SAAS,IAAIF,QAAQ,GAAG,OAAO,GAAG,KAAK,CAAC;EAC7D,MAAMG,WAAW,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;IACjC,IAAID,KAAK,CAACE,WAAW,KAAK,OAAO,IAAIX,YAAY,CAAC,CAAC,EAC/C;IACJ,MAAMY,KAAK,GAAGR,IAAI,CAACS,QAAQ,CAAC,CAAC;IAC7B,IAAIT,IAAI,CAACU,cAAc,IAAIF,KAAK,CAACG,UAAU,EAAE;MACzCX,IAAI,CAACU,cAAc,CAACE,SAAS,CAAC,YAAY,EAAEX,QAAQ,CAAC;IACzD;IACA,IAAIO,KAAK,CAACL,YAAY,CAAC,EAAE;MACrBL,KAAK,CAACe,MAAM,CAAC,MAAML,KAAK,CAACL,YAAY,CAAC,CAACE,KAAK,EAAEC,IAAI,CAAC,CAAC;IACxD;EACJ,CAAC;EACD,OAAOZ,eAAe,CAACM,IAAI,CAACc,OAAO,EAAEZ,SAAS,EAAEE,WAAW,EAAE;IACzDW,OAAO,EAAE,CAACf,IAAI,CAACS,QAAQ,CAAC,CAAC,CAACN,YAAY;EAC1C,CAAC,CAAC;AACN;AACA,MAAMa,YAAY,SAASnB,OAAO,CAAC;EAC/BoB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,OAAO,GAAGvB,IAAI,CAACI,aAAa,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAAC,EAAED,aAAa,CAAC,IAAI,CAACC,IAAI,EAAE,KAAK,CAAC,CAAC;EACxF;EACAkB,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}