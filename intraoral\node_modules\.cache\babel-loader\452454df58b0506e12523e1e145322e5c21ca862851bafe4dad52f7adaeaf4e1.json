{"ast": null, "code": "import { clamp } from '../../../utils/clamp.mjs';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\nconst clampRgbUnit = v => clamp(0, 255, v);\nconst rgbUnit = {\n  ...number,\n  transform: v => Math.round(clampRgbUnit(v))\n};\nconst rgba = {\n  test: isColorString(\"rgb\", \"red\"),\n  parse: splitColor(\"red\", \"green\", \"blue\"),\n  transform: ({\n    red,\n    green,\n    blue,\n    alpha: alpha$1 = 1\n  }) => \"rgba(\" + rgbUnit.transform(red) + \", \" + rgbUnit.transform(green) + \", \" + rgbUnit.transform(blue) + \", \" + sanitize(alpha.transform(alpha$1)) + \")\"\n};\nexport { rgbUnit, rgba };", "map": {"version": 3, "names": ["clamp", "number", "alpha", "sanitize", "isColorString", "splitColor", "clampRgbUnit", "v", "rgbUnit", "transform", "Math", "round", "rgba", "test", "parse", "red", "green", "blue", "alpha$1"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/value/types/color/rgba.mjs"], "sourcesContent": ["import { clamp } from '../../../utils/clamp.mjs';\nimport { number, alpha } from '../numbers/index.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = (v) => clamp(0, 255, v);\nconst rgbUnit = {\n    ...number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: isColorString(\"rgb\", \"red\"),\n    parse: splitColor(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        sanitize(alpha.transform(alpha$1)) +\n        \")\",\n};\n\nexport { rgbUnit, rgba };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAChD,SAASC,MAAM,EAAEC,KAAK,QAAQ,sBAAsB;AACpD,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,aAAa,EAAEC,UAAU,QAAQ,aAAa;AAEvD,MAAMC,YAAY,GAAIC,CAAC,IAAKP,KAAK,CAAC,CAAC,EAAE,GAAG,EAAEO,CAAC,CAAC;AAC5C,MAAMC,OAAO,GAAG;EACZ,GAAGP,MAAM;EACTQ,SAAS,EAAGF,CAAC,IAAKG,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,CAAC,CAAC;AAChD,CAAC;AACD,MAAMK,IAAI,GAAG;EACTC,IAAI,EAAET,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC;EACjCU,KAAK,EAAET,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;EACzCI,SAAS,EAAEA,CAAC;IAAEM,GAAG;IAAEC,KAAK;IAAEC,IAAI;IAAEf,KAAK,EAAEgB,OAAO,GAAG;EAAE,CAAC,KAAK,OAAO,GAC5DV,OAAO,CAACC,SAAS,CAACM,GAAG,CAAC,GACtB,IAAI,GACJP,OAAO,CAACC,SAAS,CAACO,KAAK,CAAC,GACxB,IAAI,GACJR,OAAO,CAACC,SAAS,CAACQ,IAAI,CAAC,GACvB,IAAI,GACJd,QAAQ,CAACD,KAAK,CAACO,SAAS,CAACS,OAAO,CAAC,CAAC,GAClC;AACR,CAAC;AAED,SAASV,OAAO,EAAEI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}