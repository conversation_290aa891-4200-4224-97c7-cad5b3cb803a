{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\AnalysisResults.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './AnalysisResults.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisResults = ({\n  results,\n  isAnalyzing\n}) => {\n  _s();\n  const [analysisSummary, setAnalysisSummary] = useState(null);\n  const [recommendations, setRecommendations] = useState([]);\n  useEffect(() => {\n    if (results && results.length > 0) {\n      generateAnalysisSummary(results);\n      generateRecommendations(results);\n    }\n  }, [results]);\n  const generateAnalysisSummary = detectionResults => {\n    const summary = {\n      totalDetections: detectionResults.length,\n      primaryIssue: null,\n      confidence: 0,\n      severity: 'low'\n    };\n\n    // Find the detection with highest confidence\n    const highestConfidence = detectionResults.reduce((max, current) => current.confidence > max.confidence ? current : max);\n    summary.primaryIssue = highestConfidence.class;\n    summary.confidence = highestConfidence.confidence;\n\n    // Determine severity based on detections\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    if (decayCount > 0) {\n      summary.severity = 'high';\n    } else if (earlyDecayCount > 0) {\n      summary.severity = 'medium';\n    } else {\n      summary.severity = 'low';\n    }\n    setAnalysisSummary(summary);\n  };\n  const generateRecommendations = detectionResults => {\n    const recs = [];\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n    if (decayCount > 0) {\n      recs.push({\n        type: 'urgent',\n        title: 'Immediate Treatment Required',\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\n        icon: '🚨',\n        priority: 1\n      });\n    }\n    if (earlyDecayCount > 0) {\n      recs.push({\n        type: 'warning',\n        title: 'Preventive Care Needed',\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\n        icon: '⚠️',\n        priority: 2\n      });\n    }\n    if (healthyCount > 0) {\n      recs.push({\n        type: 'positive',\n        title: 'Good Oral Health',\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\n        icon: '✅',\n        priority: 3\n      });\n    }\n\n    // Add general recommendations\n    recs.push({\n      type: 'info',\n      title: 'Regular Checkup',\n      description: 'Schedule your next dental checkup within 6 months.',\n      icon: '📅',\n      priority: 4\n    });\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\n  };\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'high':\n        return '#ff6b6b';\n      case 'medium':\n        return '#ffd43b';\n      case 'low':\n        return '#51cf66';\n      default:\n        return '#667eea';\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'high':\n        return '🚨';\n      case 'medium':\n        return '⚠️';\n      case 'low':\n        return '✅';\n      default:\n        return 'ℹ️';\n    }\n  };\n  if (isAnalyzing) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-results\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Processing analysis results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this);\n  }\n  if (!results || results.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-results\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No analysis results available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Start video stream to begin analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"analysis-results\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Analysis Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detection-count\",\n          children: [results.length, \" detection(s)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"analysis-time\",\n          children: new Date().toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), analysisSummary && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"summary-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-icon\",\n          style: {\n            color: getSeverityColor(analysisSummary.severity)\n          },\n          children: getSeverityIcon(analysisSummary.severity)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"summary-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"summary-primary\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"primary-issue\",\n              children: analysisSummary.primaryIssue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"confidence\",\n              children: [(analysisSummary.confidence * 100).toFixed(1), \"% confidence\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"summary-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"severity\",\n              style: {\n                color: getSeverityColor(analysisSummary.severity)\n              },\n              children: [analysisSummary.severity.toUpperCase(), \" SEVERITY\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"total-detections\",\n              children: [analysisSummary.totalDetections, \" total detection(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detections-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Detected Conditions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detections-list\",\n        children: results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detection-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-icon\",\n            children: [result.class === 'decaycavity' && '🦷', result.class === 'early-decay' && '⚠️', result.class === 'healthy tooth' && '✅']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detection-class\",\n              children: result.class\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detection-confidence\",\n              children: [(result.confidence * 100).toFixed(1), \"% confidence\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-confidence-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"confidence-fill\",\n              style: {\n                width: `${result.confidence * 100}%`,\n                backgroundColor: result.confidence >= 0.8 ? '#51cf66' : result.confidence >= 0.6 ? '#ffd43b' : '#ff6b6b'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recommendations-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recommendations-list\",\n        children: recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `recommendation-item ${rec.type}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recommendation-icon\",\n            children: rec.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recommendation-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: rec.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: rec.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"action-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Next Steps\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: \"\\uD83D\\uDCCB Generate Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: \"\\uD83D\\uDCDE Contact Dentist\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: \"\\uD83D\\uDCC5 Schedule Follow-up\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisResults, \"UYQlwBe3O42oXBiuSsTXDpUE2eU=\");\n_c = AnalysisResults;\nexport default AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AnalysisResults", "results", "isAnalyzing", "_s", "analysisSummary", "setAnalysisSummary", "recommendations", "setRecommendations", "length", "generateAnalysisSummary", "generateRecommendations", "detectionResults", "summary", "totalDetections", "primaryIssue", "confidence", "severity", "highestConfidence", "reduce", "max", "current", "class", "decayCount", "filter", "r", "earlyDecayCount", "recs", "healthyCount", "push", "type", "title", "description", "icon", "priority", "sort", "a", "b", "getSeverityColor", "getSeverityIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleTimeString", "style", "color", "toFixed", "toUpperCase", "map", "result", "index", "width", "backgroundColor", "rec", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/AnalysisResults.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './AnalysisResults.css';\r\n\r\nconst AnalysisResults = ({ results, isAnalyzing }) => {\r\n  const [analysisSummary, setAnalysisSummary] = useState(null);\r\n  const [recommendations, setRecommendations] = useState([]);\r\n\r\n  useEffect(() => {\r\n    if (results && results.length > 0) {\r\n      generateAnalysisSummary(results);\r\n      generateRecommendations(results);\r\n    }\r\n  }, [results]);\r\n\r\n  const generateAnalysisSummary = (detectionResults) => {\r\n    const summary = {\r\n      totalDetections: detectionResults.length,\r\n      primaryIssue: null,\r\n      confidence: 0,\r\n      severity: 'low'\r\n    };\r\n\r\n    // Find the detection with highest confidence\r\n    const highestConfidence = detectionResults.reduce((max, current) => \r\n      current.confidence > max.confidence ? current : max\r\n    );\r\n\r\n    summary.primaryIssue = highestConfidence.class;\r\n    summary.confidence = highestConfidence.confidence;\r\n\r\n    // Determine severity based on detections\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n\r\n    if (decayCount > 0) {\r\n      summary.severity = 'high';\r\n    } else if (earlyDecayCount > 0) {\r\n      summary.severity = 'medium';\r\n    } else {\r\n      summary.severity = 'low';\r\n    }\r\n\r\n    setAnalysisSummary(summary);\r\n  };\r\n\r\n  const generateRecommendations = (detectionResults) => {\r\n    const recs = [];\r\n\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n\r\n    if (decayCount > 0) {\r\n      recs.push({\r\n        type: 'urgent',\r\n        title: 'Immediate Treatment Required',\r\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\r\n        icon: '🚨',\r\n        priority: 1\r\n      });\r\n    }\r\n\r\n    if (earlyDecayCount > 0) {\r\n      recs.push({\r\n        type: 'warning',\r\n        title: 'Preventive Care Needed',\r\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\r\n        icon: '⚠️',\r\n        priority: 2\r\n      });\r\n    }\r\n\r\n    if (healthyCount > 0) {\r\n      recs.push({\r\n        type: 'positive',\r\n        title: 'Good Oral Health',\r\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\r\n        icon: '✅',\r\n        priority: 3\r\n      });\r\n    }\r\n\r\n    // Add general recommendations\r\n    recs.push({\r\n      type: 'info',\r\n      title: 'Regular Checkup',\r\n      description: 'Schedule your next dental checkup within 6 months.',\r\n      icon: '📅',\r\n      priority: 4\r\n    });\r\n\r\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\r\n  };\r\n\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return '#ff6b6b';\r\n      case 'medium': return '#ffd43b';\r\n      case 'low': return '#51cf66';\r\n      default: return '#667eea';\r\n    }\r\n  };\r\n\r\n  const getSeverityIcon = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return '🚨';\r\n      case 'medium': return '⚠️';\r\n      case 'low': return '✅';\r\n      default: return 'ℹ️';\r\n    }\r\n  };\r\n\r\n  if (isAnalyzing) {\r\n    return (\r\n      <div className=\"analysis-results\">\r\n        <div className=\"results-header\">\r\n          <h3>Analysis Results</h3>\r\n        </div>\r\n        <div className=\"loading-state\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <p>Processing analysis results...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!results || results.length === 0) {\r\n    return (\r\n      <div className=\"analysis-results\">\r\n        <div className=\"results-header\">\r\n          <h3>Analysis Results</h3>\r\n        </div>\r\n        <div className=\"empty-state\">\r\n          <div className=\"empty-icon\">🔍</div>\r\n          <p>No analysis results available</p>\r\n          <span>Start video stream to begin analysis</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"analysis-results\">\r\n      <div className=\"results-header\">\r\n        <h3>Analysis Results</h3>\r\n        <div className=\"results-meta\">\r\n          <span className=\"detection-count\">{results.length} detection(s)</span>\r\n          <span className=\"analysis-time\">{new Date().toLocaleTimeString()}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {analysisSummary && (\r\n        <div className=\"summary-section\">\r\n          <h4>Summary</h4>\r\n          <div className=\"summary-card\">\r\n            <div className=\"summary-icon\" style={{ color: getSeverityColor(analysisSummary.severity) }}>\r\n              {getSeverityIcon(analysisSummary.severity)}\r\n            </div>\r\n            <div className=\"summary-content\">\r\n              <div className=\"summary-primary\">\r\n                <span className=\"primary-issue\">{analysisSummary.primaryIssue}</span>\r\n                <span className=\"confidence\">{(analysisSummary.confidence * 100).toFixed(1)}% confidence</span>\r\n              </div>\r\n              <div className=\"summary-details\">\r\n                <span className=\"severity\" style={{ color: getSeverityColor(analysisSummary.severity) }}>\r\n                  {analysisSummary.severity.toUpperCase()} SEVERITY\r\n                </span>\r\n                <span className=\"total-detections\">{analysisSummary.totalDetections} total detection(s)</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"detections-section\">\r\n        <h4>Detected Conditions</h4>\r\n        <div className=\"detections-list\">\r\n          {results.map((result, index) => (\r\n            <div key={index} className=\"detection-item\">\r\n              <div className=\"detection-icon\">\r\n                {result.class === 'decaycavity' && '🦷'}\r\n                {result.class === 'early-decay' && '⚠️'}\r\n                {result.class === 'healthy tooth' && '✅'}\r\n              </div>\r\n              <div className=\"detection-info\">\r\n                <span className=\"detection-class\">{result.class}</span>\r\n                <span className=\"detection-confidence\">\r\n                  {(result.confidence * 100).toFixed(1)}% confidence\r\n                </span>\r\n              </div>\r\n              <div className=\"detection-confidence-bar\">\r\n                <div \r\n                  className=\"confidence-fill\"\r\n                  style={{ \r\n                    width: `${result.confidence * 100}%`,\r\n                    backgroundColor: result.confidence >= 0.8 ? '#51cf66' : \r\n                                   result.confidence >= 0.6 ? '#ffd43b' : '#ff6b6b'\r\n                  }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"recommendations-section\">\r\n        <h4>Recommendations</h4>\r\n        <div className=\"recommendations-list\">\r\n          {recommendations.map((rec, index) => (\r\n            <div key={index} className={`recommendation-item ${rec.type}`}>\r\n              <div className=\"recommendation-icon\">{rec.icon}</div>\r\n              <div className=\"recommendation-content\">\r\n                <h5>{rec.title}</h5>\r\n                <p>{rec.description}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"action-section\">\r\n        <h4>Next Steps</h4>\r\n        <div className=\"action-buttons\">\r\n          <button className=\"btn btn-primary\">\r\n            📋 Generate Report\r\n          </button>\r\n          <button className=\"btn btn-secondary\">\r\n            📞 Contact Dentist\r\n          </button>\r\n          <button className=\"btn btn-secondary\">\r\n            📅 Schedule Follow-up\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisResults; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACU,eAAe,EAAEC,kBAAkB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAII,OAAO,IAAIA,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MACjCC,uBAAuB,CAACR,OAAO,CAAC;MAChCS,uBAAuB,CAACT,OAAO,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMQ,uBAAuB,GAAIE,gBAAgB,IAAK;IACpD,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAEF,gBAAgB,CAACH,MAAM;MACxCM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGN,gBAAgB,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAC7DA,OAAO,CAACL,UAAU,GAAGI,GAAG,CAACJ,UAAU,GAAGK,OAAO,GAAGD,GAClD,CAAC;IAEDP,OAAO,CAACE,YAAY,GAAGG,iBAAiB,CAACI,KAAK;IAC9CT,OAAO,CAACG,UAAU,GAAGE,iBAAiB,CAACF,UAAU;;IAEjD;IACA,MAAMO,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IAEtF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBV,OAAO,CAACI,QAAQ,GAAG,MAAM;IAC3B,CAAC,MAAM,IAAIS,eAAe,GAAG,CAAC,EAAE;MAC9Bb,OAAO,CAACI,QAAQ,GAAG,QAAQ;IAC7B,CAAC,MAAM;MACLJ,OAAO,CAACI,QAAQ,GAAG,KAAK;IAC1B;IAEAX,kBAAkB,CAACO,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMF,uBAAuB,GAAIC,gBAAgB,IAAK;IACpD,MAAMe,IAAI,GAAG,EAAE;IAEf,MAAMJ,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACtF,MAAMmB,YAAY,GAAGhB,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,eAAe,CAAC,CAACb,MAAM;IAErF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBI,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,8BAA8B;QACrCC,WAAW,EAAE,iFAAiF;QAC9FC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIR,eAAe,GAAG,CAAC,EAAE;MACvBC,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,wFAAwF;QACrGC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIN,YAAY,GAAG,CAAC,EAAE;MACpBD,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,qEAAqE;QAClFC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACAP,IAAI,CAACE,IAAI,CAAC;MACRC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,oDAAoD;MACjEC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF1B,kBAAkB,CAACmB,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,QAAQ,GAAGG,CAAC,CAACH,QAAQ,CAAC,CAAC;EAClE,CAAC;EAED,MAAMI,gBAAgB,GAAIrB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMsB,eAAe,GAAItB,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,KAAK;QAAE,OAAO,GAAG;MACtB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,IAAId,WAAW,EAAE;IACf,oBACEH,OAAA;MAAKwC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzC,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BzC,OAAA;UAAAyC,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzC,OAAA;UAAKwC,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC7C,OAAA;UAAAyC,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC3C,OAAO,IAAIA,OAAO,CAACO,MAAM,KAAK,CAAC,EAAE;IACpC,oBACET,OAAA;MAAKwC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzC,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BzC,OAAA;UAAAyC,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC7C,OAAA;UAAAyC,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpC7C,OAAA;UAAAyC,QAAA,EAAM;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7C,OAAA;IAAKwC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BzC,OAAA;MAAKwC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzC,OAAA;QAAAyC,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB7C,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzC,OAAA;UAAMwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAEvC,OAAO,CAACO,MAAM,EAAC,eAAa;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtE7C,OAAA;UAAMwC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE,IAAIK,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxC,eAAe,iBACdL,OAAA;MAAKwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BzC,OAAA;QAAAyC,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChB7C,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzC,OAAA;UAAKwC,SAAS,EAAC,cAAc;UAACQ,KAAK,EAAE;YAAEC,KAAK,EAAEX,gBAAgB,CAACjC,eAAe,CAACY,QAAQ;UAAE,CAAE;UAAAwB,QAAA,EACxFF,eAAe,CAAClC,eAAe,CAACY,QAAQ;QAAC;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzC,OAAA;YAAKwC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzC,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEpC,eAAe,CAACU;YAAY;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrE7C,OAAA;cAAMwC,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAE,CAACpC,eAAe,CAACW,UAAU,GAAG,GAAG,EAAEkC,OAAO,CAAC,CAAC,CAAC,EAAC,cAAY;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzC,OAAA;cAAMwC,SAAS,EAAC,UAAU;cAACQ,KAAK,EAAE;gBAAEC,KAAK,EAAEX,gBAAgB,CAACjC,eAAe,CAACY,QAAQ;cAAE,CAAE;cAAAwB,QAAA,GACrFpC,eAAe,CAACY,QAAQ,CAACkC,WAAW,CAAC,CAAC,EAAC,WAC1C;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP7C,OAAA;cAAMwC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAEpC,eAAe,CAACS,eAAe,EAAC,qBAAmB;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED7C,OAAA;MAAKwC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzC,OAAA;QAAAyC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B7C,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7BvC,OAAO,CAACkD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBtD,OAAA;UAAiBwC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBACzCzC,OAAA;YAAKwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC5BY,MAAM,CAAC/B,KAAK,KAAK,aAAa,IAAI,IAAI,EACtC+B,MAAM,CAAC/B,KAAK,KAAK,aAAa,IAAI,IAAI,EACtC+B,MAAM,CAAC/B,KAAK,KAAK,eAAe,IAAI,GAAG;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzC,OAAA;cAAMwC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEY,MAAM,CAAC/B;YAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD7C,OAAA;cAAMwC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GACnC,CAACY,MAAM,CAACrC,UAAU,GAAG,GAAG,EAAEkC,OAAO,CAAC,CAAC,CAAC,EAAC,cACxC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvCzC,OAAA;cACEwC,SAAS,EAAC,iBAAiB;cAC3BQ,KAAK,EAAE;gBACLO,KAAK,EAAE,GAAGF,MAAM,CAACrC,UAAU,GAAG,GAAG,GAAG;gBACpCwC,eAAe,EAAEH,MAAM,CAACrC,UAAU,IAAI,GAAG,GAAG,SAAS,GACtCqC,MAAM,CAACrC,UAAU,IAAI,GAAG,GAAG,SAAS,GAAG;cACxD;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GArBES,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCzC,OAAA;QAAAyC,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB7C,OAAA;QAAKwC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClClC,eAAe,CAAC6C,GAAG,CAAC,CAACK,GAAG,EAAEH,KAAK,kBAC9BtD,OAAA;UAAiBwC,SAAS,EAAE,uBAAuBiB,GAAG,CAAC3B,IAAI,EAAG;UAAAW,QAAA,gBAC5DzC,OAAA;YAAKwC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEgB,GAAG,CAACxB;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD7C,OAAA;YAAKwC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCzC,OAAA;cAAAyC,QAAA,EAAKgB,GAAG,CAAC1B;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpB7C,OAAA;cAAAyC,QAAA,EAAIgB,GAAG,CAACzB;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA,GALES,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzC,OAAA;QAAAyC,QAAA,EAAI;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnB7C,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzC,OAAA;UAAQwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7C,OAAA;UAAQwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7C,OAAA;UAAQwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CAzOIH,eAAe;AAAAyD,EAAA,GAAfzD,eAAe;AA2OrB,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}