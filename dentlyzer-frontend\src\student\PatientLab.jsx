import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFlask, FaUniversity, FaBuilding, FaCheck, FaTimes } from 'react-icons/fa';

const PatientLab = () => {
  const { nationalId } = useParams();
  const [selectedLabType, setSelectedLabType] = useState('');
  const [showSubmitForm, setShowSubmitForm] = useState(false);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [labRequests, setLabRequests] = useState([]);

  useEffect(() => {
    fetchPatientData();
    fetchLabRequests();
  }, [nationalId]);

  const fetchPatientData = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);
      setPatientData(response.data);
    } catch (error) {
      console.error('Error fetching patient data:', error);
    }
  };

  const fetchLabRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/lab-requests/student', {
        headers: { Authorization: `Bearer ${token}` }
      });
      // Filter requests for current patient
      const patientRequests = response.data.filter(req => req.patientId === nationalId);
      setLabRequests(patientRequests);
    } catch (error) {
      console.error('Error fetching lab requests:', error);
    }
  };

  const handleLabTypeSelect = (labType) => {
    setSelectedLabType(labType);
    setShowSubmitForm(true);
  };

  const handleSubmitRequest = async () => {
    if (!selectedLabType || !patientData) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const requestData = {
        patientId: nationalId,
        patientName: patientData.fullName,
        labType: selectedLabType,
        notes: notes
      };

      await axios.post('http://localhost:5000/api/lab-requests', requestData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Reset form
      setSelectedLabType('');
      setShowSubmitForm(false);
      setNotes('');
      
      // Refresh lab requests
      fetchLabRequests();
      
      alert('Lab request submitted successfully!');
    } catch (error) {
      console.error('Error submitting lab request:', error);
      alert('Error submitting lab request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <FaCheck className="h-4 w-4" />;
      case 'rejected': return <FaTimes className="h-4 w-4" />;
      default: return <FaFlask className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-lg p-8"
        >
          <div className="flex items-center mb-8">
            <FaFlask className="h-8 w-8 text-[#FF6B35] mr-4" />
            <h1 className="text-3xl font-bold text-gray-800">Lab Requests</h1>
          </div>

          {patientData && (
            <div className="mb-8 p-4 bg-gray-50 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-700 mb-2">Patient Information</h2>
              <p className="text-gray-600">
                <span className="font-medium">Name:</span> {patientData.fullName}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">National ID:</span> {patientData.nationalId}
              </p>
            </div>
          )}

          {!showSubmitForm ? (
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200 cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => handleLabTypeSelect('university')}
              >
                <div className="flex items-center mb-4">
                  <FaUniversity className="h-8 w-8 text-blue-600 mr-3" />
                  <h3 className="text-xl font-semibold text-blue-800">University Lab</h3>
                </div>
                <p className="text-blue-700">
                  Submit request to the university laboratory for dental work and analysis.
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200 cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => handleLabTypeSelect('outside')}
              >
                <div className="flex items-center mb-4">
                  <FaBuilding className="h-8 w-8 text-green-600 mr-3" />
                  <h3 className="text-xl font-semibold text-green-800">Outside Lab</h3>
                </div>
                <p className="text-green-700">
                  Submit request to an external laboratory for specialized dental services.
                </p>
              </motion.div>
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gray-50 p-6 rounded-xl mb-8"
            >
              <h3 className="text-xl font-semibold text-gray-800 mb-4">
                Submit Request - {selectedLabType === 'university' ? 'University Lab' : 'Outside Lab'}
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35]"
                  rows="4"
                  placeholder="Enter any additional notes or requirements..."
                />
              </div>

              <div className="flex gap-4">
                <button
                  onClick={handleSubmitRequest}
                  disabled={loading}
                  className="px-6 py-2 bg-[#FF6B35] text-white rounded-lg hover:bg-[#E55A2B] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Submitting...' : 'Submit Request'}
                </button>
                <button
                  onClick={() => {
                    setShowSubmitForm(false);
                    setSelectedLabType('');
                    setNotes('');
                  }}
                  className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          )}

          {/* Lab Requests History */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Request History</h3>
            {labRequests.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No lab requests found for this patient.</p>
            ) : (
              <div className="space-y-4">
                {labRequests.map((request) => (
                  <motion.div
                    key={request._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getStatusIcon(request.status)}
                        <span className="ml-2 font-medium text-gray-800">
                          {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}
                        </span>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-medium">Submitted:</span> {new Date(request.submitDate).toLocaleDateString()}
                    </p>
                    {request.notes && (
                      <p className="text-sm text-gray-600 mb-1">
                        <span className="font-medium">Notes:</span> {request.notes}
                      </p>
                    )}
                    {request.responseNotes && (
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Response:</span> {request.responseNotes}
                      </p>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PatientLab;
