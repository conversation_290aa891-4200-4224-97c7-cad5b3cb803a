{"version": 3, "file": "TypeVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/TypeVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAoE;AACpE,oCAAqC;AAErC,uCAAoC;AAEpC,MAAM,WAAY,SAAQ,iBAAO;IAG/B,YAAY,UAAsB;QAChC,KAAK,CAAC,UAAU,CAAC,CAAC;QAHX,0CAAwB;QAI/B,uBAAA,IAAI,2BAAe,UAAU,MAAA,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAsB,EAAE,IAAmB;QACtD,MAAM,cAAc,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IAET,iBAAiB,CACzB,IAK8B;QAE9B,gFAAgF;QAChF,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEhC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC/B,IAAI,kBAAkB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBACzC,8FAA8F;gBAC9F,uBAAA,IAAI,+BAAY;qBACb,YAAY,EAAE;qBACd,gBAAgB,CACf,OAAO,EACP,IAAI,gCAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;gBAEJ,IAAI,OAAO,CAAC,cAAc,EAAE;oBAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;oBACnC,kBAAkB,GAAG,IAAI,CAAC;iBAC3B;YACH,CAAC,CAAC,CAAC;YAEH,qGAAqG;YACrG,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,EAAE;gBACpD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;aAClC;SACF;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5B,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,gBAAgB,CACxB,IAA+D;QAE/D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,4FAA4F;QAC5F,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAEX,UAAU,CAAC,IAAyB;QAC5C,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,2BAA2B;IAC7B,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,wDAAwD;QACxD,kEAAkE;QAClE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAE7D,8FAA8F;QAC9F,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QAExC,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,+BAA+B,CACvC,IAA8C;QAE9C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,cAAc,CAAC,IAA6B;QACpD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,YAAY,CAAC,IAA2B;QAChD,mGAAmG;QACnG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,mFAAmF;IACrF,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE;YACnC,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;gBAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;aAClC;SACF;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAES,WAAW,CAAC,IAA0B;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,KAAK,GAAG,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC;QAE5C;;;;UAIE;QACF,IACE,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,YAAY;YACrC,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,UAAU,EACnC;YACA,yEAAyE;YACzE,IAAI,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;YAC/B,OAAO,YAAY,EAAE;gBACnB,IACE,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,YAAY;oBAC5C,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,UAAU,EAC1C;oBACA,iCAAiC;oBACjC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;oBAClC,SAAS;iBACV;gBACD,IAAI,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,eAAe,EAAE;oBACnD,KAAK,GAAG,YAAY,CAAC;oBACrB,MAAM;iBACP;gBACD,MAAM;aACP;SACF;QAED,KAAK,CAAC,gBAAgB,CACpB,aAAa,CAAC,IAAI,EAClB,IAAI,2BAAc,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CACtD,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAES,sBAAsB,CAC9B,IAAqC;;QAErC,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,wEAAwE;YACxE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACjC;QAED,MAAA,IAAI,CAAC,OAAO,0CAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxC,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;IAES,YAAY,CAAC,IAA2B;QAChD,oEAAoE;QACpE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,kBAAkB,CAAC,IAAiC;QAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7B,sFAAsF;IACxF,CAAC;IAES,mBAAmB,CAAC,IAAkC;QAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,8EAA8E;IAChF,CAAC;IAES,sBAAsB,CAC9B,IAAqC;QAErC,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,wEAAwE;YACxE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;YACzD,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAED,qFAAqF;IAC3E,WAAW,CAAC,IAA0B;QAC9C,IAAI,UAAyD,CAAC;QAC9D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;YACzD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBACxD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;aAClB;YACD,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;SACxB;aAAM;YACL,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC5B;QACD,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;YACjD,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAEQ,kCAAW"}