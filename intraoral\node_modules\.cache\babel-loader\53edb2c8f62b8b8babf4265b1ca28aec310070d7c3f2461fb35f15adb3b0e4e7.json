{"ast": null, "code": "import { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\nimport { getFinalKeyframe } from './utils/get-final-keyframe.mjs';\nimport { animateValue } from '../js/index.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { memo } from '../../../utils/memo.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { frame, cancelFrame } from '../../../frameloop/frame.mjs';\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\", \"backgroundColor\"]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" || valueName === \"backgroundColor\" || !isWaapiSupportedEasing(options.ease);\nfunction createAcceleratedAnimation(value, valueName, {\n  onUpdate,\n  onComplete,\n  ...options\n}) {\n  const canAccelerateAnimation = supportsWaapi() && acceleratedValues.has(valueName) && !options.repeatDelay && options.repeatType !== \"mirror\" && options.damping !== 0 && options.type !== \"inertia\";\n  if (!canAccelerateAnimation) return false;\n  /**\n   * TODO: Unify with js/index\n   */\n  let hasStopped = false;\n  let resolveFinishedPromise;\n  let currentFinishedPromise;\n  /**\n   * Cancelling an animation will write to the DOM. For safety we want to defer\n   * this until the next `update` frame lifecycle. This flag tracks whether we\n   * have a pending cancel, if so we shouldn't allow animations to finish.\n   */\n  let pendingCancel = false;\n  /**\n   * Resolve the current Promise every time we enter the\n   * finished state. This is WAAPI-compatible behaviour.\n   */\n  const updateFinishedPromise = () => {\n    currentFinishedPromise = new Promise(resolve => {\n      resolveFinishedPromise = resolve;\n    });\n  };\n  // Create the first finished promise\n  updateFinishedPromise();\n  let {\n    keyframes,\n    duration = 300,\n    ease,\n    times\n  } = options;\n  /**\n   * If this animation needs pre-generated keyframes then generate.\n   */\n  if (requiresPregeneratedKeyframes(valueName, options)) {\n    const sampleAnimation = animateValue({\n      ...options,\n      repeat: 0,\n      delay: 0\n    });\n    let state = {\n      done: false,\n      value: keyframes[0]\n    };\n    const pregeneratedKeyframes = [];\n    /**\n     * Bail after 20 seconds of pre-generated keyframes as it's likely\n     * we're heading for an infinite loop.\n     */\n    let t = 0;\n    while (!state.done && t < maxDuration) {\n      state = sampleAnimation.sample(t);\n      pregeneratedKeyframes.push(state.value);\n      t += sampleDelta;\n    }\n    times = undefined;\n    keyframes = pregeneratedKeyframes;\n    duration = t - sampleDelta;\n    ease = \"linear\";\n  }\n  const animation = animateStyle(value.owner.current, valueName, keyframes, {\n    ...options,\n    duration,\n    /**\n     * This function is currently not called if ease is provided\n     * as a function so the cast is safe.\n     *\n     * However it would be possible for a future refinement to port\n     * in easing pregeneration from Motion One for browsers that\n     * support the upcoming `linear()` easing function.\n     */\n    ease: ease,\n    times\n  });\n  const cancelAnimation = () => {\n    pendingCancel = false;\n    animation.cancel();\n  };\n  const safeCancel = () => {\n    pendingCancel = true;\n    frame.update(cancelAnimation);\n    resolveFinishedPromise();\n    updateFinishedPromise();\n  };\n  /**\n   * Prefer the `onfinish` prop as it's more widely supported than\n   * the `finished` promise.\n   *\n   * Here, we synchronously set the provided MotionValue to the end\n   * keyframe. If we didn't, when the WAAPI animation is finished it would\n   * be removed from the element which would then revert to its old styles.\n   */\n  animation.onfinish = () => {\n    if (pendingCancel) return;\n    value.set(getFinalKeyframe(keyframes, options));\n    onComplete && onComplete();\n    safeCancel();\n  };\n  /**\n   * Animation interrupt callback.\n   */\n  const controls = {\n    then(resolve, reject) {\n      return currentFinishedPromise.then(resolve, reject);\n    },\n    attachTimeline(timeline) {\n      animation.timeline = timeline;\n      animation.onfinish = null;\n      return noop;\n    },\n    get time() {\n      return millisecondsToSeconds(animation.currentTime || 0);\n    },\n    set time(newTime) {\n      animation.currentTime = secondsToMilliseconds(newTime);\n    },\n    get speed() {\n      return animation.playbackRate;\n    },\n    set speed(newSpeed) {\n      animation.playbackRate = newSpeed;\n    },\n    get duration() {\n      return millisecondsToSeconds(duration);\n    },\n    play: () => {\n      if (hasStopped) return;\n      animation.play();\n      /**\n       * Cancel any pending cancel tasks\n       */\n      cancelFrame(cancelAnimation);\n    },\n    pause: () => animation.pause(),\n    stop: () => {\n      hasStopped = true;\n      if (animation.playState === \"idle\") return;\n      /**\n       * WAAPI doesn't natively have any interruption capabilities.\n       *\n       * Rather than read commited styles back out of the DOM, we can\n       * create a renderless JS animation and sample it twice to calculate\n       * its current value, \"previous\" value, and therefore allow\n       * Motion to calculate velocity for any subsequent animation.\n       */\n      const {\n        currentTime\n      } = animation;\n      if (currentTime) {\n        const sampleAnimation = animateValue({\n          ...options,\n          autoplay: false\n        });\n        value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n      }\n      safeCancel();\n    },\n    complete: () => {\n      if (pendingCancel) return;\n      animation.finish();\n    },\n    cancel: safeCancel\n  };\n  return controls;\n}\nexport { createAcceleratedAnimation };", "map": {"version": 3, "names": ["animateStyle", "isWaapiSupportedEasing", "getFinalKeyframe", "animateValue", "millisecondsToSeconds", "secondsToMilliseconds", "memo", "noop", "frame", "cancelFrame", "supportsWaapi", "Object", "hasOwnProperty", "call", "Element", "prototype", "acceleratedValues", "Set", "sampleDelta", "maxDuration", "requiresPregeneratedKeyframes", "valueName", "options", "type", "ease", "createAcceleratedAnimation", "value", "onUpdate", "onComplete", "canAccelerateAnimation", "has", "repeatDelay", "repeatType", "damping", "hasStopped", "resolveFinishedPromise", "currentFinishedPromise", "pendingCancel", "updateFinishedPromise", "Promise", "resolve", "keyframes", "duration", "times", "sampleAnimation", "repeat", "delay", "state", "done", "pregeneratedKeyframes", "t", "sample", "push", "undefined", "animation", "owner", "current", "cancelAnimation", "cancel", "safeCancel", "update", "onfinish", "set", "controls", "then", "reject", "attachTimeline", "timeline", "time", "currentTime", "newTime", "speed", "playbackRate", "newSpeed", "play", "pause", "stop", "playState", "autoplay", "setWithVelocity", "complete", "finish"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs"], "sourcesContent": ["import { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\nimport { getFinalKeyframe } from './utils/get-final-keyframe.mjs';\nimport { animateValue } from '../js/index.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { memo } from '../../../utils/memo.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { frame, cancelFrame } from '../../../frameloop/frame.mjs';\n\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    \"backgroundColor\",\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" ||\n    valueName === \"backgroundColor\" ||\n    !isWaapiSupportedEasing(options.ease);\nfunction createAcceleratedAnimation(value, valueName, { onUpdate, onComplete, ...options }) {\n    const canAccelerateAnimation = supportsWaapi() &&\n        acceleratedValues.has(valueName) &&\n        !options.repeatDelay &&\n        options.repeatType !== \"mirror\" &&\n        options.damping !== 0 &&\n        options.type !== \"inertia\";\n    if (!canAccelerateAnimation)\n        return false;\n    /**\n     * TODO: Unify with js/index\n     */\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Cancelling an animation will write to the DOM. For safety we want to defer\n     * this until the next `update` frame lifecycle. This flag tracks whether we\n     * have a pending cancel, if so we shouldn't allow animations to finish.\n     */\n    let pendingCancel = false;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let { keyframes, duration = 300, ease, times } = options;\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (requiresPregeneratedKeyframes(valueName, options)) {\n        const sampleAnimation = animateValue({\n            ...options,\n            repeat: 0,\n            delay: 0,\n        });\n        let state = { done: false, value: keyframes[0] };\n        const pregeneratedKeyframes = [];\n        /**\n         * Bail after 20 seconds of pre-generated keyframes as it's likely\n         * we're heading for an infinite loop.\n         */\n        let t = 0;\n        while (!state.done && t < maxDuration) {\n            state = sampleAnimation.sample(t);\n            pregeneratedKeyframes.push(state.value);\n            t += sampleDelta;\n        }\n        times = undefined;\n        keyframes = pregeneratedKeyframes;\n        duration = t - sampleDelta;\n        ease = \"linear\";\n    }\n    const animation = animateStyle(value.owner.current, valueName, keyframes, {\n        ...options,\n        duration,\n        /**\n         * This function is currently not called if ease is provided\n         * as a function so the cast is safe.\n         *\n         * However it would be possible for a future refinement to port\n         * in easing pregeneration from Motion One for browsers that\n         * support the upcoming `linear()` easing function.\n         */\n        ease: ease,\n        times,\n    });\n    const cancelAnimation = () => {\n        pendingCancel = false;\n        animation.cancel();\n    };\n    const safeCancel = () => {\n        pendingCancel = true;\n        frame.update(cancelAnimation);\n        resolveFinishedPromise();\n        updateFinishedPromise();\n    };\n    /**\n     * Prefer the `onfinish` prop as it's more widely supported than\n     * the `finished` promise.\n     *\n     * Here, we synchronously set the provided MotionValue to the end\n     * keyframe. If we didn't, when the WAAPI animation is finished it would\n     * be removed from the element which would then revert to its old styles.\n     */\n    animation.onfinish = () => {\n        if (pendingCancel)\n            return;\n        value.set(getFinalKeyframe(keyframes, options));\n        onComplete && onComplete();\n        safeCancel();\n    };\n    /**\n     * Animation interrupt callback.\n     */\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        attachTimeline(timeline) {\n            animation.timeline = timeline;\n            animation.onfinish = null;\n            return noop;\n        },\n        get time() {\n            return millisecondsToSeconds(animation.currentTime || 0);\n        },\n        set time(newTime) {\n            animation.currentTime = secondsToMilliseconds(newTime);\n        },\n        get speed() {\n            return animation.playbackRate;\n        },\n        set speed(newSpeed) {\n            animation.playbackRate = newSpeed;\n        },\n        get duration() {\n            return millisecondsToSeconds(duration);\n        },\n        play: () => {\n            if (hasStopped)\n                return;\n            animation.play();\n            /**\n             * Cancel any pending cancel tasks\n             */\n            cancelFrame(cancelAnimation);\n        },\n        pause: () => animation.pause(),\n        stop: () => {\n            hasStopped = true;\n            if (animation.playState === \"idle\")\n                return;\n            /**\n             * WAAPI doesn't natively have any interruption capabilities.\n             *\n             * Rather than read commited styles back out of the DOM, we can\n             * create a renderless JS animation and sample it twice to calculate\n             * its current value, \"previous\" value, and therefore allow\n             * Motion to calculate velocity for any subsequent animation.\n             */\n            const { currentTime } = animation;\n            if (currentTime) {\n                const sampleAnimation = animateValue({\n                    ...options,\n                    autoplay: false,\n                });\n                value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n            }\n            safeCancel();\n        },\n        complete: () => {\n            if (pendingCancel)\n                return;\n            animation.finish();\n        },\n        cancel: safeCancel,\n    };\n    return controls;\n}\n\nexport { createAcceleratedAnimation };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,SAASC,sBAAsB,QAAQ,cAAc;AACrD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,oCAAoC;AACjG,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,KAAK,EAAEC,WAAW,QAAQ,8BAA8B;AAEjE,MAAMC,aAAa,GAAGJ,IAAI,CAAC,MAAMK,MAAM,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC1F;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,SAAS,EACT,UAAU,EACV,QAAQ,EACR,WAAW,EACX,iBAAiB,CACpB,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,KAAK;AACzB,MAAMC,6BAA6B,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,IACnFF,SAAS,KAAK,iBAAiB,IAC/B,CAACpB,sBAAsB,CAACqB,OAAO,CAACE,IAAI,CAAC;AACzC,SAASC,0BAA0BA,CAACC,KAAK,EAAEL,SAAS,EAAE;EAAEM,QAAQ;EAAEC,UAAU;EAAE,GAAGN;AAAQ,CAAC,EAAE;EACxF,MAAMO,sBAAsB,GAAGnB,aAAa,CAAC,CAAC,IAC1CM,iBAAiB,CAACc,GAAG,CAACT,SAAS,CAAC,IAChC,CAACC,OAAO,CAACS,WAAW,IACpBT,OAAO,CAACU,UAAU,KAAK,QAAQ,IAC/BV,OAAO,CAACW,OAAO,KAAK,CAAC,IACrBX,OAAO,CAACC,IAAI,KAAK,SAAS;EAC9B,IAAI,CAACM,sBAAsB,EACvB,OAAO,KAAK;EAChB;AACJ;AACA;EACI,IAAIK,UAAU,GAAG,KAAK;EACtB,IAAIC,sBAAsB;EAC1B,IAAIC,sBAAsB;EAC1B;AACJ;AACA;AACA;AACA;EACI,IAAIC,aAAa,GAAG,KAAK;EACzB;AACJ;AACA;AACA;EACI,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAChCF,sBAAsB,GAAG,IAAIG,OAAO,CAAEC,OAAO,IAAK;MAC9CL,sBAAsB,GAAGK,OAAO;IACpC,CAAC,CAAC;EACN,CAAC;EACD;EACAF,qBAAqB,CAAC,CAAC;EACvB,IAAI;IAAEG,SAAS;IAAEC,QAAQ,GAAG,GAAG;IAAElB,IAAI;IAAEmB;EAAM,CAAC,GAAGrB,OAAO;EACxD;AACJ;AACA;EACI,IAAIF,6BAA6B,CAACC,SAAS,EAAEC,OAAO,CAAC,EAAE;IACnD,MAAMsB,eAAe,GAAGzC,YAAY,CAAC;MACjC,GAAGmB,OAAO;MACVuB,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE;IACX,CAAC,CAAC;IACF,IAAIC,KAAK,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEtB,KAAK,EAAEe,SAAS,CAAC,CAAC;IAAE,CAAC;IAChD,MAAMQ,qBAAqB,GAAG,EAAE;IAChC;AACR;AACA;AACA;IACQ,IAAIC,CAAC,GAAG,CAAC;IACT,OAAO,CAACH,KAAK,CAACC,IAAI,IAAIE,CAAC,GAAG/B,WAAW,EAAE;MACnC4B,KAAK,GAAGH,eAAe,CAACO,MAAM,CAACD,CAAC,CAAC;MACjCD,qBAAqB,CAACG,IAAI,CAACL,KAAK,CAACrB,KAAK,CAAC;MACvCwB,CAAC,IAAIhC,WAAW;IACpB;IACAyB,KAAK,GAAGU,SAAS;IACjBZ,SAAS,GAAGQ,qBAAqB;IACjCP,QAAQ,GAAGQ,CAAC,GAAGhC,WAAW;IAC1BM,IAAI,GAAG,QAAQ;EACnB;EACA,MAAM8B,SAAS,GAAGtD,YAAY,CAAC0B,KAAK,CAAC6B,KAAK,CAACC,OAAO,EAAEnC,SAAS,EAAEoB,SAAS,EAAE;IACtE,GAAGnB,OAAO;IACVoB,QAAQ;IACR;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQlB,IAAI,EAAEA,IAAI;IACVmB;EACJ,CAAC,CAAC;EACF,MAAMc,eAAe,GAAGA,CAAA,KAAM;IAC1BpB,aAAa,GAAG,KAAK;IACrBiB,SAAS,CAACI,MAAM,CAAC,CAAC;EACtB,CAAC;EACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBtB,aAAa,GAAG,IAAI;IACpB7B,KAAK,CAACoD,MAAM,CAACH,eAAe,CAAC;IAC7BtB,sBAAsB,CAAC,CAAC;IACxBG,qBAAqB,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgB,SAAS,CAACO,QAAQ,GAAG,MAAM;IACvB,IAAIxB,aAAa,EACb;IACJX,KAAK,CAACoC,GAAG,CAAC5D,gBAAgB,CAACuC,SAAS,EAAEnB,OAAO,CAAC,CAAC;IAC/CM,UAAU,IAAIA,UAAU,CAAC,CAAC;IAC1B+B,UAAU,CAAC,CAAC;EAChB,CAAC;EACD;AACJ;AACA;EACI,MAAMI,QAAQ,GAAG;IACbC,IAAIA,CAACxB,OAAO,EAAEyB,MAAM,EAAE;MAClB,OAAO7B,sBAAsB,CAAC4B,IAAI,CAACxB,OAAO,EAAEyB,MAAM,CAAC;IACvD,CAAC;IACDC,cAAcA,CAACC,QAAQ,EAAE;MACrBb,SAAS,CAACa,QAAQ,GAAGA,QAAQ;MAC7Bb,SAAS,CAACO,QAAQ,GAAG,IAAI;MACzB,OAAOtD,IAAI;IACf,CAAC;IACD,IAAI6D,IAAIA,CAAA,EAAG;MACP,OAAOhE,qBAAqB,CAACkD,SAAS,CAACe,WAAW,IAAI,CAAC,CAAC;IAC5D,CAAC;IACD,IAAID,IAAIA,CAACE,OAAO,EAAE;MACdhB,SAAS,CAACe,WAAW,GAAGhE,qBAAqB,CAACiE,OAAO,CAAC;IAC1D,CAAC;IACD,IAAIC,KAAKA,CAAA,EAAG;MACR,OAAOjB,SAAS,CAACkB,YAAY;IACjC,CAAC;IACD,IAAID,KAAKA,CAACE,QAAQ,EAAE;MAChBnB,SAAS,CAACkB,YAAY,GAAGC,QAAQ;IACrC,CAAC;IACD,IAAI/B,QAAQA,CAAA,EAAG;MACX,OAAOtC,qBAAqB,CAACsC,QAAQ,CAAC;IAC1C,CAAC;IACDgC,IAAI,EAAEA,CAAA,KAAM;MACR,IAAIxC,UAAU,EACV;MACJoB,SAAS,CAACoB,IAAI,CAAC,CAAC;MAChB;AACZ;AACA;MACYjE,WAAW,CAACgD,eAAe,CAAC;IAChC,CAAC;IACDkB,KAAK,EAAEA,CAAA,KAAMrB,SAAS,CAACqB,KAAK,CAAC,CAAC;IAC9BC,IAAI,EAAEA,CAAA,KAAM;MACR1C,UAAU,GAAG,IAAI;MACjB,IAAIoB,SAAS,CAACuB,SAAS,KAAK,MAAM,EAC9B;MACJ;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM;QAAER;MAAY,CAAC,GAAGf,SAAS;MACjC,IAAIe,WAAW,EAAE;QACb,MAAMzB,eAAe,GAAGzC,YAAY,CAAC;UACjC,GAAGmB,OAAO;UACVwD,QAAQ,EAAE;QACd,CAAC,CAAC;QACFpD,KAAK,CAACqD,eAAe,CAACnC,eAAe,CAACO,MAAM,CAACkB,WAAW,GAAGnD,WAAW,CAAC,CAACQ,KAAK,EAAEkB,eAAe,CAACO,MAAM,CAACkB,WAAW,CAAC,CAAC3C,KAAK,EAAER,WAAW,CAAC;MAC1I;MACAyC,UAAU,CAAC,CAAC;IAChB,CAAC;IACDqB,QAAQ,EAAEA,CAAA,KAAM;MACZ,IAAI3C,aAAa,EACb;MACJiB,SAAS,CAAC2B,MAAM,CAAC,CAAC;IACtB,CAAC;IACDvB,MAAM,EAAEC;EACZ,CAAC;EACD,OAAOI,QAAQ;AACnB;AAEA,SAAStC,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}