{"name": "adjust-sourcemap-loader", "version": "4.0.0", "description": "Webpack loader that adjusts source maps", "main": "index.js", "engines": {"node": ">=8.9"}, "repository": {"type": "git", "url": "git+https://github.com/bholloway/adjust-sourcemap-loader.git"}, "keywords": ["webpack", "loader", "source-map", "sourcemap", "sources", "resolve", "adjust"], "author": "bholloway", "license": "MIT", "bugs": {"url": "https://github.com/bholloway/adjust-sourcemap-loader/issues"}, "homepage": "https://github.com/bholloway/adjust-sourcemap-loader", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "devDependencies": {"jshint": "^2.12.0"}, "scripts": {"lint": "jshint index.js lib codec"}}