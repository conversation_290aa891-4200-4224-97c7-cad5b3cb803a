import React, { useState, useRef, useEffect, useCallback } from 'react';
import Webcam from 'react-webcam';
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaCog } from 'react-icons/fa';
import './VideoCall.css';

const VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured, onDetectionResults }) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedImages, setCapturedImages] = useState([]);
  const [autoCapture, setAutoCapture] = useState(true);
  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds
  const [availableCameras, setAvailableCameras] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [showCameraSettings, setShowCameraSettings] = useState(false);
  const webcamRef = useRef(null);
  const intervalRef = useRef(null);

  // Get available cameras
  const getAvailableCameras = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      console.log('Available cameras:', videoDevices);
      setAvailableCameras(videoDevices);
      
      // Auto-select first USB camera or fallback to first available
      const usbCamera = videoDevices.find(device => 
        device.label.toLowerCase().includes('usb') || 
        device.label.toLowerCase().includes('external') ||
        device.label.toLowerCase().includes('camera')
      );
      
      if (usbCamera) {
        setSelectedCamera(usbCamera.deviceId);
        console.log('Selected USB camera:', usbCamera.label);
      } else if (videoDevices.length > 0) {
        setSelectedCamera(videoDevices[0].deviceId);
        console.log('Selected first available camera:', videoDevices[0].label);
      }
    } catch (error) {
      console.error('Error getting cameras:', error);
      toast.error('Failed to get camera list');
    }
  }, []);

  const videoConstraints = {
    width: 640,
    height: 480,
    deviceId: selectedCamera ? { exact: selectedCamera } : undefined
  };

  const sendImageForAnalysis = useCallback(async (imageSrc) => {
    try {
      onStartAnalysis();

      // Convert base64 to blob
      const base64Data = imageSrc.replace(/^data:image\/jpeg;base64,/, '');
      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());

      // Create FormData
      const formData = new FormData();
      formData.append('image', blob, 'capture.jpg');

      // Send to backend for YOLOv8 analysis
      const response = await fetch('/api/analyze', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const results = await response.json();
      console.log('YOLOv8 Results:', results);
      if (onDetectionResults && results && results.results) {
        onDetectionResults(results.results);
      }

    } catch (error) {
      console.error('Error sending image for analysis:', error);
      toast.error('Failed to analyze image');
      if (onDetectionResults) {
        onDetectionResults([]);
      }
    }
  }, [onStartAnalysis, onDetectionResults]);

  const captureImage = useCallback(() => {
    if (webcamRef.current && isStreaming) {
      setIsCapturing(true);
      const imageSrc = webcamRef.current.getScreenshot();

      if (imageSrc) {
        const newImage = {
          id: Date.now(),
          src: imageSrc,
          timestamp: new Date().toISOString(),
          analyzed: false
        };

        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images

        // Pass the captured image to parent component for YOLOv8 analysis
        if (onImageCaptured) {
          onImageCaptured(imageSrc);
        }

        // Send to YOLOv8 analysis
        sendImageForAnalysis(imageSrc);

        toast.success('Image captured and sent for analysis');
      }

      setIsCapturing(false);
    }
  }, [isStreaming, onImageCaptured, sendImageForAnalysis]);

  const startAutoCapture = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      if (isStreaming && webcamRef.current) {
        captureImage();
      }
    }, captureInterval);
  }, [isStreaming, captureInterval, captureImage]);

  const startStream = useCallback(() => {
    if (!selectedCamera) {
      toast.error('Please select a camera first');
      return;
    }
    
    setIsStreaming(true);
    onConnectionStatus(true);
    toast.success('Video stream started');

    // Start automatic capture if enabled
    if (autoCapture) {
      startAutoCapture();
    }
  }, [autoCapture, onConnectionStatus, startAutoCapture, selectedCamera]);

  const stopStream = useCallback(() => {
    setIsStreaming(false);
    onConnectionStatus(false);
    stopAutoCapture();
    toast.info('Video stream stopped');
  }, [onConnectionStatus]);

  const stopAutoCapture = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const toggleAutoCapture = () => {
    setAutoCapture(!autoCapture);
    if (!autoCapture) {
      startAutoCapture();
    } else {
      stopAutoCapture();
    }
  };

  const handleCameraChange = (deviceId) => {
    setSelectedCamera(deviceId);
    if (isStreaming) {
      stopStream();
      setTimeout(() => {
        startStream();
      }, 500);
    }
  };

  useEffect(() => {
    getAvailableCameras();
  }, [getAvailableCameras]);

  useEffect(() => {
    return () => {
      stopAutoCapture();
    };
  }, []);

  useEffect(() => {
    if (autoCapture && isStreaming) {
      startAutoCapture();
    } else {
      stopAutoCapture();
    }
  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);

  return (
    <div className="space-y-6">
      {/* Camera Selection */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">Camera Settings</h3>
          <button
            onClick={() => setShowCameraSettings(!showCameraSettings)}
            className="flex items-center text-sm text-[#0077B6] hover:text-[#20B2AA]"
          >
            <FaCog className="mr-1 h-4 w-4" />
            {showCameraSettings ? 'Hide' : 'Show'} Settings
          </button>
        </div>
        
        {showCameraSettings && (
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Select Camera:
            </label>
            <select
              value={selectedCamera || ''}
              onChange={(e) => handleCameraChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm"
            >
              <option value="">Select a camera...</option>
              {availableCameras.map((camera) => (
                <option key={camera.deviceId} value={camera.deviceId}>
                  {camera.label || `Camera ${camera.deviceId.slice(0, 8)}...`}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500">
              {availableCameras.length} camera(s) detected
            </p>
          </div>
        )}
      </div>

      {/* Video Controls */}
      <div className="flex flex-wrap gap-3 items-center justify-between">
        <div className="flex gap-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${
              isStreaming
                ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg'
                : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'
            }`}
            onClick={isStreaming ? stopStream : startStream}
            disabled={!selectedCamera}
          >
            {isStreaming ? <FaStop className="mr-2 h-4 w-4" /> : <FaPlay className="mr-2 h-4 w-4" />}
            {isStreaming ? 'Stop Stream' : 'Start Stream'}
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={captureImage}
            disabled={!isStreaming || isCapturing}
          >
            <FaCamera className="mr-2 h-4 w-4" />
            {isCapturing ? 'Capturing...' : 'Capture Image'}
          </motion.button>
        </div>

        <div className="flex items-center gap-4">
          <label className="flex items-center text-sm font-medium text-gray-700">
            <input
              type="checkbox"
              checked={autoCapture}
              onChange={toggleAutoCapture}
              disabled={!isStreaming}
              className="mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded"
            />
            Auto Capture
          </label>

          {autoCapture && (
            <select
              value={captureInterval}
              onChange={(e) => setCaptureInterval(Number(e.target.value))}
              disabled={!isStreaming}
              className="px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm"
            >
              <option value={3000}>Every 3s</option>
              <option value={5000}>Every 5s</option>
              <option value={10000}>Every 10s</option>
            </select>
          )}
        </div>
      </div>

      {/* Video Container */}
      <div className="relative bg-gray-900 rounded-xl overflow-hidden shadow-lg">
        {isStreaming ? (
          <Webcam
            ref={webcamRef}
            audio={false}
            screenshotFormat="image/jpeg"
            videoConstraints={videoConstraints}
            className="w-full h-80 md:h-96 lg:h-[500px] object-cover"
          />
        ) : (
          <div className="w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
            <div className="text-center text-white">
              <FaVideo className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium">Click "Start Stream" to begin video consultation</p>
              <p className="text-sm text-gray-400 mt-2">Live dental examination and analysis</p>
            </div>
          </div>
        )}

        {isCapturing && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium">
              <FaCamera className="mr-2 h-5 w-5 animate-pulse" />
              Capturing...
            </div>
          </div>
        )}
      </div>

      {/* Recent Captures */}
      <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
            <FaImage className="h-4 w-4" />
          </div>
          <h3 className="text-lg font-semibold text-[#0077B6]">Recent Captures ({capturedImages.length})</h3>
        </div>

        {capturedImages.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {capturedImages.map((image) => (
              <motion.div
                key={image.id}
                whileHover={{ scale: 1.05 }}
                className="relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200"
              >
                <img
                  src={image.src}
                  alt="Captured"
                  className="w-full h-24 object-cover"
                />
                <div className="p-2">
                  <p className="text-xs text-gray-600 mb-1">
                    {new Date(image.timestamp).toLocaleTimeString()}
                  </p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    image.analyzed
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <FaImage className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No captures yet. Start streaming and capture images for analysis.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoCall; 