'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.ICONS = exports.CLEAR = exports.ARROW = void 0;

/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
const isWindows = process.platform === 'win32';
const ARROW = ' \u203A ';
exports.ARROW = ARROW;
const ICONS = {
  failed: isWindows ? '\u00D7' : '\u2715',
  pending: '\u25CB',
  success: isWindows ? '\u221A' : '\u2713',
  todo: '\u270E'
};
exports.ICONS = ICONS;
const CLEAR = isWindows ? '\x1B[2J\x1B[0f' : '\x1B[2J\x1B[3J\x1B[H';
exports.CLEAR = CLEAR;
