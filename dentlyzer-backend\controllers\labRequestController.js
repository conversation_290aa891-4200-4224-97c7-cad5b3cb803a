const Joi = require('joi');
const LabRequest = require('../models/LabRequest');
const Patient = require('../models/Patient');
const Student = require('../models/Student');

// Validation schema for creating a lab request
const labRequestSchema = Joi.object({
  patientId: Joi.string().required(),
  patientName: Joi.string().required(),
  labType: Joi.string().valid('university', 'outside').required(),
  notes: Joi.string().allow(''),
});

// Validation schema for updating a lab request
const updateLabRequestSchema = Joi.object({
  status: Joi.string().valid('approved', 'rejected', 'completed').required(),
  responseNotes: Joi.string().allow(''),
});

// Create a new lab request
const createLabRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = labRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { patientId, patientName, labType, notes } = req.body;

    // Find the student
    const student = await Student.findOne({ studentId: req.user.studentId });
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Verify the patient exists
    const patient = await Patient.findOne({ nationalId: patientId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Create the lab request
    const labRequest = new LabRequest({
      studentId: req.user.studentId,
      studentName: req.user.name,
      patientId,
      patientName: patient.fullName,
      labType,
      notes: notes || '',
    });

    await labRequest.save();

    res.status(201).json({
      message: 'Lab request created successfully',
      labRequest
    });
  } catch (error) {
    console.error('Error creating lab request:', error);
    res.status(500).json({
      message: 'Server error while creating lab request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all lab requests for a student
const getStudentLabRequests = async (req, res) => {
  try {
    const labRequests = await LabRequest.find({ studentId: req.user.studentId })
      .sort({ submitDate: -1 });

    res.json(labRequests);
  } catch (error) {
    console.error('Error fetching student lab requests:', error);
    res.status(500).json({
      message: 'Server error while fetching lab requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all lab requests (for assistants/admins)
const getAllLabRequests = async (req, res) => {
  try {
    const labRequests = await LabRequest.find()
      .sort({ submitDate: -1 });

    res.json(labRequests);
  } catch (error) {
    console.error('Error fetching all lab requests:', error);
    res.status(500).json({
      message: 'Server error while fetching lab requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update a lab request (approve/reject/complete)
const updateLabRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = updateLabRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { status, responseNotes } = req.body;
    const { requestId } = req.params;

    const labRequest = await LabRequest.findById(requestId);
    if (!labRequest) {
      return res.status(404).json({ message: 'Lab request not found' });
    }

    // Update the lab request
    labRequest.status = status;
    labRequest.responseNotes = responseNotes || '';
    labRequest.responseDate = new Date();
    labRequest.responderId = req.user.id;
    labRequest.responderName = req.user.name;

    await labRequest.save();

    res.json({
      message: `Lab request ${status}`,
      labRequest
    });
  } catch (error) {
    console.error('Error updating lab request:', error);
    res.status(500).json({
      message: 'Server error while updating lab request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createLabRequest,
  getStudentLabRequests,
  getAllLabRequests,
  updateLabRequest
};
