# @babel/plugin-bugfix-firefox-class-in-computed-class-key

> Wraps classes defined in computed keys of other classes affected by https://bugzilla.mozilla.org/show_bug.cgi?id=1887677

See our website [@babel/plugin-bugfix-firefox-class-in-computed-class-key](https://babeljs.io/docs/babel-plugin-bugfix-firefox-class-in-computed-class-key) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-bugfix-firefox-class-in-computed-class-key
```

or using yarn:

```sh
yarn add @babel/plugin-bugfix-firefox-class-in-computed-class-key --dev
```
