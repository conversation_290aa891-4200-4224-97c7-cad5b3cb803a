{"ast": null, "code": "(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory(require(\"react\"));else if (typeof define === 'function' && define.amd) define([\"react\"], factory);else if (typeof exports === 'object') exports[\"Webcam\"] = factory(require(\"react\"));else root[\"Webcam\"] = factory(root[\"React\"]);\n})(this, function (__WEBPACK_EXTERNAL_MODULE_react__) {\n  return /******/function (modules) {\n    // webpackBootstrap\n    /******/ // The module cache\n    /******/\n    var installedModules = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/\n      /******/ // Check if module is in cache\n      /******/if (installedModules[moduleId]) {\n        /******/return installedModules[moduleId].exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = installedModules[moduleId] = {\n        /******/i: moduleId,\n        /******/l: false,\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Flag the module as loaded\n      /******/\n      module.l = true;\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /******/\n    /******/ // expose the modules object (__webpack_modules__)\n    /******/\n    __webpack_require__.m = modules;\n    /******/\n    /******/ // expose the module cache\n    /******/\n    __webpack_require__.c = installedModules;\n    /******/\n    /******/ // define getter function for harmony exports\n    /******/\n    __webpack_require__.d = function (exports, name, getter) {\n      /******/if (!__webpack_require__.o(exports, name)) {\n        /******/Object.defineProperty(exports, name, {\n          enumerable: true,\n          get: getter\n        });\n        /******/\n      }\n      /******/\n    };\n    /******/\n    /******/ // define __esModule on exports\n    /******/\n    __webpack_require__.r = function (exports) {\n      /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n        /******/Object.defineProperty(exports, Symbol.toStringTag, {\n          value: 'Module'\n        });\n        /******/\n      }\n      /******/\n      Object.defineProperty(exports, '__esModule', {\n        value: true\n      });\n      /******/\n    };\n    /******/\n    /******/ // create a fake namespace object\n    /******/ // mode & 1: value is a module id, require it\n    /******/ // mode & 2: merge all properties of value into the ns\n    /******/ // mode & 4: return value when already ns object\n    /******/ // mode & 8|1: behave like require\n    /******/\n    __webpack_require__.t = function (value, mode) {\n      /******/if (mode & 1) value = __webpack_require__(value);\n      /******/\n      if (mode & 8) return value;\n      /******/\n      if (mode & 4 && typeof value === 'object' && value && value.__esModule) return value;\n      /******/\n      var ns = Object.create(null);\n      /******/\n      __webpack_require__.r(ns);\n      /******/\n      Object.defineProperty(ns, 'default', {\n        enumerable: true,\n        value: value\n      });\n      /******/\n      if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {\n        return value[key];\n      }.bind(null, key));\n      /******/\n      return ns;\n      /******/\n    };\n    /******/\n    /******/ // getDefaultExport function for compatibility with non-harmony modules\n    /******/\n    __webpack_require__.n = function (module) {\n      /******/var getter = module && module.__esModule ? /******/function getDefault() {\n        return module['default'];\n      } : /******/function getModuleExports() {\n        return module;\n      };\n      /******/\n      __webpack_require__.d(getter, 'a', getter);\n      /******/\n      return getter;\n      /******/\n    };\n    /******/\n    /******/ // Object.prototype.hasOwnProperty.call\n    /******/\n    __webpack_require__.o = function (object, property) {\n      return Object.prototype.hasOwnProperty.call(object, property);\n    };\n    /******/\n    /******/ // __webpack_public_path__\n    /******/\n    __webpack_require__.p = \"\";\n    /******/\n    /******/\n    /******/ // Load entry module and return exports\n    /******/\n    return __webpack_require__(__webpack_require__.s = \"./src/react-webcam.tsx\");\n    /******/\n  }\n  /************************************************************************/\n  /******/({\n    /***/\"./src/react-webcam.tsx\": (\n    /*!******************************!*\\\n      !*** ./src/react-webcam.tsx ***!\n      \\******************************/\n    /*! exports provided: default */\n    /***/\n    function (module, __webpack_exports__, __webpack_require__) {\n      \"use strict\";\n\n      __webpack_require__.r(__webpack_exports__);\n      /* harmony import */\n      var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */\"react\");\n      /* harmony import */\n      var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n      var __extends = undefined && undefined.__extends || function () {\n        var extendStatics = function (d, b) {\n          extendStatics = Object.setPrototypeOf || {\n            __proto__: []\n          } instanceof Array && function (d, b) {\n            d.__proto__ = b;\n          } || function (d, b) {\n            for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n          };\n          return extendStatics(d, b);\n        };\n        return function (d, b) {\n          extendStatics(d, b);\n          function __() {\n            this.constructor = d;\n          }\n          d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n        };\n      }();\n      var __assign = undefined && undefined.__assign || function () {\n        __assign = Object.assign || function (t) {\n          for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n          }\n          return t;\n        };\n        return __assign.apply(this, arguments);\n      };\n      var __rest = undefined && undefined.__rest || function (s, e) {\n        var t = {};\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n        if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n        }\n        return t;\n      };\n\n      // polyfill based on https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n      (function polyfillGetUserMedia() {\n        if (typeof window === 'undefined') {\n          return;\n        }\n        // Older browsers might not implement mediaDevices at all, so we set an empty object first\n        if (navigator.mediaDevices === undefined) {\n          navigator.mediaDevices = {};\n        }\n        // Some browsers partially implement mediaDevices. We can't just assign an object\n        // with getUserMedia as it would overwrite existing properties.\n        // Here, we will just add the getUserMedia property if it's missing.\n        if (navigator.mediaDevices.getUserMedia === undefined) {\n          navigator.mediaDevices.getUserMedia = function (constraints) {\n            // First get ahold of the legacy getUserMedia, if present\n            var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia;\n            // Some browsers just don't implement it - return a rejected promise with an error\n            // to keep a consistent interface\n            if (!getUserMedia) {\n              return Promise.reject(new Error(\"getUserMedia is not implemented in this browser\"));\n            }\n            // Otherwise, wrap the call to the old navigator.getUserMedia with a Promise\n            return new Promise(function (resolve, reject) {\n              getUserMedia.call(navigator, constraints, resolve, reject);\n            });\n          };\n        }\n      })();\n      function hasGetUserMedia() {\n        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n      }\n      var Webcam = /** @class */function (_super) {\n        __extends(Webcam, _super);\n        function Webcam(props) {\n          var _this = _super.call(this, props) || this;\n          _this.canvas = null;\n          _this.ctx = null;\n          _this.requestUserMediaId = 0;\n          _this.unmounted = false;\n          _this.state = {\n            hasUserMedia: false\n          };\n          return _this;\n        }\n        Webcam.prototype.componentDidMount = function () {\n          var _a = this,\n            state = _a.state,\n            props = _a.props;\n          this.unmounted = false;\n          if (!hasGetUserMedia()) {\n            props.onUserMediaError(\"getUserMedia not supported\");\n            return;\n          }\n          if (!state.hasUserMedia) {\n            this.requestUserMedia();\n          }\n          if (props.children && typeof props.children != 'function') {\n            console.warn(\"children must be a function\");\n          }\n        };\n        Webcam.prototype.componentDidUpdate = function (nextProps) {\n          var props = this.props;\n          if (!hasGetUserMedia()) {\n            props.onUserMediaError(\"getUserMedia not supported\");\n            return;\n          }\n          var audioConstraintsChanged = JSON.stringify(nextProps.audioConstraints) !== JSON.stringify(props.audioConstraints);\n          var videoConstraintsChanged = JSON.stringify(nextProps.videoConstraints) !== JSON.stringify(props.videoConstraints);\n          var minScreenshotWidthChanged = nextProps.minScreenshotWidth !== props.minScreenshotWidth;\n          var minScreenshotHeightChanged = nextProps.minScreenshotHeight !== props.minScreenshotHeight;\n          if (videoConstraintsChanged || minScreenshotWidthChanged || minScreenshotHeightChanged) {\n            this.canvas = null;\n            this.ctx = null;\n          }\n          if (audioConstraintsChanged || videoConstraintsChanged) {\n            this.stopAndCleanup();\n            this.requestUserMedia();\n          }\n        };\n        Webcam.prototype.componentWillUnmount = function () {\n          this.unmounted = true;\n          this.stopAndCleanup();\n        };\n        Webcam.stopMediaStream = function (stream) {\n          if (stream) {\n            if (stream.getVideoTracks && stream.getAudioTracks) {\n              stream.getVideoTracks().map(function (track) {\n                stream.removeTrack(track);\n                track.stop();\n              });\n              stream.getAudioTracks().map(function (track) {\n                stream.removeTrack(track);\n                track.stop();\n              });\n            } else {\n              stream.stop();\n            }\n          }\n        };\n        Webcam.prototype.stopAndCleanup = function () {\n          var state = this.state;\n          if (state.hasUserMedia) {\n            Webcam.stopMediaStream(this.stream);\n            if (state.src) {\n              window.URL.revokeObjectURL(state.src);\n            }\n          }\n        };\n        Webcam.prototype.getScreenshot = function (screenshotDimensions) {\n          var _a = this,\n            state = _a.state,\n            props = _a.props;\n          if (!state.hasUserMedia) return null;\n          var canvas = this.getCanvas(screenshotDimensions);\n          return canvas && canvas.toDataURL(props.screenshotFormat, props.screenshotQuality);\n        };\n        Webcam.prototype.getCanvas = function (screenshotDimensions) {\n          var _a = this,\n            state = _a.state,\n            props = _a.props;\n          if (!this.video) {\n            return null;\n          }\n          if (!state.hasUserMedia || !this.video.videoHeight) return null;\n          if (!this.ctx) {\n            var canvasWidth = this.video.videoWidth;\n            var canvasHeight = this.video.videoHeight;\n            if (!this.props.forceScreenshotSourceSize) {\n              var aspectRatio = canvasWidth / canvasHeight;\n              canvasWidth = props.minScreenshotWidth || this.video.clientWidth;\n              canvasHeight = canvasWidth / aspectRatio;\n              if (props.minScreenshotHeight && canvasHeight < props.minScreenshotHeight) {\n                canvasHeight = props.minScreenshotHeight;\n                canvasWidth = canvasHeight * aspectRatio;\n              }\n            }\n            this.canvas = document.createElement(\"canvas\");\n            this.canvas.width = (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.width) || canvasWidth;\n            this.canvas.height = (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.height) || canvasHeight;\n            this.ctx = this.canvas.getContext(\"2d\");\n          }\n          var _b = this,\n            ctx = _b.ctx,\n            canvas = _b.canvas;\n          if (ctx && canvas) {\n            // adjust the height and width of the canvas to the given dimensions\n            canvas.width = (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.width) || canvas.width;\n            canvas.height = (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.height) || canvas.height;\n            // mirror the screenshot\n            if (props.mirrored) {\n              ctx.translate(canvas.width, 0);\n              ctx.scale(-1, 1);\n            }\n            ctx.imageSmoothingEnabled = props.imageSmoothing;\n            ctx.drawImage(this.video, 0, 0, (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.width) || canvas.width, (screenshotDimensions === null || screenshotDimensions === void 0 ? void 0 : screenshotDimensions.height) || canvas.height);\n            // invert mirroring\n            if (props.mirrored) {\n              ctx.scale(-1, 1);\n              ctx.translate(-canvas.width, 0);\n            }\n          }\n          return canvas;\n        };\n        Webcam.prototype.requestUserMedia = function () {\n          var _this = this;\n          var props = this.props;\n          var sourceSelected = function (audioConstraints, videoConstraints) {\n            var constraints = {\n              video: typeof videoConstraints !== \"undefined\" ? videoConstraints : true\n            };\n            if (props.audio) {\n              constraints.audio = typeof audioConstraints !== \"undefined\" ? audioConstraints : true;\n            }\n            _this.requestUserMediaId++;\n            var myRequestUserMediaId = _this.requestUserMediaId;\n            navigator.mediaDevices.getUserMedia(constraints).then(function (stream) {\n              if (_this.unmounted || myRequestUserMediaId !== _this.requestUserMediaId) {\n                Webcam.stopMediaStream(stream);\n              } else {\n                _this.handleUserMedia(null, stream);\n              }\n            }).catch(function (e) {\n              _this.handleUserMedia(e);\n            });\n          };\n          if (\"mediaDevices\" in navigator) {\n            sourceSelected(props.audioConstraints, props.videoConstraints);\n          } else {\n            var optionalSource_1 = function (id) {\n              return {\n                optional: [{\n                  sourceId: id\n                }]\n              };\n            };\n            var constraintToSourceId_1 = function (constraint) {\n              var deviceId = constraint.deviceId;\n              if (typeof deviceId === \"string\") {\n                return deviceId;\n              }\n              if (Array.isArray(deviceId) && deviceId.length > 0) {\n                return deviceId[0];\n              }\n              if (typeof deviceId === \"object\" && deviceId.ideal) {\n                return deviceId.ideal;\n              }\n              return null;\n            };\n            // @ts-ignore: deprecated api\n            MediaStreamTrack.getSources(function (sources) {\n              var audioSource = null;\n              var videoSource = null;\n              sources.forEach(function (source) {\n                if (source.kind === \"audio\") {\n                  audioSource = source.id;\n                } else if (source.kind === \"video\") {\n                  videoSource = source.id;\n                }\n              });\n              var audioSourceId = constraintToSourceId_1(props.audioConstraints);\n              if (audioSourceId) {\n                audioSource = audioSourceId;\n              }\n              var videoSourceId = constraintToSourceId_1(props.videoConstraints);\n              if (videoSourceId) {\n                videoSource = videoSourceId;\n              }\n              sourceSelected(optionalSource_1(audioSource), optionalSource_1(videoSource));\n            });\n          }\n        };\n        Webcam.prototype.handleUserMedia = function (err, stream) {\n          var props = this.props;\n          if (err || !stream) {\n            this.setState({\n              hasUserMedia: false\n            });\n            props.onUserMediaError(err);\n            return;\n          }\n          this.stream = stream;\n          try {\n            if (this.video) {\n              this.video.srcObject = stream;\n            }\n            this.setState({\n              hasUserMedia: true\n            });\n          } catch (error) {\n            this.setState({\n              hasUserMedia: true,\n              src: window.URL.createObjectURL(stream)\n            });\n          }\n          props.onUserMedia(stream);\n        };\n        Webcam.prototype.render = function () {\n          var _this = this;\n          var _a = this,\n            state = _a.state,\n            props = _a.props;\n          var audio = props.audio,\n            forceScreenshotSourceSize = props.forceScreenshotSourceSize,\n            disablePictureInPicture = props.disablePictureInPicture,\n            onUserMedia = props.onUserMedia,\n            onUserMediaError = props.onUserMediaError,\n            screenshotFormat = props.screenshotFormat,\n            screenshotQuality = props.screenshotQuality,\n            minScreenshotWidth = props.minScreenshotWidth,\n            minScreenshotHeight = props.minScreenshotHeight,\n            audioConstraints = props.audioConstraints,\n            videoConstraints = props.videoConstraints,\n            imageSmoothing = props.imageSmoothing,\n            mirrored = props.mirrored,\n            _b = props.style,\n            style = _b === void 0 ? {} : _b,\n            children = props.children,\n            rest = __rest(props, [\"audio\", \"forceScreenshotSourceSize\", \"disablePictureInPicture\", \"onUserMedia\", \"onUserMediaError\", \"screenshotFormat\", \"screenshotQuality\", \"minScreenshotWidth\", \"minScreenshotHeight\", \"audioConstraints\", \"videoConstraints\", \"imageSmoothing\", \"mirrored\", \"style\", \"children\"]);\n          var videoStyle = mirrored ? __assign(__assign({}, style), {\n            transform: (style.transform || \"\") + \" scaleX(-1)\"\n          }) : style;\n          var childrenProps = {\n            getScreenshot: this.getScreenshot.bind(this)\n          };\n          return react__WEBPACK_IMPORTED_MODULE_0__[\"createElement\"](react__WEBPACK_IMPORTED_MODULE_0__[\"Fragment\"], null, react__WEBPACK_IMPORTED_MODULE_0__[\"createElement\"](\"video\", __assign({\n            autoPlay: true,\n            disablePictureInPicture: disablePictureInPicture,\n            src: state.src,\n            muted: !audio,\n            playsInline: true,\n            ref: function (ref) {\n              _this.video = ref;\n            },\n            style: videoStyle\n          }, rest)), children && children(childrenProps));\n        };\n        Webcam.defaultProps = {\n          audio: false,\n          disablePictureInPicture: false,\n          forceScreenshotSourceSize: false,\n          imageSmoothing: true,\n          mirrored: false,\n          onUserMedia: function () {\n            return undefined;\n          },\n          onUserMediaError: function () {\n            return undefined;\n          },\n          screenshotFormat: \"image/webp\",\n          screenshotQuality: 0.92\n        };\n        return Webcam;\n      }(react__WEBPACK_IMPORTED_MODULE_0__[\"Component\"]);\n      /* harmony default export */\n      __webpack_exports__[\"default\"] = Webcam;\n\n      /***/\n    }),\n    /***/\"react\": (\n    /*!**************************************************************************************!*\\\n      !*** external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"} ***!\n      \\**************************************************************************************/\n    /*! no static exports found */\n    /***/\n    function (module, exports) {\n      module.exports = __WEBPACK_EXTERNAL_MODULE_react__;\n\n      /***/\n    })\n\n    /******/\n  })[\"default\"];\n});", "map": {"version": 3, "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE_react__", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "polyfillGetUserMedia", "window", "navigator", "mediaDevices", "undefined", "getUserMedia", "constraints", "webkitGetUserMedia", "mozGetUserMedia", "msGetUserMedia", "Promise", "reject", "Error", "resolve", "hasGetUserMedia", "Webcam", "_super", "__extends", "props", "_this", "canvas", "ctx", "requestUserMediaId", "unmounted", "state", "hasUserMedia", "componentDidMount", "_a", "onUserMediaError", "requestUserMedia", "children", "console", "warn", "componentDidUpdate", "nextProps", "audioConstraintsChanged", "JSON", "stringify", "audioConstraints", "videoConstraintsChanged", "videoConstraints", "minScreenshotWidthChanged", "minScreenshotWidth", "minScreenshotHeightChanged", "minScreenshotHeight", "stopAndCleanup", "componentWillUnmount", "stopMediaStream", "stream", "getVideoTracks", "getAudioTracks", "map", "track", "removeTrack", "stop", "src", "URL", "revokeObjectURL", "getScreenshot", "screenshotDimensions", "get<PERSON>anvas", "toDataURL", "screenshotFormat", "screenshotQuality", "video", "videoHeight", "canvasWidth", "videoWidth", "canvasHeight", "forceScreenshotSourceSize", "aspectRatio", "clientWidth", "document", "createElement", "width", "height", "getContext", "_b", "mirrored", "translate", "scale", "imageSmoothingEnabled", "imageSmoothing", "drawImage", "sourceSelected", "audio", "myRequestUserMediaId", "then", "handleUserMedia", "catch", "e", "optionalSource_1", "id", "optional", "sourceId", "constraintToSourceId_1", "constraint", "deviceId", "Array", "isArray", "length", "ideal", "MediaStreamTrack", "getSources", "sources", "audioSource", "videoSource", "for<PERSON>ach", "source", "kind", "audioSourceId", "videoSourceId", "err", "setState", "srcObject", "error", "createObjectURL", "onUserMedia", "render", "disablePictureInPicture", "style", "rest", "__rest", "videoStyle", "__assign", "transform", "childrenProps", "react__WEBPACK_IMPORTED_MODULE_0__", "autoPlay", "muted", "playsInline", "ref", "defaultProps", "__webpack_exports__"], "sources": ["webpack://Webcam/webpack/universalModuleDefinition", "webpack://Webcam/webpack/bootstrap", "webpack://Webcam/src/react-webcam.tsx", "webpack://Webcam/external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Webcam\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"Webcam\"] = factory(root[\"React\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_react__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./src/react-webcam.tsx\");\n", "import * as React from \"react\";\n\n// polyfill based on https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n(function polyfillGetUserMedia() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // Older browsers might not implement mediaDevices at all, so we set an empty object first\n  if (navigator.mediaDevices === undefined) {\n    (navigator as any).mediaDevices = {};\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n  if (navigator.mediaDevices.getUserMedia === undefined) {\n    navigator.mediaDevices.getUserMedia = function(constraints) {\n      // First get ahold of the legacy getUserMedia, if present\n      const getUserMedia =\n        navigator.getUserMedia ||\n        navigator.webkitGetUserMedia ||\n        navigator.mozGetUserMedia ||\n        navigator.msGetUserMedia;\n\n      // Some browsers just don't implement it - return a rejected promise with an error\n      // to keep a consistent interface\n      if (!getUserMedia) {\n        return Promise.reject(\n          new Error(\"getUserMedia is not implemented in this browser\")\n        );\n      }\n\n      // Otherwise, wrap the call to the old navigator.getUserMedia with a Promise\n      return new Promise(function(resolve, reject) {\n        getUserMedia.call(navigator, constraints, resolve, reject);\n      });\n    };\n  }\n})();\n\nfunction hasGetUserMedia() {\n  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n}\n\ninterface ScreenshotDimensions {\n  width: number;\n  height: number;\n}\n\ninterface ChildrenProps {\n  getScreenshot: (screenshotDimensions?: ScreenshotDimensions) => string | null;\n}\n\nexport type WebcamProps = Omit<React.HTMLProps<HTMLVideoElement>, \"ref\"> & {\n  audio: boolean;\n  audioConstraints?: MediaStreamConstraints[\"audio\"];\n  disablePictureInPicture: boolean;\n  forceScreenshotSourceSize: boolean;\n  imageSmoothing: boolean;\n  mirrored: boolean;\n  minScreenshotHeight?: number;\n  minScreenshotWidth?: number;\n  onUserMedia: (stream: MediaStream) => void;\n  onUserMediaError: (error: string | DOMException) => void;\n  screenshotFormat: \"image/webp\" | \"image/png\" | \"image/jpeg\";\n  screenshotQuality: number;\n  videoConstraints?: MediaStreamConstraints[\"video\"];\n  children?: (childrenProps: ChildrenProps) => JSX.Element;\n}\n\ninterface WebcamState {\n  hasUserMedia: boolean;\n  src?: string;\n}\n\nexport default class Webcam extends React.Component<WebcamProps, WebcamState> {\n  static defaultProps = {\n    audio: false,\n    disablePictureInPicture: false,\n    forceScreenshotSourceSize: false,\n    imageSmoothing: true,\n    mirrored: false,\n    onUserMedia: () => undefined,\n    onUserMediaError: () => undefined,\n    screenshotFormat: \"image/webp\",\n    screenshotQuality: 0.92,\n  };\n\n  private canvas: HTMLCanvasElement | null = null;\n\n  private ctx: CanvasRenderingContext2D | null = null;\n\n  private requestUserMediaId = 0;\n\n  private unmounted = false;\n\n  stream: MediaStream | null;\n\n  video: HTMLVideoElement | null;\n\n  constructor(props: WebcamProps) {\n    super(props);\n    this.state = {\n      hasUserMedia: false\n    };\n  }\n\n  componentDidMount() {\n    const { state, props } = this;\n    this.unmounted = false;\n\n    if (!hasGetUserMedia()) {\n      props.onUserMediaError(\"getUserMedia not supported\");\n\n      return;\n    }\n\n    if (!state.hasUserMedia) {\n      this.requestUserMedia();\n    }\n\n    if (props.children && typeof props.children != 'function') {\n      console.warn(\"children must be a function\");\n    }\n  }\n\n  componentDidUpdate(nextProps: WebcamProps) {\n    const { props } = this;\n\n    if (!hasGetUserMedia()) {\n      props.onUserMediaError(\"getUserMedia not supported\");\n\n      return;\n    }\n\n    const audioConstraintsChanged =\n      JSON.stringify(nextProps.audioConstraints) !==\n      JSON.stringify(props.audioConstraints);\n    const videoConstraintsChanged =\n      JSON.stringify(nextProps.videoConstraints) !==\n      JSON.stringify(props.videoConstraints);\n    const minScreenshotWidthChanged =\n      nextProps.minScreenshotWidth !== props.minScreenshotWidth;\n    const minScreenshotHeightChanged =\n      nextProps.minScreenshotHeight !== props.minScreenshotHeight;\n    if (\n      videoConstraintsChanged ||\n      minScreenshotWidthChanged ||\n      minScreenshotHeightChanged\n    ) {\n      this.canvas = null;\n      this.ctx = null;\n    }\n    if (audioConstraintsChanged || videoConstraintsChanged) {\n      this.stopAndCleanup();\n      this.requestUserMedia();\n    }\n  }\n\n  componentWillUnmount() {\n    this.unmounted = true;\n    this.stopAndCleanup();\n  }\n\n  private static stopMediaStream(stream: MediaStream | null) {\n    if (stream) {\n      if (stream.getVideoTracks && stream.getAudioTracks) {\n        stream.getVideoTracks().map(track => {\n          stream.removeTrack(track);\n          track.stop();\n        });\n        stream.getAudioTracks().map(track => {\n          stream.removeTrack(track);\n          track.stop()\n        });\n      } else {\n        ((stream as unknown) as MediaStreamTrack).stop();\n      }\n    }\n  }\n\n  private stopAndCleanup() {\n    const { state } = this;\n\n    if (state.hasUserMedia) {\n      Webcam.stopMediaStream(this.stream);\n\n      if (state.src) {\n        window.URL.revokeObjectURL(state.src);\n      }\n    }\n  }\n\n  getScreenshot(screenshotDimensions?: ScreenshotDimensions) {\n    const { state, props } = this;\n\n    if (!state.hasUserMedia) return null;\n\n    const canvas = this.getCanvas(screenshotDimensions);\n    return (\n      canvas &&\n      canvas.toDataURL(props.screenshotFormat, props.screenshotQuality)\n    );\n  }\n\n  getCanvas(screenshotDimensions?: ScreenshotDimensions) {\n    const { state, props } = this;\n\n    if (!this.video) {\n      return null;\n    }\n\n    if (!state.hasUserMedia || !this.video.videoHeight) return null;\n\n    if (!this.ctx) {\n      let canvasWidth = this.video.videoWidth;\n      let canvasHeight = this.video.videoHeight;\n      if (!this.props.forceScreenshotSourceSize) {\n        const aspectRatio = canvasWidth / canvasHeight;\n\n        canvasWidth = props.minScreenshotWidth || this.video.clientWidth;\n        canvasHeight = canvasWidth / aspectRatio;\n\n        if (\n          props.minScreenshotHeight &&\n          canvasHeight < props.minScreenshotHeight\n        ) {\n          canvasHeight = props.minScreenshotHeight;\n          canvasWidth = canvasHeight * aspectRatio;\n        }\n      }\n\n      this.canvas = document.createElement(\"canvas\");\n      this.canvas.width = screenshotDimensions?.width ||  canvasWidth;\n      this.canvas.height = screenshotDimensions?.height || canvasHeight;\n      this.ctx = this.canvas.getContext(\"2d\");\n    }\n\n    const { ctx, canvas } = this;\n\n    if (ctx && canvas) {\n\n      // adjust the height and width of the canvas to the given dimensions\n      canvas.width = screenshotDimensions?.width ||  canvas.width;\n      canvas.height = screenshotDimensions?.height || canvas.height;\n\n      // mirror the screenshot\n      if (props.mirrored) {\n        ctx.translate(canvas.width, 0);\n        ctx.scale(-1, 1);\n      }\n\n      ctx.imageSmoothingEnabled = props.imageSmoothing;\n      ctx.drawImage(this.video, 0, 0, screenshotDimensions?.width || canvas.width, screenshotDimensions?.height || canvas.height);\n\n      // invert mirroring\n      if (props.mirrored) {\n        ctx.scale(-1, 1);\n        ctx.translate(-canvas.width, 0);\n      }\n    }\n\n    return canvas;\n  }\n\n  private requestUserMedia() {\n    const { props } = this;\n\n    const sourceSelected = (\n      audioConstraints: boolean | MediaTrackConstraints | undefined,\n      videoConstraints: boolean | MediaTrackConstraints | undefined,\n    ) => {\n      const constraints: MediaStreamConstraints = {\n        video: typeof videoConstraints !== \"undefined\" ? videoConstraints : true\n      };\n\n      if (props.audio) {\n        constraints.audio =\n          typeof audioConstraints !== \"undefined\" ? audioConstraints : true;\n      }\n\n      this.requestUserMediaId++\n      const myRequestUserMediaId = this.requestUserMediaId\n\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then(stream => {\n          if (this.unmounted || myRequestUserMediaId !== this.requestUserMediaId) {\n            Webcam.stopMediaStream(stream);\n          } else {\n            this.handleUserMedia(null, stream);\n          }\n        })\n        .catch(e => {\n          this.handleUserMedia(e);\n        });\n    };\n\n    if (\"mediaDevices\" in navigator) {\n      sourceSelected(props.audioConstraints, props.videoConstraints);\n    } else {\n      const optionalSource = (id: string | null) => ({ optional: [{ sourceId: id }] }) as MediaTrackConstraints;\n\n      const constraintToSourceId = (constraint) => {\n        const { deviceId } = constraint;\n\n        if (typeof deviceId === \"string\") {\n          return deviceId;\n        }\n\n        if (Array.isArray(deviceId) && deviceId.length > 0) {\n          return deviceId[0];\n        }\n\n        if (typeof deviceId === \"object\" && deviceId.ideal) {\n          return deviceId.ideal;\n        }\n\n        return null;\n      };\n\n      // @ts-ignore: deprecated api\n      MediaStreamTrack.getSources(sources => {\n        let audioSource: string | null = null;\n        let videoSource: string | null = null;\n\n        sources.forEach((source: MediaStreamTrack) => {\n          if (source.kind === \"audio\") {\n            audioSource = source.id;\n          } else if (source.kind === \"video\") {\n            videoSource = source.id;\n          }\n        });\n\n        const audioSourceId = constraintToSourceId(props.audioConstraints);\n        if (audioSourceId) {\n          audioSource = audioSourceId;\n        }\n\n        const videoSourceId = constraintToSourceId(props.videoConstraints);\n        if (videoSourceId) {\n          videoSource = videoSourceId;\n        }\n\n        sourceSelected(\n          optionalSource(audioSource),\n          optionalSource(videoSource)\n        );\n      });\n    }\n  }\n\n  private handleUserMedia(err, stream?: MediaStream) {\n    const { props } = this;\n\n    if (err || !stream) {\n      this.setState({ hasUserMedia: false });\n      props.onUserMediaError(err);\n\n      return;\n    }\n\n    this.stream = stream;\n\n    try {\n      if (this.video) {\n        this.video.srcObject = stream;\n      }\n      this.setState({ hasUserMedia: true });\n    } catch (error) {\n      this.setState({\n        hasUserMedia: true,\n        src: window.URL.createObjectURL(stream)\n      });\n    }\n\n    props.onUserMedia(stream);\n  }\n\n  render() {\n    const { state, props } = this;\n\n    const {\n      audio,\n      forceScreenshotSourceSize,\n      disablePictureInPicture,\n      onUserMedia,\n      onUserMediaError,\n      screenshotFormat,\n      screenshotQuality,\n      minScreenshotWidth,\n      minScreenshotHeight,\n      audioConstraints,\n      videoConstraints,\n      imageSmoothing,\n      mirrored,\n      style = {},\n      children,\n      ...rest\n    } = props;\n\n    const videoStyle = mirrored ? { ...style, transform: `${style.transform || \"\"} scaleX(-1)` } : style;\n\n    const childrenProps: ChildrenProps = {\n      getScreenshot: this.getScreenshot.bind(this),\n    };\n\n    return (\n      <>\n        <video\n          autoPlay\n          disablePictureInPicture={disablePictureInPicture}\n          src={state.src}\n          muted={!audio}\n          playsInline\n          ref={ref => {\n            this.video = ref;\n          }}\n          style={videoStyle}\n          {...rest}\n        />\n        {children && children(childrenProps)}\n      </>\n    );\n  }\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE_react__;"], "mappings": "AAAA,UAAAA,iCAAAC,IAAA,EAAAC,OAAA;EACA,WAAAC,OAAA,wBAAAC,MAAA,eACAA,MAAA,CAAAD,OAAA,GAAAD,OAAA,CAAAG,OAAA,gBACA,WAAAC,MAAA,mBAAAA,MAAA,CAAAC,GAAA,EACAD,MAAA,YAAAJ,OAAA,OACA,WAAAC,OAAA,eACAA,OAAA,aAAAD,OAAA,CAAAG,OAAA,gBAEAJ,IAAA,aAAAC,OAAA,CAAAD,IAAA;AACA,CAAC,kBAAAO,iCAAA;EACD,O;;aCVA;;IACA,IAAAC,gBAAA;;aAEA;;IACA,SAAAC,oBAAAC,QAAA;;eAEA;cACA,IAAAF,gBAAA,CAAAE,QAAA;gBACA,OAAAF,gBAAA,CAAAE,QAAA,EAAAR,OAAA;;MACA;eACA;;MACA,IAAAC,MAAA,GAAAK,gBAAA,CAAAE,QAAA;gBACAC,CAAA,EAAAD,QAAA;gBACAE,CAAA;gBACAV,OAAA;;MACA;;eAEA;;MACAW,OAAA,CAAAH,QAAA,EAAAI,IAAA,CAAAX,MAAA,CAAAD,OAAA,EAAAC,MAAA,EAAAA,MAAA,CAAAD,OAAA,EAAAO,mBAAA;;eAEA;;MACAN,MAAA,CAAAS,CAAA;;eAEA;;MACA,OAAAT,MAAA,CAAAD,OAAA;;IACA;;;aAGA;;IACAO,mBAAA,CAAAM,CAAA,GAAAF,OAAA;;aAEA;;IACAJ,mBAAA,CAAAO,CAAA,GAAAR,gBAAA;;aAEA;;IACAC,mBAAA,CAAAQ,CAAA,aAAAf,OAAA,EAAAgB,IAAA,EAAAC,MAAA;cACA,KAAAV,mBAAA,CAAAW,CAAA,CAAAlB,OAAA,EAAAgB,IAAA;gBACAG,MAAA,CAAAC,cAAA,CAAApB,OAAA,EAAAgB,IAAA;UAA0CK,UAAA;UAAAC,GAAA,EAAAL;QAAA,CAAgC;;MAC1E;;IACA;;aAEA;;IACAV,mBAAA,CAAAgB,CAAA,aAAAvB,OAAA;cACA,WAAAwB,MAAA,oBAAAA,MAAA,CAAAC,WAAA;gBACAN,MAAA,CAAAC,cAAA,CAAApB,OAAA,EAAAwB,MAAA,CAAAC,WAAA;UAAwDC,KAAA;QAAA,CAAkB;;MAC1E;;MACAP,MAAA,CAAAC,cAAA,CAAApB,OAAA;QAAiD0B,KAAA;MAAA,CAAc;;IAC/D;;aAEA;aACA;aACA;aACA;aACA;;IACAnB,mBAAA,CAAAoB,CAAA,aAAAD,KAAA,EAAAE,IAAA;cACA,IAAAA,IAAA,MAAAF,KAAA,GAAAnB,mBAAA,CAAAmB,KAAA;;MACA,IAAAE,IAAA,aAAAF,KAAA;;MACA,IAAAE,IAAA,eAAAF,KAAA,iBAAAA,KAAA,IAAAA,KAAA,CAAAG,UAAA,SAAAH,KAAA;;MACA,IAAAI,EAAA,GAAAX,MAAA,CAAAY,MAAA;;MACAxB,mBAAA,CAAAgB,CAAA,CAAAO,EAAA;;MACAX,MAAA,CAAAC,cAAA,CAAAU,EAAA;QAAyCT,UAAA;QAAAK,KAAA,EAAAA;MAAA,CAAiC;;MAC1E,IAAAE,IAAA,eAAAF,KAAA,uBAAAM,GAAA,IAAAN,KAAA,EAAAnB,mBAAA,CAAAQ,CAAA,CAAAe,EAAA,EAAAE,GAAA,YAAAA,GAAA;QAAgH,OAAAN,KAAA,CAAAM,GAAA;MAAmB,CAAE,CAAAC,IAAA,OAAAD,GAAA;;MACrI,OAAAF,EAAA;;IACA;;aAEA;;IACAvB,mBAAA,CAAA2B,CAAA,aAAAjC,MAAA;cACA,IAAAgB,MAAA,GAAAhB,MAAA,IAAAA,MAAA,CAAA4B,UAAA,G,QACA,SAAAM,WAAA;QAA2B,OAAAlC,MAAA;MAA0B,CAAE,G,QACvD,SAAAmC,iBAAA;QAAiC,OAAAnC,MAAA;MAAe;;MAChDM,mBAAA,CAAAQ,CAAA,CAAAE,MAAA,OAAAA,MAAA;;MACA,OAAAA,MAAA;;IACA;;aAEA;;IACAV,mBAAA,CAAAW,CAAA,aAAAmB,MAAA,EAAAC,QAAA;MAAsD,OAAAnB,MAAA,CAAAoB,SAAA,CAAAC,cAAA,CAAA5B,IAAA,CAAAyB,MAAA,EAAAC,QAAA;IAA+D;;aAErH;;IACA/B,mBAAA,CAAAkC,CAAA;;;aAGA;;IACA,OAAAlC,mBAAA,CAAAA,mBAAA,CAAAmC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MChFA;MACA,CAAC,SAASC,oBAAoBA,CAAA;QAC5B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;UACjC;;QAGF;QACA,IAAIC,SAAS,CAACC,YAAY,KAAKC,SAAS,EAAE;UACvCF,SAAiB,CAACC,YAAY,GAAG,EAAE;;QAGtC;QACA;QACA;QACA,IAAID,SAAS,CAACC,YAAY,CAACE,YAAY,KAAKD,SAAS,EAAE;UACrDF,SAAS,CAACC,YAAY,CAACE,YAAY,GAAG,UAASC,WAAW;YACxD;YACA,IAAMD,YAAY,GAChBH,SAAS,CAACG,YAAY,IACtBH,SAAS,CAACK,kBAAkB,IAC5BL,SAAS,CAACM,eAAe,IACzBN,SAAS,CAACO,cAAc;YAE1B;YACA;YACA,IAAI,CAACJ,YAAY,EAAE;cACjB,OAAOK,OAAO,CAACC,MAAM,CACnB,IAAIC,KAAK,CAAC,iDAAiD,CAAC,CAC7D;;YAGH;YACA,OAAO,IAAIF,OAAO,CAAC,UAASG,OAAO,EAAEF,MAAM;cACzCN,YAAY,CAACpC,IAAI,CAACiC,SAAS,EAAEI,WAAW,EAAEO,OAAO,EAAEF,MAAM,CAAC;YAC5D,CAAC,CAAC;UACJ,CAAC;;MAEL,CAAC,EAAC,CAAE;MAEJ,SAASG,eAAeA,CAAA;QACtB,OAAO,CAAC,EAAEZ,SAAS,CAACC,YAAY,IAAID,SAAS,CAACC,YAAY,CAACE,YAAY,CAAC;MAC1E;MAiCA,IAAAU,MAAA,0BAAAC,MAAA;QAAoCC,SAAA,CAAAF,MAAA,EAAAC,MAAA;QAyBlC,SAAAD,OAAYG,KAAkB;UAA9B,IAAAC,KAAA,GACEH,MAAA,CAAA/C,IAAA,OAAMiD,KAAK,CAAC;UAbNC,KAAA,CAAAC,MAAM,GAA6B,IAAI;UAEvCD,KAAA,CAAAE,GAAG,GAAoC,IAAI;UAE3CF,KAAA,CAAAG,kBAAkB,GAAG,CAAC;UAEtBH,KAAA,CAAAI,SAAS,GAAG,KAAK;UAQvBJ,KAAI,CAACK,KAAK,GAAG;YACXC,YAAY,EAAE;WACf;;QACH;QAEAV,MAAA,CAAAnB,SAAA,CAAA8B,iBAAiB,GAAjB;UACQ,IAAAC,EAAA,GAAmB,IAAI;YAArBH,KAAK,GAAAG,EAAA,CAAAH,KAAA;YAAEN,KAAK,GAAAS,EAAA,CAAAT,KAAS;UAC7B,IAAI,CAACK,SAAS,GAAG,KAAK;UAEtB,IAAI,CAACT,eAAe,EAAE,EAAE;YACtBI,KAAK,CAACU,gBAAgB,CAAC,4BAA4B,CAAC;YAEpD;;UAGF,IAAI,CAACJ,KAAK,CAACC,YAAY,EAAE;YACvB,IAAI,CAACI,gBAAgB,EAAE;;UAGzB,IAAIX,KAAK,CAACY,QAAQ,IAAI,OAAOZ,KAAK,CAACY,QAAQ,IAAI,UAAU,EAAE;YACzDC,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;;QAE/C,CAAC;QAEDjB,MAAA,CAAAnB,SAAA,CAAAqC,kBAAkB,GAAlB,UAAmBC,SAAsB;UAC/B,IAAAhB,KAAK,GAAK,IAAI,CAAAA,KAAT;UAEb,IAAI,CAACJ,eAAe,EAAE,EAAE;YACtBI,KAAK,CAACU,gBAAgB,CAAC,4BAA4B,CAAC;YAEpD;;UAGF,IAAMO,uBAAuB,GAC3BC,IAAI,CAACC,SAAS,CAACH,SAAS,CAACI,gBAAgB,CAAC,KAC1CF,IAAI,CAACC,SAAS,CAACnB,KAAK,CAACoB,gBAAgB,CAAC;UACxC,IAAMC,uBAAuB,GAC3BH,IAAI,CAACC,SAAS,CAACH,SAAS,CAACM,gBAAgB,CAAC,KAC1CJ,IAAI,CAACC,SAAS,CAACnB,KAAK,CAACsB,gBAAgB,CAAC;UACxC,IAAMC,yBAAyB,GAC7BP,SAAS,CAACQ,kBAAkB,KAAKxB,KAAK,CAACwB,kBAAkB;UAC3D,IAAMC,0BAA0B,GAC9BT,SAAS,CAACU,mBAAmB,KAAK1B,KAAK,CAAC0B,mBAAmB;UAC7D,IACEL,uBAAuB,IACvBE,yBAAyB,IACzBE,0BAA0B,EAC1B;YACA,IAAI,CAACvB,MAAM,GAAG,IAAI;YAClB,IAAI,CAACC,GAAG,GAAG,IAAI;;UAEjB,IAAIc,uBAAuB,IAAII,uBAAuB,EAAE;YACtD,IAAI,CAACM,cAAc,EAAE;YACrB,IAAI,CAAChB,gBAAgB,EAAE;;QAE3B,CAAC;QAEDd,MAAA,CAAAnB,SAAA,CAAAkD,oBAAoB,GAApB;UACE,IAAI,CAACvB,SAAS,GAAG,IAAI;UACrB,IAAI,CAACsB,cAAc,EAAE;QACvB,CAAC;QAEc9B,MAAA,CAAAgC,eAAe,GAA9B,UAA+BC,MAA0B;UACvD,IAAIA,MAAM,EAAE;YACV,IAAIA,MAAM,CAACC,cAAc,IAAID,MAAM,CAACE,cAAc,EAAE;cAClDF,MAAM,CAACC,cAAc,EAAE,CAACE,GAAG,CAAC,UAAAC,KAAK;gBAC/BJ,MAAM,CAACK,WAAW,CAACD,KAAK,CAAC;gBACzBA,KAAK,CAACE,IAAI,EAAE;cACd,CAAC,CAAC;cACFN,MAAM,CAACE,cAAc,EAAE,CAACC,GAAG,CAAC,UAAAC,KAAK;gBAC/BJ,MAAM,CAACK,WAAW,CAACD,KAAK,CAAC;gBACzBA,KAAK,CAACE,IAAI,EAAE;cACd,CAAC,CAAC;aACH,MAAM;cACHN,MAAuC,CAACM,IAAI,EAAE;;;QAGtD,CAAC;QAEOvC,MAAA,CAAAnB,SAAA,CAAAiD,cAAc,GAAtB;UACU,IAAArB,KAAK,GAAK,IAAI,CAAAA,KAAT;UAEb,IAAIA,KAAK,CAACC,YAAY,EAAE;YACtBV,MAAM,CAACgC,eAAe,CAAC,IAAI,CAACC,MAAM,CAAC;YAEnC,IAAIxB,KAAK,CAAC+B,GAAG,EAAE;cACbtD,MAAM,CAACuD,GAAG,CAACC,eAAe,CAACjC,KAAK,CAAC+B,GAAG,CAAC;;;QAG3C,CAAC;QAEDxC,MAAA,CAAAnB,SAAA,CAAA8D,aAAa,GAAb,UAAcC,oBAA2C;UACjD,IAAAhC,EAAA,GAAmB,IAAI;YAArBH,KAAK,GAAAG,EAAA,CAAAH,KAAA;YAAEN,KAAK,GAAAS,EAAA,CAAAT,KAAS;UAE7B,IAAI,CAACM,KAAK,CAACC,YAAY,EAAE,OAAO,IAAI;UAEpC,IAAML,MAAM,GAAG,IAAI,CAACwC,SAAS,CAACD,oBAAoB,CAAC;UACnD,OACEvC,MAAM,IACNA,MAAM,CAACyC,SAAS,CAAC3C,KAAK,CAAC4C,gBAAgB,EAAE5C,KAAK,CAAC6C,iBAAiB,CAAC;QAErE,CAAC;QAEDhD,MAAA,CAAAnB,SAAA,CAAAgE,SAAS,GAAT,UAAUD,oBAA2C;UAC7C,IAAAhC,EAAA,GAAmB,IAAI;YAArBH,KAAK,GAAAG,EAAA,CAAAH,KAAA;YAAEN,KAAK,GAAAS,EAAA,CAAAT,KAAS;UAE7B,IAAI,CAAC,IAAI,CAAC8C,KAAK,EAAE;YACf,OAAO,IAAI;;UAGb,IAAI,CAACxC,KAAK,CAACC,YAAY,IAAI,CAAC,IAAI,CAACuC,KAAK,CAACC,WAAW,EAAE,OAAO,IAAI;UAE/D,IAAI,CAAC,IAAI,CAAC5C,GAAG,EAAE;YACb,IAAI6C,WAAW,GAAG,IAAI,CAACF,KAAK,CAACG,UAAU;YACvC,IAAIC,YAAY,GAAG,IAAI,CAACJ,KAAK,CAACC,WAAW;YACzC,IAAI,CAAC,IAAI,CAAC/C,KAAK,CAACmD,yBAAyB,EAAE;cACzC,IAAMC,WAAW,GAAGJ,WAAW,GAAGE,YAAY;cAE9CF,WAAW,GAAGhD,KAAK,CAACwB,kBAAkB,IAAI,IAAI,CAACsB,KAAK,CAACO,WAAW;cAChEH,YAAY,GAAGF,WAAW,GAAGI,WAAW;cAExC,IACEpD,KAAK,CAAC0B,mBAAmB,IACzBwB,YAAY,GAAGlD,KAAK,CAAC0B,mBAAmB,EACxC;gBACAwB,YAAY,GAAGlD,KAAK,CAAC0B,mBAAmB;gBACxCsB,WAAW,GAAGE,YAAY,GAAGE,WAAW;;;YAI5C,IAAI,CAAClD,MAAM,GAAGoD,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAACrD,MAAM,CAACsD,KAAK,GAAG,CAAAf,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,KAAK,KAAKR,WAAW;YAC/D,IAAI,CAAC9C,MAAM,CAACuD,MAAM,GAAG,CAAAhB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,MAAM,KAAIP,YAAY;YACjE,IAAI,CAAC/C,GAAG,GAAG,IAAI,CAACD,MAAM,CAACwD,UAAU,CAAC,IAAI,CAAC;;UAGnC,IAAAC,EAAA,GAAkB,IAAI;YAApBxD,GAAG,GAAAwD,EAAA,CAAAxD,GAAA;YAAED,MAAM,GAAAyD,EAAA,CAAAzD,MAAS;UAE5B,IAAIC,GAAG,IAAID,MAAM,EAAE;YAEjB;YACAA,MAAM,CAACsD,KAAK,GAAG,CAAAf,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,KAAK,KAAKtD,MAAM,CAACsD,KAAK;YAC3DtD,MAAM,CAACuD,MAAM,GAAG,CAAAhB,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,MAAM,KAAIvD,MAAM,CAACuD,MAAM;YAE7D;YACA,IAAIzD,KAAK,CAAC4D,QAAQ,EAAE;cAClBzD,GAAG,CAAC0D,SAAS,CAAC3D,MAAM,CAACsD,KAAK,EAAE,CAAC,CAAC;cAC9BrD,GAAG,CAAC2D,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;YAGlB3D,GAAG,CAAC4D,qBAAqB,GAAG/D,KAAK,CAACgE,cAAc;YAChD7D,GAAG,CAAC8D,SAAS,CAAC,IAAI,CAACnB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAAL,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,KAAK,KAAItD,MAAM,CAACsD,KAAK,EAAE,CAAAf,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,MAAM,KAAIvD,MAAM,CAACuD,MAAM,CAAC;YAE3H;YACA,IAAIzD,KAAK,CAAC4D,QAAQ,EAAE;cAClBzD,GAAG,CAAC2D,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAChB3D,GAAG,CAAC0D,SAAS,CAAC,CAAC3D,MAAM,CAACsD,KAAK,EAAE,CAAC,CAAC;;;UAInC,OAAOtD,MAAM;QACf,CAAC;QAEOL,MAAA,CAAAnB,SAAA,CAAAiC,gBAAgB,GAAxB;UAAA,IAAAV,KAAA;UACU,IAAAD,KAAK,GAAK,IAAI,CAAAA,KAAT;UAEb,IAAMkE,cAAc,GAAG,SAAAA,CACrB9C,gBAA6D,EAC7DE,gBAA6D;YAE7D,IAAMlC,WAAW,GAA2B;cAC1C0D,KAAK,EAAE,OAAOxB,gBAAgB,KAAK,WAAW,GAAGA,gBAAgB,GAAG;aACrE;YAED,IAAItB,KAAK,CAACmE,KAAK,EAAE;cACf/E,WAAW,CAAC+E,KAAK,GACf,OAAO/C,gBAAgB,KAAK,WAAW,GAAGA,gBAAgB,GAAG,IAAI;;YAGrEnB,KAAI,CAACG,kBAAkB,EAAE;YACzB,IAAMgE,oBAAoB,GAAGnE,KAAI,CAACG,kBAAkB;YAEpDpB,SAAS,CAACC,YAAY,CACnBE,YAAY,CAACC,WAAW,CAAC,CACzBiF,IAAI,CAAC,UAAAvC,MAAM;cACV,IAAI7B,KAAI,CAACI,SAAS,IAAI+D,oBAAoB,KAAKnE,KAAI,CAACG,kBAAkB,EAAE;gBACtEP,MAAM,CAACgC,eAAe,CAACC,MAAM,CAAC;eAC/B,MAAM;gBACL7B,KAAI,CAACqE,eAAe,CAAC,IAAI,EAAExC,MAAM,CAAC;;YAEtC,CAAC,CAAC,CACDyC,KAAK,CAAC,UAAAC,CAAC;cACNvE,KAAI,CAACqE,eAAe,CAACE,CAAC,CAAC;YACzB,CAAC,CAAC;UACN,CAAC;UAED,IAAI,cAAc,IAAIxF,SAAS,EAAE;YAC/BkF,cAAc,CAAClE,KAAK,CAACoB,gBAAgB,EAAEpB,KAAK,CAACsB,gBAAgB,CAAC;WAC/D,MAAM;YACL,IAAMmD,gBAAc,GAAG,SAAAA,CAACC,EAAiB;cAAK,OAAC;gBAAEC,QAAQ,EAAE,CAAC;kBAAEC,QAAQ,EAAEF;gBAAE,CAAE;cAAC,CAAE;YAAjC,CAA2D;YAEzG,IAAMG,sBAAoB,GAAG,SAAAA,CAACC,UAAU;cAC9B,IAAAC,QAAQ,GAAKD,UAAU,CAAAC,QAAf;cAEhB,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;gBAChC,OAAOA,QAAQ;;cAGjB,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;gBAClD,OAAOH,QAAQ,CAAC,CAAC,CAAC;;cAGpB,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACI,KAAK,EAAE;gBAClD,OAAOJ,QAAQ,CAACI,KAAK;;cAGvB,OAAO,IAAI;YACb,CAAC;YAED;YACAC,gBAAgB,CAACC,UAAU,CAAC,UAAAC,OAAO;cACjC,IAAIC,WAAW,GAAkB,IAAI;cACrC,IAAIC,WAAW,GAAkB,IAAI;cAErCF,OAAO,CAACG,OAAO,CAAC,UAACC,MAAwB;gBACvC,IAAIA,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;kBAC3BJ,WAAW,GAAGG,MAAM,CAAChB,EAAE;iBACxB,MAAM,IAAIgB,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;kBAClCH,WAAW,GAAGE,MAAM,CAAChB,EAAE;;cAE3B,CAAC,CAAC;cAEF,IAAMkB,aAAa,GAAGf,sBAAoB,CAAC7E,KAAK,CAACoB,gBAAgB,CAAC;cAClE,IAAIwE,aAAa,EAAE;gBACjBL,WAAW,GAAGK,aAAa;;cAG7B,IAAMC,aAAa,GAAGhB,sBAAoB,CAAC7E,KAAK,CAACsB,gBAAgB,CAAC;cAClE,IAAIuE,aAAa,EAAE;gBACjBL,WAAW,GAAGK,aAAa;;cAG7B3B,cAAc,CACZO,gBAAc,CAACc,WAAW,CAAC,EAC3Bd,gBAAc,CAACe,WAAW,CAAC,CAC5B;YACH,CAAC,CAAC;;QAEN,CAAC;QAEO3F,MAAA,CAAAnB,SAAA,CAAA4F,eAAe,GAAvB,UAAwBwB,GAAG,EAAEhE,MAAoB;UACvC,IAAA9B,KAAK,GAAK,IAAI,CAAAA,KAAT;UAEb,IAAI8F,GAAG,IAAI,CAAChE,MAAM,EAAE;YAClB,IAAI,CAACiE,QAAQ,CAAC;cAAExF,YAAY,EAAE;YAAK,CAAE,CAAC;YACtCP,KAAK,CAACU,gBAAgB,CAACoF,GAAG,CAAC;YAE3B;;UAGF,IAAI,CAAChE,MAAM,GAAGA,MAAM;UAEpB,IAAI;YACF,IAAI,IAAI,CAACgB,KAAK,EAAE;cACd,IAAI,CAACA,KAAK,CAACkD,SAAS,GAAGlE,MAAM;;YAE/B,IAAI,CAACiE,QAAQ,CAAC;cAAExF,YAAY,EAAE;YAAI,CAAE,CAAC;WACtC,CAAC,OAAO0F,KAAK,EAAE;YACd,IAAI,CAACF,QAAQ,CAAC;cACZxF,YAAY,EAAE,IAAI;cAClB8B,GAAG,EAAEtD,MAAM,CAACuD,GAAG,CAAC4D,eAAe,CAACpE,MAAM;aACvC,CAAC;;UAGJ9B,KAAK,CAACmG,WAAW,CAACrE,MAAM,CAAC;QAC3B,CAAC;QAEDjC,MAAA,CAAAnB,SAAA,CAAA0H,MAAM,GAAN;UAAA,IAAAnG,KAAA;UACQ,IAAAQ,EAAA,GAAmB,IAAI;YAArBH,KAAK,GAAAG,EAAA,CAAAH,KAAA;YAAEN,KAAK,GAAAS,EAAA,CAAAT,KAAS;UAG3B,IAAAmE,KAAK,GAgBHnE,KAAK,CAAAmE,KAhBF;YACLhB,yBAAyB,GAevBnD,KAAK,CAAAmD,yBAfkB;YACzBkD,uBAAuB,GAcrBrG,KAAK,CAAAqG,uBAdgB;YACvBF,WAAW,GAaTnG,KAAK,CAAAmG,WAbI;YACXzF,gBAAgB,GAYdV,KAAK,CAAAU,gBAZS;YAChBkC,gBAAgB,GAWd5C,KAAK,CAAA4C,gBAXS;YAChBC,iBAAiB,GAUf7C,KAAK,CAAA6C,iBAVU;YACjBrB,kBAAkB,GAShBxB,KAAK,CAAAwB,kBATW;YAClBE,mBAAmB,GAQjB1B,KAAK,CAAA0B,mBARY;YACnBN,gBAAgB,GAOdpB,KAAK,CAAAoB,gBAPS;YAChBE,gBAAgB,GAMdtB,KAAK,CAAAsB,gBANS;YAChB0C,cAAc,GAKZhE,KAAK,CAAAgE,cALO;YACdJ,QAAQ,GAIN5D,KAAK,CAAA4D,QAJC;YACRD,EAAA,GAGE3D,KAAK,CAAAsG,KAHG;YAAVA,KAAK,GAAA3C,EAAA,cAAG,EAAE,GAAAA,EAAA;YACV/C,QAAQ,GAENZ,KAAK,CAAAY,QAFC;YACL2F,IAAI,GAAAC,MAAA,CACLxG,KAAK,EAjBH,qRAiBL,CADQ;UAGT,IAAMyG,UAAU,GAAG7C,QAAQ,GAAE8C,QAAA,CAAAA,QAAA,KAAMJ,KAAK;YAAEK,SAAS,EAAE,CAAGL,KAAK,CAACK,SAAS,IAAI,EAAE;UAAa,KAAKL,KAAK;UAEpG,IAAMM,aAAa,GAAkB;YACnCpE,aAAa,EAAE,IAAI,CAACA,aAAa,CAACpE,IAAI,CAAC,IAAI;WAC5C;UAED,OACEyI,kCAAA,kBAAAA,kCAAA,oBACEA,kCAAA,2BAAAH,QAAA;YACEI,QAAQ;YACRT,uBAAuB,EAAEA,uBAAuB;YAChDhE,GAAG,EAAE/B,KAAK,CAAC+B,GAAG;YACd0E,KAAK,EAAE,CAAC5C,KAAK;YACb6C,WAAW;YACXC,GAAG,EAAE,SAAAA,IAAG;cACNhH,KAAI,CAAC6C,KAAK,GAAGmE,GAAG;YAClB,CAAC;YACDX,KAAK,EAAEG;UAAU,GACbF,IAAI,EACR,EACD3F,QAAQ,IAAIA,QAAQ,CAACgG,aAAa,CAAC,CACnC;QAEP,CAAC;QA5VM/G,MAAA,CAAAqH,YAAY,GAAG;UACpB/C,KAAK,EAAE,KAAK;UACZkC,uBAAuB,EAAE,KAAK;UAC9BlD,yBAAyB,EAAE,KAAK;UAChCa,cAAc,EAAE,IAAI;UACpBJ,QAAQ,EAAE,KAAK;UACfuC,WAAW,EAAE,SAAAA,CAAA;YAAM,OAAAjH,SAAS;UAAT,CAAS;UAC5BwB,gBAAgB,EAAE,SAAAA,CAAA;YAAM,OAAAxB,SAAS;UAAT,CAAS;UACjC0D,gBAAgB,EAAE,YAAY;UAC9BC,iBAAiB,EAAE;SACpB;QAmVH,OAAAhD,MAAC;OAAA,CA9VmCgH,kCAAA,aAAe;MAA9B;MAAAM,mBAAA,cAAAtH,MAAM;;;;;;;;;;;MC5E3BzD,MAAA,CAAAD,OAAA,GAAAK,iCAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}