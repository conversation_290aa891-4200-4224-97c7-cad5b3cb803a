{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { motion } from 'framer-motion';\nimport { FaTooth, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport Sidebar from './components/Sidebar';\nimport Navbar from './components/Navbar';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _detectionResults$;\n  const [isConnected, setIsConnected] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patientInfo] = useState({\n    name: 'John Doe',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  const [analysisHistory, setAnalysisHistory] = useState([]);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to patient successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = results => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n\n    // Only add to history if there are actual results\n    if (results.length > 0) {\n      // Add to history\n      const newAnalysis = {\n        id: Date.now(),\n        timestamp: new Date().toISOString(),\n        results: results,\n        image: currentCapturedImage,\n        patientId: patientInfo.id,\n        patientName: patientInfo.name\n      };\n      setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\n\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    } else {\n      // Clear results if no mouth detected\n      toast.warning('No mouth detected in the image. Please ensure the patient\\'s mouth is clearly visible.');\n    }\n  };\n  const startAnalysis = () => {\n    if (!currentCapturedImage) {\n      toast.error('No image captured. Please capture an image first.');\n      return;\n    }\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen),\n        isConnected: isConnected\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Intraoral Patient Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Real-time dental analysis and consultation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: startAnalysis,\n                disabled: isAnalyzing,\n                className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50\",\n                children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), isAnalyzing ? 'Analyzing...' : 'Start Analysis']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: patientInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Video Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Detections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: detectionResults.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Analyses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analysisHistory.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Live Video Consultation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n                  onConnectionStatus: handleConnectionStatus,\n                  onStartAnalysis: startAnalysis,\n                  onImageCaptured: handleImageCaptured\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n                  patient: patientInfo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaBrain, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"AI Dental Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n                  onResults: handleDetectionResults,\n                  isAnalyzing: isAnalyzing,\n                  currentImage: currentCapturedImage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Analysis Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n                  results: detectionResults,\n                  isAnalyzing: isAnalyzing,\n                  history: analysisHistory\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-[#0077B6]\",\n                  children: \"Patient Summary & History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Current Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Total Detections\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 277,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-bold text-[#0077B6]\",\n                          children: detectionResults.length\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 278,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Primary Issue\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 281,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-semibold text-gray-900 capitalize\",\n                          children: ((_detectionResults$ = detectionResults[0]) === null || _detectionResults$ === void 0 ? void 0 : _detectionResults$.class.replace('-', ' ')) || 'None'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 282,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700 capitalize\",\n                          children: result.class.replace('-', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 291,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-bold text-[#0077B6]\",\n                          children: [(result.confidence * 100).toFixed(1), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 294,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No current analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Start analysis to see summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: (() => {\n                      const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n                      const recommendations = [];\n                      if (decayCount > 0) {\n                        recommendations.push({\n                          type: 'urgent',\n                          title: 'Immediate Treatment',\n                          description: 'Cavities require immediate dental treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                            className: \"h-4 w-4 text-red-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 332,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (earlyDecayCount > 0) {\n                        recommendations.push({\n                          type: 'warning',\n                          title: 'Preventive Care',\n                          description: 'Schedule follow-up for preventive treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"h-4 w-4 text-yellow-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 341,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (healthyCount > 0) {\n                        recommendations.push({\n                          type: 'positive',\n                          title: 'Good Oral Health',\n                          description: 'Continue regular oral hygiene routine.',\n                          icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-4 w-4 text-green-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 350,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      recommendations.push({\n                        type: 'info',\n                        title: 'Regular Checkup',\n                        description: 'Schedule next checkup within 6 months.',\n                        icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                          className: \"h-4 w-4 text-blue-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 358,\n                          columnNumber: 35\n                        }, this)\n                      });\n                      return recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mr-3 mt-1\",\n                          children: rec.icon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: rec.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 367,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-gray-600\",\n                            children: rec.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 368,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 29\n                      }, this));\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Complete analysis for recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Analysis History\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), analysisHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                    children: [analysisHistory.slice(0, 5).map((historyItem, index) => {\n                      const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\n                      let severity = 'low';\n                      let icon = /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-4 w-4 text-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 400,\n                        columnNumber: 38\n                      }, this);\n                      if (decayCount > 0) {\n                        severity = 'high';\n                        icon = /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                          className: \"h-4 w-4 text-red-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 404,\n                          columnNumber: 36\n                        }, this);\n                      } else if (earlyDecayCount > 0) {\n                        severity = 'medium';\n                        icon = /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                          className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 407,\n                          columnNumber: 36\n                        }, this);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mr-3\",\n                            children: icon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 413,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm font-medium text-gray-900\",\n                              children: [historyItem.results.length, \" detection(s)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 417,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-xs text-gray-500\",\n                              children: new Date(historyItem.timestamp).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 420,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 416,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 412,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\",\n                          children: severity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 31\n                        }, this)]\n                      }, historyItem.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 29\n                      }, this);\n                    }), analysisHistory.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\"+\", analysisHistory.length - 5, \" more analyses\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No analysis history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Perform analyses to build history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"kJJYRNPqWwH1+UFNO1XEwF1fbvU=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ToastContainer", "toast", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaVideo", "FaUser", "FaChartBar", "FaBrain", "FaClock", "FaEye", "FaSearch", "FaHistory", "FaInfoCircle", "FaTimesCircle", "FaExclamationTriangle", "FaCheckCircle", "FaCalendarAlt", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "Sidebar", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "_detectionResults$", "isConnected", "setIsConnected", "sidebarOpen", "setSidebarOpen", "patientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "analysisHistory", "setAnalysisHistory", "handleConnectionStatus", "status", "success", "error", "handleDetectionResults", "results", "length", "newAnalysis", "Date", "now", "timestamp", "toISOString", "image", "patientId", "patientName", "prev", "slice", "classNames", "map", "result", "class", "info", "join", "warning", "startAnalysis", "handleImageCaptured", "imageSrc", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "disabled", "y", "delay", "x", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "patient", "onResults", "currentImage", "history", "replace", "index", "confidence", "toFixed", "decayCount", "filter", "r", "earlyDecayCount", "healthyCount", "recommendations", "push", "type", "title", "description", "icon", "rec", "historyItem", "severity", "toLocaleDateString", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt } from 'react-icons/fa';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport Sidebar from './components/Sidebar';\r\nimport Navbar from './components/Navbar';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [patientInfo] = useState({\r\n    name: '<PERSON>',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\r\n  const [analysisHistory, setAnalysisHistory] = useState([]);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to patient successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    // Only add to history if there are actual results\r\n    if (results.length > 0) {\r\n      // Add to history\r\n      const newAnalysis = {\r\n        id: Date.now(),\r\n        timestamp: new Date().toISOString(),\r\n        results: results,\r\n        image: currentCapturedImage,\r\n        patientId: patientInfo.id,\r\n        patientName: patientInfo.name\r\n      };\r\n      \r\n      setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\r\n      \r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    } else {\r\n      // Clear results if no mouth detected\r\n      toast.warning('No mouth detected in the image. Please ensure the patient\\'s mouth is clearly visible.');\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    if (!currentCapturedImage) {\r\n      toast.error('No image captured. Please capture an image first.');\r\n      return;\r\n    }\r\n    \r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  const handleImageCaptured = (imageSrc) => {\r\n    setCurrentCapturedImage(imageSrc);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isConnected={isConnected} />\r\n\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\r\n                    Intraoral Patient Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Real-time dental analysis and consultation</p>\r\n                </div>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  onClick={startAnalysis}\r\n                  disabled={isAnalyzing}\r\n                  className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50\"\r\n                >\r\n                  <FaBrain className=\"h-5 w-5 mr-2\" />\r\n                  {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}\r\n                </motion.button>\r\n              </div>\r\n\r\n              {/* Stats Cards */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.1 }}\r\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n              >\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaUser className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Patient</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{patientInfo.name}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaVideo className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Video Status</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isConnected ? 'Live' : 'Offline'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaEye className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Detections</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{detectionResults.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaHistory className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Total Analyses</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analysisHistory.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Main Content - Split Layout */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\">\r\n                {/* Live Video Section - Larger */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaVideo className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Live Video Consultation</h2>\r\n                  </div>\r\n                  <VideoCall\r\n                    onConnectionStatus={handleConnectionStatus}\r\n                    onStartAnalysis={startAnalysis}\r\n                    onImageCaptured={handleImageCaptured}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Patient Info Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: 20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaUser className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Information</h2>\r\n                  </div>\r\n                  <PatientInfo patient={patientInfo} />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Analysis and Results Section */}\r\n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\r\n                {/* AI Analysis Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaBrain className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">AI Dental Analysis</h2>\r\n                  </div>\r\n                  <YOLODetection\r\n                    onResults={handleDetectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    currentImage={currentCapturedImage}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Results Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaChartBar className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Analysis Results</h2>\r\n                  </div>\r\n                  <AnalysisResults\r\n                    results={detectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    history={analysisHistory}\r\n                  />\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Static Patient Summary Section - Always Visible */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4 }}\r\n                className=\"mt-8\"\r\n              >\r\n                <div className=\"flex items-center mb-6\">\r\n                  <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                    <FaHistory className=\"h-5 w-5\" />\r\n                  </div>\r\n                  <h2 className=\"text-2xl font-bold text-[#0077B6]\">Patient Summary & History</h2>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n                  {/* Summary Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaChartBar className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Current Summary</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-4\">\r\n                        <div className=\"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\">\r\n                          <div className=\"flex items-center justify-between mb-2\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Total Detections</span>\r\n                            <span className=\"text-lg font-bold text-[#0077B6]\">{detectionResults.length}</span>\r\n                          </div>\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-sm font-medium text-gray-700\">Primary Issue</span>\r\n                            <span className=\"text-sm font-semibold text-gray-900 capitalize\">\r\n                              {detectionResults[0]?.class.replace('-', ' ') || 'None'}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"space-y-2\">\r\n                          {detectionResults.map((result, index) => (\r\n                            <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\r\n                              <span className=\"text-sm font-medium text-gray-700 capitalize\">\r\n                                {result.class.replace('-', ' ')}\r\n                              </span>\r\n                              <span className=\"text-sm font-bold text-[#0077B6]\">\r\n                                {(result.confidence * 100).toFixed(1)}%\r\n                              </span>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaSearch className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No current analysis</p>\r\n                        <p className=\"text-gray-500 text-sm\">Start analysis to see summary</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Recommendations Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaInfoCircle className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recommendations</h3>\r\n                    </div>\r\n                    \r\n                    {detectionResults.length > 0 ? (\r\n                      <div className=\"space-y-3\">\r\n                        {(() => {\r\n                          const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n                          const recommendations = [];\r\n\r\n                          if (decayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'urgent',\r\n                              title: 'Immediate Treatment',\r\n                              description: 'Cavities require immediate dental treatment.',\r\n                              icon: <FaTimesCircle className=\"h-4 w-4 text-red-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (earlyDecayCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'warning',\r\n                              title: 'Preventive Care',\r\n                              description: 'Schedule follow-up for preventive treatment.',\r\n                              icon: <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />\r\n                            });\r\n                          }\r\n\r\n                          if (healthyCount > 0) {\r\n                            recommendations.push({\r\n                              type: 'positive',\r\n                              title: 'Good Oral Health',\r\n                              description: 'Continue regular oral hygiene routine.',\r\n                              icon: <FaCheckCircle className=\"h-4 w-4 text-green-500\" />\r\n                            });\r\n                          }\r\n\r\n                          recommendations.push({\r\n                            type: 'info',\r\n                            title: 'Regular Checkup',\r\n                            description: 'Schedule next checkup within 6 months.',\r\n                            icon: <FaCalendarAlt className=\"h-4 w-4 text-blue-500\" />\r\n                          });\r\n\r\n                          return recommendations.map((rec, index) => (\r\n                            <div key={index} className=\"flex items-start p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"mr-3 mt-1\">\r\n                                {rec.icon}\r\n                              </div>\r\n                              <div>\r\n                                <h4 className=\"text-sm font-semibold text-gray-900\">{rec.title}</h4>\r\n                                <p className=\"text-xs text-gray-600\">{rec.description}</p>\r\n                              </div>\r\n                            </div>\r\n                          ));\r\n                        })()}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaInfoCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No recommendations</p>\r\n                        <p className=\"text-gray-500 text-sm\">Complete analysis for recommendations</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Analysis History Card */}\r\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                    <div className=\"flex items-center mb-4\">\r\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                        <FaHistory className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis History</h3>\r\n                    </div>\r\n                    \r\n                    {analysisHistory.length > 0 ? (\r\n                      <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n                        {analysisHistory.slice(0, 5).map((historyItem, index) => {\r\n                          const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\r\n                          const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\r\n                          const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\r\n                          \r\n                          let severity = 'low';\r\n                          let icon = <FaCheckCircle className=\"h-4 w-4 text-green-500\" />;\r\n                          \r\n                          if (decayCount > 0) {\r\n                            severity = 'high';\r\n                            icon = <FaTimesCircle className=\"h-4 w-4 text-red-500\" />;\r\n                          } else if (earlyDecayCount > 0) {\r\n                            severity = 'medium';\r\n                            icon = <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />;\r\n                          }\r\n\r\n                          return (\r\n                            <div key={historyItem.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                              <div className=\"flex items-center\">\r\n                                <div className=\"mr-3\">\r\n                                  {icon}\r\n                                </div>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium text-gray-900\">\r\n                                    {historyItem.results.length} detection(s)\r\n                                  </p>\r\n                                  <p className=\"text-xs text-gray-500\">\r\n                                    {new Date(historyItem.timestamp).toLocaleDateString()}\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              <span className=\"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\">\r\n                                {severity}\r\n                              </span>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                        \r\n                        {analysisHistory.length > 5 && (\r\n                          <div className=\"text-center pt-2\">\r\n                            <p className=\"text-xs text-gray-500\">\r\n                              +{analysisHistory.length - 5} more analyses\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"text-center py-8\">\r\n                        <FaHistory className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n                        <p className=\"text-gray-600 font-medium mb-2\">No analysis history</p>\r\n                        <p className=\"text-gray-500 text-sm\">Perform analyses to build history</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,QAAQ,gBAAgB;AACrM,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,WAAW,CAAC,GAAGjC,QAAQ,CAAC;IAC7BkC,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM8C,sBAAsB,GAAIC,MAAM,IAAK;IACzCjB,cAAc,CAACiB,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACV7C,KAAK,CAAC8C,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACL9C,KAAK,CAAC+C,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;IAC1CZ,mBAAmB,CAACY,OAAO,CAAC;IAC5BV,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIU,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB;MACA,MAAMC,WAAW,GAAG;QAClBlB,EAAE,EAAEmB,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;QACnCN,OAAO,EAAEA,OAAO;QAChBO,KAAK,EAAEhB,oBAAoB;QAC3BiB,SAAS,EAAE1B,WAAW,CAACE,EAAE;QACzByB,WAAW,EAAE3B,WAAW,CAACC;MAC3B,CAAC;MAEDW,kBAAkB,CAACgB,IAAI,IAAI,CAACR,WAAW,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjE,MAAMC,UAAU,GAAGZ,OAAO,CAACa,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtDhE,KAAK,CAACiE,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD,CAAC,MAAM;MACL;MACAlE,KAAK,CAACmE,OAAO,CAAC,wFAAwF,CAAC;IACzG;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC5B,oBAAoB,EAAE;MACzBxC,KAAK,CAAC+C,KAAK,CAAC,mDAAmD,CAAC;MAChE;IACF;IAEAR,cAAc,CAAC,IAAI,CAAC;IACpBvC,KAAK,CAACiE,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,MAAMI,mBAAmB,GAAIC,QAAQ,IAAK;IACxC7B,uBAAuB,CAAC6B,QAAQ,CAAC;EACnC,CAAC;EAED,oBACE/C,OAAA;IAAKgD,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCjD,OAAA,CAACH,OAAO;MAACqD,MAAM,EAAE5C,WAAY;MAAC6C,SAAS,EAAE5C;IAAe;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3DvD,OAAA;MAAKgD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDjD,OAAA,CAACF,MAAM;QAAC0D,aAAa,EAAEA,CAAA,KAAMjD,cAAc,CAAC,CAACD,WAAW,CAAE;QAACF,WAAW,EAAEA;MAAY;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvFvD,OAAA;QAAMgD,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGjD,OAAA;UAAKgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCjD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAG9BjD,OAAA;cAAKgD,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FjD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIgD,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLvD,OAAA;kBAAGgD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNvD,OAAA,CAACtB,MAAM,CAACqF,MAAM;gBACZC,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAC1BE,OAAO,EAAEtB,aAAc;gBACvBuB,QAAQ,EAAErD,WAAY;gBACtBiC,SAAS,EAAC,wNAAwN;gBAAAC,QAAA,gBAElOjD,OAAA,CAACjB,OAAO;kBAACiE,SAAS,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACnCxC,WAAW,GAAG,cAAc,GAAG,gBAAgB;cAAA;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGNvD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErEjD,OAAA;gBAAKgD,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIjD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjD,OAAA;oBAAKgD,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEjD,OAAA,CAACnB,MAAM;sBAACmE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNvD,OAAA;oBAAKgD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjD,OAAA;sBAAIgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9DvD,OAAA;sBAAGgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEzC,WAAW,CAACC;oBAAI;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvD,OAAA;gBAAKgD,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIjD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjD,OAAA;oBAAKgD,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEjD,OAAA,CAACpB,OAAO;sBAACoE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNvD,OAAA;oBAAKgD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjD,OAAA;sBAAIgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEvD,OAAA;sBAAGgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE7C,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvD,OAAA;gBAAKgD,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIjD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjD,OAAA;oBAAKgD,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEjD,OAAA,CAACf,KAAK;sBAAC+D,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACNvD,OAAA;oBAAKgD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjD,OAAA;sBAAIgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjEvD,OAAA;sBAAGgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEpC,gBAAgB,CAACc;oBAAM;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENvD,OAAA;gBAAKgD,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIjD,OAAA;kBAAKgD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCjD,OAAA;oBAAKgD,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEjD,OAAA,CAACb,SAAS;sBAAC6D,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNvD,OAAA;oBAAKgD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjD,OAAA;sBAAIgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEvD,OAAA;sBAAGgD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE9B,eAAe,CAACQ;oBAAM;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbvD,OAAA;cAAKgD,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDjD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3IjD,OAAA;kBAAKgD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjD,OAAA;oBAAKgD,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EjD,OAAA,CAACpB,OAAO;sBAACoE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNvD,OAAA;oBAAIgD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNvD,OAAA,CAACP,SAAS;kBACR+E,kBAAkB,EAAEnD,sBAAuB;kBAC3CoD,eAAe,EAAE5B,aAAc;kBAC/B6B,eAAe,EAAE5B;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbvD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HjD,OAAA;kBAAKgD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjD,OAAA;oBAAKgD,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EjD,OAAA,CAACnB,MAAM;sBAACmE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNvD,OAAA;oBAAIgD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNvD,OAAA,CAACL,WAAW;kBAACgF,OAAO,EAAEnE;gBAAY;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA;cAAKgD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDjD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HjD,OAAA;kBAAKgD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjD,OAAA;oBAAKgD,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EjD,OAAA,CAACjB,OAAO;sBAACiE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNvD,OAAA;oBAAIgD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNvD,OAAA,CAACN,aAAa;kBACZkF,SAAS,EAAEnD,sBAAuB;kBAClCV,WAAW,EAAEA,WAAY;kBACzB8D,YAAY,EAAE5D;gBAAqB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbvD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HjD,OAAA;kBAAKgD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjD,OAAA;oBAAKgD,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EjD,OAAA,CAAClB,UAAU;sBAACkE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNvD,OAAA;oBAAIgD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNvD,OAAA,CAACJ,eAAe;kBACd8B,OAAO,EAAEb,gBAAiB;kBAC1BE,WAAW,EAAEA,WAAY;kBACzB+D,OAAO,EAAE3D;gBAAgB;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNvD,OAAA,CAACtB,MAAM,CAAC+E,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBjD,OAAA;gBAAKgD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCjD,OAAA;kBAAKgD,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1EjD,OAAA,CAACb,SAAS;oBAAC6D,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNvD,OAAA;kBAAIgD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eAENvD,OAAA;gBAAKgD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAEpDjD,OAAA;kBAAKgD,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIjD,OAAA;oBAAKgD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCjD,OAAA;sBAAKgD,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EjD,OAAA,CAAClB,UAAU;wBAACkE,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACNvD,OAAA;sBAAIgD,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAEL1C,gBAAgB,CAACc,MAAM,GAAG,CAAC,gBAC1B3B,OAAA;oBAAKgD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBjD,OAAA;sBAAKgD,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,gBACvDjD,OAAA;wBAAKgD,SAAS,EAAC,wCAAwC;wBAAAC,QAAA,gBACrDjD,OAAA;0BAAMgD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3EvD,OAAA;0BAAMgD,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAAEpC,gBAAgB,CAACc;wBAAM;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,eACNvD,OAAA;wBAAKgD,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDjD,OAAA;0BAAMgD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxEvD,OAAA;0BAAMgD,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,EAC7D,EAAA9C,kBAAA,GAAAU,gBAAgB,CAAC,CAAC,CAAC,cAAAV,kBAAA,uBAAnBA,kBAAA,CAAqBsC,KAAK,CAACsC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;wBAAM;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENvD,OAAA;sBAAKgD,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvBpC,gBAAgB,CAAC0B,GAAG,CAAC,CAACC,MAAM,EAAEwC,KAAK,kBAClChF,OAAA;wBAAiBgD,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACnFjD,OAAA;0BAAMgD,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,EAC3DT,MAAM,CAACC,KAAK,CAACsC,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACPvD,OAAA;0BAAMgD,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,GAC/C,CAACT,MAAM,CAACyC,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;wBAAA;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GANCyB,KAAK;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENvD,OAAA;oBAAKgD,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjD,OAAA,CAACd,QAAQ;sBAAC8D,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7DvD,OAAA;sBAAGgD,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEvD,OAAA;sBAAGgD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNvD,OAAA;kBAAKgD,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIjD,OAAA;oBAAKgD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCjD,OAAA;sBAAKgD,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EjD,OAAA,CAACZ,YAAY;wBAAC4D,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACNvD,OAAA;sBAAIgD,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAEL1C,gBAAgB,CAACc,MAAM,GAAG,CAAC,gBAC1B3B,OAAA;oBAAKgD,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvB,CAAC,MAAM;sBACN,MAAMkC,UAAU,GAAGtE,gBAAgB,CAACuE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,aAAa,CAAC,CAACd,MAAM;sBACjF,MAAM2D,eAAe,GAAGzE,gBAAgB,CAACuE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,aAAa,CAAC,CAACd,MAAM;sBACtF,MAAM4D,YAAY,GAAG1E,gBAAgB,CAACuE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,eAAe,CAAC,CAACd,MAAM;sBACrF,MAAM6D,eAAe,GAAG,EAAE;sBAE1B,IAAIL,UAAU,GAAG,CAAC,EAAE;wBAClBK,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,QAAQ;0BACdC,KAAK,EAAE,qBAAqB;0BAC5BC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE7F,OAAA,CAACX,aAAa;4BAAC2D,SAAS,EAAC;0BAAsB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACzD,CAAC,CAAC;sBACJ;sBAEA,IAAI+B,eAAe,GAAG,CAAC,EAAE;wBACvBE,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,iBAAiB;0BACxBC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE7F,OAAA,CAACV,qBAAqB;4BAAC0D,SAAS,EAAC;0BAAyB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACpE,CAAC,CAAC;sBACJ;sBAEA,IAAIgC,YAAY,GAAG,CAAC,EAAE;wBACpBC,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,UAAU;0BAChBC,KAAK,EAAE,kBAAkB;0BACzBC,WAAW,EAAE,wCAAwC;0BACrDC,IAAI,eAAE7F,OAAA,CAACT,aAAa;4BAACyD,SAAS,EAAC;0BAAwB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAC3D,CAAC,CAAC;sBACJ;sBAEAiC,eAAe,CAACC,IAAI,CAAC;wBACnBC,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,iBAAiB;wBACxBC,WAAW,EAAE,wCAAwC;wBACrDC,IAAI,eAAE7F,OAAA,CAACR,aAAa;0BAACwD,SAAS,EAAC;wBAAuB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAC1D,CAAC,CAAC;sBAEF,OAAOiC,eAAe,CAACjD,GAAG,CAAC,CAACuD,GAAG,EAAEd,KAAK,kBACpChF,OAAA;wBAAiBgD,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACrEjD,OAAA;0BAAKgD,SAAS,EAAC,WAAW;0BAAAC,QAAA,EACvB6C,GAAG,CAACD;wBAAI;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACNvD,OAAA;0BAAAiD,QAAA,gBACEjD,OAAA;4BAAIgD,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAE6C,GAAG,CAACH;0BAAK;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpEvD,OAAA;4BAAGgD,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE6C,GAAG,CAACF;0BAAW;4BAAAxC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC;sBAAA,GAPEyB,KAAK;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQV,CACN,CAAC;oBACJ,CAAC,EAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAENvD,OAAA;oBAAKgD,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjD,OAAA,CAACZ,YAAY;sBAAC4D,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjEvD,OAAA;sBAAGgD,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEvD,OAAA;sBAAGgD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAqC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNvD,OAAA;kBAAKgD,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIjD,OAAA;oBAAKgD,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCjD,OAAA;sBAAKgD,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EjD,OAAA,CAACb,SAAS;wBAAC6D,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACNvD,OAAA;sBAAIgD,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,EAELpC,eAAe,CAACQ,MAAM,GAAG,CAAC,gBACzB3B,OAAA;oBAAKgD,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAChD9B,eAAe,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACE,GAAG,CAAC,CAACwD,WAAW,EAAEf,KAAK,KAAK;sBACvD,MAAMG,UAAU,GAAGY,WAAW,CAACrE,OAAO,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,aAAa,CAAC,CAACd,MAAM;sBACpF,MAAM2D,eAAe,GAAGS,WAAW,CAACrE,OAAO,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,aAAa,CAAC,CAACd,MAAM;sBACzF,MAAM4D,YAAY,GAAGQ,WAAW,CAACrE,OAAO,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,KAAK,eAAe,CAAC,CAACd,MAAM;sBAExF,IAAIqE,QAAQ,GAAG,KAAK;sBACpB,IAAIH,IAAI,gBAAG7F,OAAA,CAACT,aAAa;wBAACyD,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;sBAE/D,IAAI4B,UAAU,GAAG,CAAC,EAAE;wBAClBa,QAAQ,GAAG,MAAM;wBACjBH,IAAI,gBAAG7F,OAAA,CAACX,aAAa;0BAAC2D,SAAS,EAAC;wBAAsB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAC3D,CAAC,MAAM,IAAI+B,eAAe,GAAG,CAAC,EAAE;wBAC9BU,QAAQ,GAAG,QAAQ;wBACnBH,IAAI,gBAAG7F,OAAA,CAACV,qBAAqB;0BAAC0D,SAAS,EAAC;wBAAyB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBACtE;sBAEA,oBACEvD,OAAA;wBAA0BgD,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,gBAC/FjD,OAAA;0BAAKgD,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChCjD,OAAA;4BAAKgD,SAAS,EAAC,MAAM;4BAAAC,QAAA,EAClB4C;0BAAI;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACNvD,OAAA;4BAAAiD,QAAA,gBACEjD,OAAA;8BAAGgD,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAC7C8C,WAAW,CAACrE,OAAO,CAACC,MAAM,EAAC,eAC9B;4BAAA;8BAAAyB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,eACJvD,OAAA;8BAAGgD,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EACjC,IAAIpB,IAAI,CAACkE,WAAW,CAAChE,SAAS,CAAC,CAACkE,kBAAkB,CAAC;4BAAC;8BAAA7C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvD,OAAA;0BAAMgD,SAAS,EAAC,oFAAoF;0BAAAC,QAAA,EACjG+C;wBAAQ;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GAhBCwC,WAAW,CAACrF,EAAE;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBnB,CAAC;oBAEV,CAAC,CAAC,EAEDpC,eAAe,CAACQ,MAAM,GAAG,CAAC,iBACzB3B,OAAA;sBAAKgD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BjD,OAAA;wBAAGgD,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC9B,eAAe,CAACQ,MAAM,GAAG,CAAC,EAAC,gBAC/B;sBAAA;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENvD,OAAA;oBAAKgD,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BjD,OAAA,CAACb,SAAS;sBAAC6D,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9DvD,OAAA;sBAAGgD,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEvD,OAAA;sBAAGgD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENvD,OAAA,CAACxB,cAAc;MACb0H,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACrD,EAAA,CAvcQD,GAAG;AAAA2G,EAAA,GAAH3G,GAAG;AAycZ,eAAeA,GAAG;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}