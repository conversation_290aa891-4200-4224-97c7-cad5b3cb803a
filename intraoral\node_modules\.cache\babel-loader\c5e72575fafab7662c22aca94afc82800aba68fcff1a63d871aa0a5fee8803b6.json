{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [patientInfo, setPatientInfo] = useState({\n    name: '<PERSON>',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to dentist successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = results => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"app-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83E\\uDDB7 Intraoral Patient Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"connection-status\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `status-indicator ${isConnected ? 'status-online' : 'status-offline'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), isConnected ? 'Connected to Dentist' : 'Disconnected']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCF9 Live Video Consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n            onConnectionStatus: handleConnectionStatus,\n            onStartAnalysis: startAnalysis,\n            onImageCaptured: handleImageCaptured\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDC64 Patient Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n            patient: patientInfo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDD0D AI Dental Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n            onResults: handleDetectionResults,\n            isAnalyzing: isAnalyzing,\n            currentImage: currentCapturedImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCCA Analysis Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n            results: detectionResults,\n            isAnalyzing: isAnalyzing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"uWyDeOcdKrAN6UfbTi9fgjAQcRY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ToastContainer", "toast", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "jsxDEV", "_jsxDEV", "App", "_s", "isConnected", "setIsConnected", "patientInfo", "setPatientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "handleConnectionStatus", "status", "success", "error", "handleDetectionResults", "results", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "handleImageCaptured", "imageSrc", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "patient", "onResults", "currentImage", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [patientInfo, setPatientInfo] = useState({\r\n    name: '<PERSON>',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to dentist successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    if (results.length > 0) {\r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  const handleImageCaptured = (imageSrc) => {\r\n    setCurrentCapturedImage(imageSrc);\r\n  };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      <div className=\"container\">\r\n        <header className=\"app-header\">\r\n          <h1>🦷 Intraoral Patient Dashboard</h1>\r\n          <div className=\"connection-status\">\r\n            <span className={`status-indicator ${isConnected ? 'status-online' : 'status-offline'}`}></span>\r\n            {isConnected ? 'Connected to Dentist' : 'Disconnected'}\r\n          </div>\r\n        </header>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div className=\"card\">\r\n            <h2>📹 Live Video Consultation</h2>\r\n            <VideoCall \r\n              onConnectionStatus={handleConnectionStatus}\r\n              onStartAnalysis={startAnalysis}\r\n              onImageCaptured={handleImageCaptured}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h2>👤 Patient Information</h2>\r\n            <PatientInfo patient={patientInfo} />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div className=\"card\">\r\n            <h2>🔍 AI Dental Analysis</h2>\r\n            <YOLODetection \r\n              onResults={handleDetectionResults}\r\n              isAnalyzing={isAnalyzing}\r\n              currentImage={currentCapturedImage}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h2>📊 Analysis Results</h2>\r\n            <AnalysisResults \r\n              results={detectionResults}\r\n              isAnalyzing={isAnalyzing}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC7CgB,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAEtE,MAAM0B,sBAAsB,GAAIC,MAAM,IAAK;IACzCd,cAAc,CAACc,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACVxB,KAAK,CAACyB,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACLzB,KAAK,CAAC0B,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;IAC1CV,mBAAmB,CAACU,OAAO,CAAC;IAC5BR,cAAc,CAAC,KAAK,CAAC;IAErB,IAAIQ,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGF,OAAO,CAACG,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtDjC,KAAK,CAACkC,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BhB,cAAc,CAAC,IAAI,CAAC;IACpBpB,KAAK,CAACkC,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;IACxChB,uBAAuB,CAACgB,QAAQ,CAAC;EACnC,CAAC;EAED,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBlC,OAAA;MAAKiC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlC,OAAA;QAAQiC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAC5BlC,OAAA;UAAAkC,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCtC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAMiC,SAAS,EAAE,oBAAoB9B,WAAW,GAAG,eAAe,GAAG,gBAAgB;UAAG;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC/FnC,WAAW,GAAG,sBAAsB,GAAG,cAAc;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETtC,OAAA;QAAKiC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlC,OAAA;UAAKiC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlC,OAAA;YAAAkC,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCtC,OAAA,CAACL,SAAS;YACR4C,kBAAkB,EAAEtB,sBAAuB;YAC3CuB,eAAe,EAAEV,aAAc;YAC/BW,eAAe,EAAEV;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlC,OAAA;YAAAkC,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BtC,OAAA,CAACH,WAAW;YAAC6C,OAAO,EAAErC;UAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtC,OAAA;QAAKiC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlC,OAAA;UAAKiC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlC,OAAA;YAAAkC,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BtC,OAAA,CAACJ,aAAa;YACZ+C,SAAS,EAAEtB,sBAAuB;YAClCR,WAAW,EAAEA,WAAY;YACzB+B,YAAY,EAAE7B;UAAqB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlC,OAAA;YAAAkC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BtC,OAAA,CAACF,eAAe;YACdwB,OAAO,EAAEX,gBAAiB;YAC1BE,WAAW,EAAEA;UAAY;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtC,OAAA,CAACP,cAAc;MACboD,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACpC,EAAA,CArGQD,GAAG;AAAAsD,EAAA,GAAHtD,GAAG;AAuGZ,eAAeA,GAAG;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}