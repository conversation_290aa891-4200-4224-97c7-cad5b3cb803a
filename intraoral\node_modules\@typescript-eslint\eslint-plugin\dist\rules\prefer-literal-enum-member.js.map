{"version": 3, "file": "prefer-literal-enum-member.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-literal-enum-member.ts"], "names": [], "mappings": ";;AAAA,oDAA0D;AAE1D,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,KAAK;SAC5B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,kFAAkF;SAC/F;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,uBAAuB,EAAE;wBACvB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,uBAAuB,EAAE,KAAK;SAC/B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,uBAAuB,EAAE,CAAC;QAC3C,OAAO;YACL,YAAY,CAAC,IAAI;gBACf,wFAAwF;gBACxF,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE;oBAC5B,OAAO;iBACR;gBACD,kBAAkB;gBAClB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;oBACpD,OAAO;iBACR;gBACD,sCAAsC;gBACtC,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EACzC;oBACA,OAAO;iBACR;gBACD,YAAY;gBACZ,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACzD,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;wBAC7C,CAAC,uBAAuB,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,EACjE;oBACA,OAAO;iBACR;gBAED,IACE,uBAAuB;oBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACzD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAC1B;oBACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACrD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EACtD;oBACA,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,EAAE;oBACb,SAAS,EAAE,YAAY;iBACxB,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}