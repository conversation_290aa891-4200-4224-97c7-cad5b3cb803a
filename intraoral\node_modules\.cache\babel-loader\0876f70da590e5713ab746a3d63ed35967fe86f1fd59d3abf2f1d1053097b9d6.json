{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': '🦷',\n    'early-decay': '⚠️',\n    'healthy tooth': '✅'\n  };\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n  useEffect(() => {\n    // Simulate receiving detection results\n    if (isAnalyzing) {\n      const mockResults = generateMockResults();\n      setTimeout(() => {\n        handleDetectionResults(mockResults);\n      }, 2000);\n    }\n  }, [isAnalyzing]);\n  const generateMockResults = () => {\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\n    const numDetections = Math.floor(Math.random() * 3) + 1;\n    const results = [];\n    for (let i = 0; i < numDetections; i++) {\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\n      results.push({\n        class: randomClass,\n        confidence: Math.random() * 0.4 + 0.6,\n        // 60-100% confidence\n        bbox: {\n          x: Math.random() * 0.8,\n          y: Math.random() * 0.8,\n          width: Math.random() * 0.3 + 0.1,\n          height: Math.random() * 0.3 + 0.1\n        }\n      });\n    }\n    return results;\n  };\n  const handleDetectionResults = results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  };\n  const generateAnnotatedImage = results => {\n    // Create a canvas to draw the annotated image\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = 640;\n    canvas.height = 480;\n\n    // If we have a current image, use it as background\n    if (currentImage) {\n      const img = new Image();\n      img.onload = () => {\n        // Draw the original image\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n        // Draw detection boxes on top\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n\n        // Set the annotated image\n        setAnnotatedImage(canvas.toDataURL());\n      };\n      img.src = currentImage;\n    } else {\n      // Fallback to placeholder if no image\n      ctx.fillStyle = '#f5f5f5';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Draw detection boxes\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n      setAnnotatedImage(canvas.toDataURL());\n    }\n  };\n  const drawDetectionBoxes = (ctx, results, canvasWidth, canvasHeight) => {\n    results.forEach((result, index) => {\n      const {\n        bbox\n      } = result;\n      const x = bbox.x * canvasWidth;\n      const y = bbox.y * canvasHeight;\n      const width = bbox.width * canvasWidth;\n      const height = bbox.height * canvasHeight;\n\n      // Draw bounding box\n      ctx.strokeStyle = classColors[result.class];\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\n      const labelWidth = ctx.measureText(label).width + 20;\n      const labelHeight = 25;\n      ctx.fillStyle = classColors[result.class];\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.font = 'bold 14px Arial';\n      ctx.fillText(label, x + 10, y - 8);\n    });\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaBrain, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \"Model: best.pt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Analyzing image with YOLOv8...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this), annotatedImage && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Annotated Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\",\n          className: \"w-full h-auto max-h-64 object-contain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Detection Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mr-3\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaClock, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Detections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), detectionHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: new Date(detection.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n              children: [detection.results.length, \" detection(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n            children: detection.results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-2 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center text-sm font-medium text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: classIcons[result.class]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 25\n                }, this), result.class.replace('-', ' ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold\",\n                style: {\n                  color: getConfidenceColor(result.confidence)\n                },\n                children: [(result.confidence * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this)]\n        }, detection.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No detections yet. Capture images to start AI analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"vBN12p95qDZCWRquH0V3SwSbCs4=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "motion", "FaBrain", "FaChartBar", "FaClock", "FaImage", "FaRobot", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "classColors", "classIcons", "console", "log", "mockResults", "generateMockResults", "setTimeout", "handleDetectionResults", "classes", "numDetections", "Math", "floor", "random", "results", "i", "randomClass", "length", "push", "class", "confidence", "bbox", "x", "y", "width", "height", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "for<PERSON>ach", "result", "generateAnnotatedImage", "canvas", "document", "createElement", "ctx", "getContext", "img", "Image", "onload", "drawImage", "drawDetectionBoxes", "toDataURL", "src", "fillStyle", "fillRect", "canvasWidth", "canvasHeight", "index", "strokeStyle", "lineWidth", "strokeRect", "label", "toFixed", "labelWidth", "measureText", "labelHeight", "font", "fillText", "getConfidenceColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "scale", "alt", "Object", "entries", "map", "count", "whileHover", "replace", "detection", "toLocaleTimeString", "style", "color", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport { motion } from 'framer-motion';\r\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot } from 'react-icons/fa';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ff6b6b',\r\n    'early-decay': '#ffd43b',\r\n    'healthy tooth': '#51cf66'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': '🦷',\r\n    'early-decay': '⚠️',\r\n    'healthy tooth': '✅'\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Update current image when prop changes\r\n    if (currentImage) {\r\n      console.log('📸 Received new captured image for analysis');\r\n    }\r\n  }, [currentImage]);\r\n\r\n  useEffect(() => {\r\n    // Simulate receiving detection results\r\n    if (isAnalyzing) {\r\n      const mockResults = generateMockResults();\r\n      setTimeout(() => {\r\n        handleDetectionResults(mockResults);\r\n      }, 2000);\r\n    }\r\n  }, [isAnalyzing]);\r\n\r\n  const generateMockResults = () => {\r\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\r\n    const numDetections = Math.floor(Math.random() * 3) + 1;\r\n    const results = [];\r\n\r\n    for (let i = 0; i < numDetections; i++) {\r\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\r\n      results.push({\r\n        class: randomClass,\r\n        confidence: Math.random() * 0.4 + 0.6, // 60-100% confidence\r\n        bbox: {\r\n          x: Math.random() * 0.8,\r\n          y: Math.random() * 0.8,\r\n          width: Math.random() * 0.3 + 0.1,\r\n          height: Math.random() * 0.3 + 0.1\r\n        }\r\n      });\r\n    }\r\n\r\n    return results;\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n    \r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  };\r\n\r\n  const generateAnnotatedImage = (results) => {\r\n    // Create a canvas to draw the annotated image\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n    \r\n    // Set canvas size\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n\r\n    // If we have a current image, use it as background\r\n    if (currentImage) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        // Draw the original image\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n        \r\n        // Draw detection boxes on top\r\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n        \r\n        // Set the annotated image\r\n        setAnnotatedImage(canvas.toDataURL());\r\n      };\r\n      img.src = currentImage;\r\n    } else {\r\n      // Fallback to placeholder if no image\r\n      ctx.fillStyle = '#f5f5f5';\r\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n      \r\n      // Draw detection boxes\r\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n      \r\n      setAnnotatedImage(canvas.toDataURL());\r\n    }\r\n  };\r\n\r\n  const drawDetectionBoxes = (ctx, results, canvasWidth, canvasHeight) => {\r\n    results.forEach((result, index) => {\r\n      const { bbox } = result;\r\n      const x = bbox.x * canvasWidth;\r\n      const y = bbox.y * canvasHeight;\r\n      const width = bbox.width * canvasWidth;\r\n      const height = bbox.height * canvasHeight;\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = classColors[result.class];\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\r\n      const labelWidth = ctx.measureText(label).width + 20;\r\n      const labelHeight = 25;\r\n\r\n      ctx.fillStyle = classColors[result.class];\r\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.font = 'bold 14px Arial';\r\n      ctx.fillText(label, x + 10, y - 8);\r\n    });\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#51cf66';\r\n    if (confidence >= 0.6) return '#ffd43b';\r\n    return '#ff6b6b';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Model Info */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaBrain className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">AI Analysis</h3>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n            <FaRobot className=\"inline mr-1\" />\r\n            Model: best.pt\r\n          </span>\r\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\r\n            Classes: 3\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Analysis Status */}\r\n      {isAnalyzing && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\"\r\n        >\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"loading-spinner mr-3\"></div>\r\n            <p className=\"text-[#0077B6] font-medium\">Analyzing image with YOLOv8...</p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Annotated Image */}\r\n      {annotatedImage && (\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\"\r\n        >\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaImage className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Annotated Results</h4>\r\n          </div>\r\n          <div className=\"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\">\r\n            <img\r\n              src={annotatedImage}\r\n              alt=\"Annotated detection\"\r\n              className=\"w-full h-auto max-h-64 object-contain\"\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Detection Statistics */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartBar className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Detection Statistics</h4>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n          {Object.entries(detectionStats).map(([className, count]) => (\r\n            <motion.div\r\n              key={className}\r\n              whileHover={{ scale: 1.05 }}\r\n              className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"text-2xl mr-3\">{classIcons[className]}</div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-700 capitalize\">{className.replace('-', ' ')}</p>\r\n                  <p className=\"text-2xl font-bold text-[#0077B6]\">{count}</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detection History */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaClock className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Detections</h4>\r\n        </div>\r\n\r\n        {detectionHistory.length > 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {detectionHistory.map((detection) => (\r\n              <motion.div\r\n                key={detection.id}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n              >\r\n                <div className=\"flex justify-between items-center mb-3\">\r\n                  <span className=\"text-sm font-medium text-gray-600\">\r\n                    {new Date(detection.timestamp).toLocaleTimeString()}\r\n                  </span>\r\n                  <span className=\"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n                    {detection.results.length} detection(s)\r\n                  </span>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\r\n                  {detection.results.map((result, index) => (\r\n                    <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded-lg\">\r\n                      <span className=\"flex items-center text-sm font-medium text-gray-700\">\r\n                        <span className=\"mr-2\">{classIcons[result.class]}</span>\r\n                        {result.class.replace('-', ' ')}\r\n                      </span>\r\n                      <span\r\n                        className=\"text-sm font-bold\"\r\n                        style={{ color: getConfidenceColor(result.confidence) }}\r\n                      >\r\n                        {(result.confidence * 100).toFixed(1)}%\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <FaBrain className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\r\n            <p>No detections yet. Capture images to start AI analysis.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC;IACnDsB,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE;EACnB,CAAC;EAEDvB,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,YAAY,EAAE;MAChBW,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC,EAAE,CAACZ,YAAY,CAAC,CAAC;EAElBb,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,WAAW,EAAE;MACf,MAAMc,WAAW,GAAGC,mBAAmB,CAAC,CAAC;MACzCC,UAAU,CAAC,MAAM;QACfC,sBAAsB,CAACH,WAAW,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACd,WAAW,CAAC,CAAC;EAEjB,MAAMe,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMG,OAAO,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,EAAEK,CAAC,EAAE,EAAE;MACtC,MAAMC,WAAW,GAAGP,OAAO,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,OAAO,CAACQ,MAAM,CAAC,CAAC;MACvEH,OAAO,CAACI,IAAI,CAAC;QACXC,KAAK,EAAEH,WAAW;QAClBI,UAAU,EAAET,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAAE;QACvCQ,IAAI,EAAE;UACJC,CAAC,EAAEX,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBU,CAAC,EAAEZ,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBW,KAAK,EAAEb,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCY,MAAM,EAAEd,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOC,OAAO;EAChB,CAAC;EAED,MAAMN,sBAAsB,GAAIM,OAAO,IAAK;IAC1C,MAAMY,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCjB,OAAO,EAAEA,OAAO;MAChBkB,KAAK,EAAExC,YAAY,IAAI;IACzB,CAAC;IAEDG,mBAAmB,CAACsC,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAGrC;IAAe,CAAC;IACtCgB,OAAO,CAACsB,OAAO,CAACC,MAAM,IAAI;MACxBF,QAAQ,CAACE,MAAM,CAAClB,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFpB,iBAAiB,CAACoC,QAAQ,CAAC;;IAE3B;IACAG,sBAAsB,CAACxB,OAAO,CAAC;;IAE/B;IACAxB,SAAS,CAACwB,OAAO,CAAC;EACpB,CAAC;EAED,MAAMwB,sBAAsB,GAAIxB,OAAO,IAAK;IAC1C;IACA,MAAMyB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAJ,MAAM,CAACf,KAAK,GAAG,GAAG;IAClBe,MAAM,CAACd,MAAM,GAAG,GAAG;;IAEnB;IACA,IAAIjC,YAAY,EAAE;MAChB,MAAMoD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB;QACAJ,GAAG,CAACK,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEL,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;QAErD;QACAuB,kBAAkB,CAACN,GAAG,EAAE5B,OAAO,EAAEyB,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;QAE7D;QACA5B,iBAAiB,CAAC0C,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC;MACvC,CAAC;MACDL,GAAG,CAACM,GAAG,GAAG1D,YAAY;IACxB,CAAC,MAAM;MACL;MACAkD,GAAG,CAACS,SAAS,GAAG,SAAS;MACzBT,GAAG,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEb,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;MAE/C;MACAuB,kBAAkB,CAACN,GAAG,EAAE5B,OAAO,EAAEyB,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;MAE7D5B,iBAAiB,CAAC0C,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMD,kBAAkB,GAAGA,CAACN,GAAG,EAAE5B,OAAO,EAAEuC,WAAW,EAAEC,YAAY,KAAK;IACtExC,OAAO,CAACsB,OAAO,CAAC,CAACC,MAAM,EAAEkB,KAAK,KAAK;MACjC,MAAM;QAAElC;MAAK,CAAC,GAAGgB,MAAM;MACvB,MAAMf,CAAC,GAAGD,IAAI,CAACC,CAAC,GAAG+B,WAAW;MAC9B,MAAM9B,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG+B,YAAY;MAC/B,MAAM9B,KAAK,GAAGH,IAAI,CAACG,KAAK,GAAG6B,WAAW;MACtC,MAAM5B,MAAM,GAAGJ,IAAI,CAACI,MAAM,GAAG6B,YAAY;;MAEzC;MACAZ,GAAG,CAACc,WAAW,GAAGvD,WAAW,CAACoC,MAAM,CAAClB,KAAK,CAAC;MAC3CuB,GAAG,CAACe,SAAS,GAAG,CAAC;MACjBf,GAAG,CAACgB,UAAU,CAACpC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMkC,KAAK,GAAG,GAAGtB,MAAM,CAAClB,KAAK,IAAI,CAACkB,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,GAAG;MACxE,MAAMC,UAAU,GAAGnB,GAAG,CAACoB,WAAW,CAACH,KAAK,CAAC,CAACnC,KAAK,GAAG,EAAE;MACpD,MAAMuC,WAAW,GAAG,EAAE;MAEtBrB,GAAG,CAACS,SAAS,GAAGlD,WAAW,CAACoC,MAAM,CAAClB,KAAK,CAAC;MACzCuB,GAAG,CAACU,QAAQ,CAAC9B,CAAC,EAAEC,CAAC,GAAGwC,WAAW,EAAEF,UAAU,EAAEE,WAAW,CAAC;;MAEzD;MACArB,GAAG,CAACS,SAAS,GAAG,OAAO;MACvBT,GAAG,CAACsB,IAAI,GAAG,iBAAiB;MAC5BtB,GAAG,CAACuB,QAAQ,CAACN,KAAK,EAAErC,CAAC,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2C,kBAAkB,GAAI9C,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEhC,OAAA;IAAK+E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhF,OAAA;MAAK+E,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAC1FhF,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACN,OAAO;YAACqF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhF,OAAA;UAAM+E,SAAS,EAAC,oFAAoF;UAAAC,QAAA,gBAClGhF,OAAA,CAACF,OAAO;YAACiF,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpF,OAAA;UAAM+E,SAAS,EAAC,qFAAqF;UAAAC,QAAA,EAAC;QAEtG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjF,WAAW,iBACVH,OAAA,CAACP,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEpD,CAAC,EAAE;MAAG,CAAE;MAC/BqD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEpD,CAAC,EAAE;MAAE,CAAE;MAC9B4C,SAAS,EAAC,oFAAoF;MAAAC,QAAA,eAE9FhF,OAAA;QAAK+E,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ChF,OAAA;UAAK+E,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CpF,OAAA;UAAG+E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGA5E,cAAc,iBACbR,OAAA,CAACP,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAK,CAAE;MACrCD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCV,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEpDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACH,OAAO;YAACkF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,sEAAsE;QAAAC,QAAA,eACnFhF,OAAA;UACE8D,GAAG,EAAEtD,cAAe;UACpBkF,GAAG,EAAC,qBAAqB;UACzBX,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAGDpF,OAAA;MAAK+E,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACL,UAAU;YAACoF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDW,MAAM,CAACC,OAAO,CAAClF,cAAc,CAAC,CAACmF,GAAG,CAAC,CAAC,CAACd,SAAS,EAAEe,KAAK,CAAC,kBACrD9F,OAAA,CAACP,MAAM,CAAC4F,GAAG;UAETU,UAAU,EAAE;YAAEN,KAAK,EAAE;UAAK,CAAE;UAC5BV,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAEvHhF,OAAA;YAAK+E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChF,OAAA;cAAK+E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAElE,UAAU,CAACiE,SAAS;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAG+E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAED,SAAS,CAACiB,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FpF,OAAA;gBAAG+E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEc;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAVDL,SAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA;MAAK+E,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACJ,OAAO;YAACmF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,EAEL9E,gBAAgB,CAACuB,MAAM,GAAG,CAAC,gBAC1B7B,OAAA;QAAK+E,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB1E,gBAAgB,CAACuF,GAAG,CAAEI,SAAS,iBAC9BjG,OAAA,CAACP,MAAM,CAAC4F,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAErD,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCsD,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAErD,CAAC,EAAE;UAAE,CAAE;UAC9B6C,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAEvHhF,OAAA;YAAK+E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDhF,OAAA;cAAM+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAChD,IAAIxC,IAAI,CAACyD,SAAS,CAACvD,SAAS,CAAC,CAACwD,kBAAkB,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACPpF,OAAA;cAAM+E,SAAS,EAAC,oFAAoF;cAAAC,QAAA,GACjGiB,SAAS,CAACvE,OAAO,CAACG,MAAM,EAAC,eAC5B;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDiB,SAAS,CAACvE,OAAO,CAACmE,GAAG,CAAC,CAAC5C,MAAM,EAAEkB,KAAK,kBACnCnE,OAAA;cAAiB+E,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACtFhF,OAAA;gBAAM+E,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,gBACnEhF,OAAA;kBAAM+E,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAElE,UAAU,CAACmC,MAAM,CAAClB,KAAK;gBAAC;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvDnC,MAAM,CAAClB,KAAK,CAACiE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACPpF,OAAA;gBACE+E,SAAS,EAAC,mBAAmB;gBAC7BoB,KAAK,EAAE;kBAAEC,KAAK,EAAEtB,kBAAkB,CAAC7B,MAAM,CAACjB,UAAU;gBAAE,CAAE;gBAAAgD,QAAA,GAEvD,CAAC/B,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAVCjB,KAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GA7BDa,SAAS,CAAC1D,EAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BP,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENpF,OAAA;QAAK+E,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ChF,OAAA,CAACN,OAAO;UAACqF,SAAS,EAAC;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDpF,OAAA;UAAAgF,QAAA,EAAG;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CArSIJ,aAAa;AAAAoG,EAAA,GAAbpG,aAAa;AAuSnB,eAAeA,aAAa;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}