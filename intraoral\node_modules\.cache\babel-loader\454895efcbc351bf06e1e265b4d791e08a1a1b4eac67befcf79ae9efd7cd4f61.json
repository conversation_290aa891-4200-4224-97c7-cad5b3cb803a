{"ast": null, "code": "// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = easing => p => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\nexport { mirrorEasing };", "map": {"version": 3, "names": ["mirrorEasing", "easing", "p"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/easing/modifiers/mirror.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,YAAY,GAAIC,MAAM,IAAMC,CAAC,IAAKA,CAAC,IAAI,GAAG,GAAGD,MAAM,CAAC,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGD,MAAM,CAAC,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,CAAC,IAAI,CAAC;AAEpG,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}