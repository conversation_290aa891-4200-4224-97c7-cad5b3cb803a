{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\VideoCall.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { toast } from 'react-toastify';\nimport { motion } from 'framer-motion';\nimport { FaPlay, FaStop, FaCamera, FaImage } from 'react-icons/fa';\nimport './VideoCall.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  onConnectionStatus,\n  onStartAnalysis,\n  onImageCaptured\n}) => {\n  _s();\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    facingMode: \"user\"\n  };\n  const startAutoCapture = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  }, [isStreaming, captureInterval]);\n  const startStream = useCallback(() => {\n    setIsStreaming(true);\n    onConnectionStatus(true);\n    toast.success('Video stream started');\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus, startAutoCapture]);\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    stopAutoCapture();\n    toast.info('Video stream stopped');\n  }, [onConnectionStatus]);\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n  const captureImage = useCallback(() => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n      if (imageSrc) {\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false\n        };\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Send to YOLOv8 analysis\n        sendImageForAnalysis(imageSrc);\n        toast.success('Image captured and sent for analysis');\n      }\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured]);\n  const sendImageForAnalysis = async imageSrc => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n      const results = await response.json();\n      console.log('YOLOv8 Results:', results);\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      toast.error('Failed to analyze image');\n    }\n  };\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-3 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: `flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${isStreaming ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg' : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'}`,\n          onClick: isStreaming ? stopStream : startStream,\n          children: [isStreaming ? /*#__PURE__*/_jsxDEV(FaStop, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(FaPlay, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 66\n          }, this), isStreaming ? 'Stop Stream' : 'Start Stream']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n          onClick: captureImage,\n          disabled: !isStreaming || isCapturing,\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), isCapturing ? 'Capturing...' : 'Capture Image']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoCapture,\n            onChange: toggleAutoCapture,\n            disabled: !isStreaming,\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), \"Auto Capture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), autoCapture && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: captureInterval,\n          onChange: e => setCaptureInterval(Number(e.target.value)),\n          disabled: !isStreaming,\n          className: \"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 3000,\n            children: \"Every 3s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5000,\n            children: \"Every 5s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10000,\n            children: \"Every 10s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\",\n      children: [isStreaming ? /*#__PURE__*/_jsxDEV(Webcam, {\n        ref: webcamRef,\n        audio: false,\n        screenshotFormat: \"image/jpeg\",\n        videoConstraints: videoConstraints,\n        className: \"w-full h-64 md:h-80 object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-64 md:h-80 flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mb-4\",\n            children: \"\\uD83D\\uDCF9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-medium\",\n            children: \"Click \\\"Start Stream\\\" to begin video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-5 w-5 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), \"Capturing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: [\"Recent Captures (\", capturedImages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), capturedImages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n        children: capturedImages.map(image => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: \"Captured\",\n            className: \"w-full h-24 object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-1\",\n              children: new Date(image.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.analyzed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n              children: image.analyzed ? '✓ Analyzed' : '⏳ Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No captures yet. Start streaming and capture images for analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"K2BHJC8Mxpy6rg+hr0OXQfjwd54=\");\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Webcam", "toast", "motion", "FaPlay", "FaStop", "FaCamera", "FaImage", "jsxDEV", "_jsxDEV", "VideoCall", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "_s", "isStreaming", "setIsStreaming", "isCapturing", "setIsCapturing", "capturedImages", "setCapturedImages", "autoCapture", "setAutoCapture", "captureInterval", "setCaptureInterval", "webcamRef", "intervalRef", "videoConstraints", "width", "height", "facingMode", "startAutoCapture", "current", "clearInterval", "setInterval", "captureImage", "startStream", "success", "stopStream", "stopAutoCapture", "info", "imageSrc", "getScreenshot", "newImage", "id", "Date", "now", "src", "timestamp", "toISOString", "analyzed", "prev", "slice", "sendImageForAnalysis", "base64Data", "replace", "blob", "fetch", "then", "res", "formData", "FormData", "append", "response", "method", "body", "ok", "Error", "results", "json", "console", "log", "error", "toggleAutoCapture", "className", "children", "button", "whileHover", "scale", "whileTap", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "type", "checked", "onChange", "value", "e", "Number", "target", "ref", "audio", "screenshotFormat", "length", "map", "image", "div", "alt", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/VideoCall.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport Webcam from 'react-webcam';\r\nimport { toast } from 'react-toastify';\r\nimport { motion } from 'framer-motion';\r\nimport { FaPlay, FaStop, FaCamera, FaImage } from 'react-icons/fa';\r\nimport './VideoCall.css';\r\n\r\nconst VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured }) => {\r\n  const [isStreaming, setIsStreaming] = useState(false);\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const [capturedImages, setCapturedImages] = useState([]);\r\n  const [autoCapture, setAutoCapture] = useState(true);\r\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\r\n  const webcamRef = useRef(null);\r\n  const intervalRef = useRef(null);\r\n\r\n  const videoConstraints = {\r\n    width: 640,\r\n    height: 480,\r\n    facingMode: \"user\"\r\n  };\r\n\r\n  const startAutoCapture = useCallback(() => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n    }\r\n\r\n    intervalRef.current = setInterval(() => {\r\n      if (isStreaming && webcamRef.current) {\r\n        captureImage();\r\n      }\r\n    }, captureInterval);\r\n  }, [isStreaming, captureInterval]);\r\n\r\n  const startStream = useCallback(() => {\r\n    setIsStreaming(true);\r\n    onConnectionStatus(true);\r\n    toast.success('Video stream started');\r\n\r\n    // Start automatic capture if enabled\r\n    if (autoCapture) {\r\n      startAutoCapture();\r\n    }\r\n  }, [autoCapture, onConnectionStatus, startAutoCapture]);\r\n\r\n  const stopStream = useCallback(() => {\r\n    setIsStreaming(false);\r\n    onConnectionStatus(false);\r\n    stopAutoCapture();\r\n    toast.info('Video stream stopped');\r\n  }, [onConnectionStatus]);\r\n\r\n\r\n\r\n  const stopAutoCapture = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n      intervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  const captureImage = useCallback(() => {\r\n    if (webcamRef.current && isStreaming) {\r\n      setIsCapturing(true);\r\n      const imageSrc = webcamRef.current.getScreenshot();\r\n      \r\n      if (imageSrc) {\r\n        const newImage = {\r\n          id: Date.now(),\r\n          src: imageSrc,\r\n          timestamp: new Date().toISOString(),\r\n          analyzed: false\r\n        };\r\n        \r\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\r\n        \r\n        // Pass the captured image to parent component for YOLOv8 analysis\r\n        if (onImageCaptured) {\r\n          onImageCaptured(imageSrc);\r\n        }\r\n        \r\n        // Send to YOLOv8 analysis\r\n        sendImageForAnalysis(imageSrc);\r\n        \r\n        toast.success('Image captured and sent for analysis');\r\n      }\r\n      \r\n      setIsCapturing(false);\r\n    }\r\n  }, [isStreaming, onImageCaptured]);\r\n\r\n  const sendImageForAnalysis = async (imageSrc) => {\r\n    try {\r\n      onStartAnalysis();\r\n      \r\n      // Convert base64 to blob\r\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\r\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\r\n      \r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('image', blob, 'capture.jpg');\r\n      \r\n      // Send to backend for YOLOv8 analysis\r\n      const response = await fetch('/api/analyze', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Analysis failed');\r\n      }\r\n      \r\n      const results = await response.json();\r\n      console.log('YOLOv8 Results:', results);\r\n      \r\n    } catch (error) {\r\n      console.error('Error sending image for analysis:', error);\r\n      toast.error('Failed to analyze image');\r\n    }\r\n  };\r\n\r\n  const toggleAutoCapture = () => {\r\n    setAutoCapture(!autoCapture);\r\n    if (!autoCapture) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      stopAutoCapture();\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (autoCapture && isStreaming) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  }, [autoCapture, captureInterval, isStreaming]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Video Controls */}\r\n      <div className=\"flex flex-wrap gap-3 items-center justify-between\">\r\n        <div className=\"flex gap-3\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${\r\n              isStreaming\r\n                ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg'\r\n                : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'\r\n            }`}\r\n            onClick={isStreaming ? stopStream : startStream}\r\n          >\r\n            {isStreaming ? <FaStop className=\"mr-2 h-4 w-4\" /> : <FaPlay className=\"mr-2 h-4 w-4\" />}\r\n            {isStreaming ? 'Stop Stream' : 'Start Stream'}\r\n          </motion.button>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            onClick={captureImage}\r\n            disabled={!isStreaming || isCapturing}\r\n          >\r\n            <FaCamera className=\"mr-2 h-4 w-4\" />\r\n            {isCapturing ? 'Capturing...' : 'Capture Image'}\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <label className=\"flex items-center text-sm font-medium text-gray-700\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={autoCapture}\r\n              onChange={toggleAutoCapture}\r\n              disabled={!isStreaming}\r\n              className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\r\n            />\r\n            Auto Capture\r\n          </label>\r\n\r\n          {autoCapture && (\r\n            <select\r\n              value={captureInterval}\r\n              onChange={(e) => setCaptureInterval(Number(e.target.value))}\r\n              disabled={!isStreaming}\r\n              className=\"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\r\n            >\r\n              <option value={3000}>Every 3s</option>\r\n              <option value={5000}>Every 5s</option>\r\n              <option value={10000}>Every 10s</option>\r\n            </select>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Container */}\r\n      <div className=\"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\">\r\n        {isStreaming ? (\r\n          <Webcam\r\n            ref={webcamRef}\r\n            audio={false}\r\n            screenshotFormat=\"image/jpeg\"\r\n            videoConstraints={videoConstraints}\r\n            className=\"w-full h-64 md:h-80 object-cover\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-full h-64 md:h-80 flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\">\r\n            <div className=\"text-center text-white\">\r\n              <div className=\"text-6xl mb-4\">📹</div>\r\n              <p className=\"text-lg font-medium\">Click \"Start Stream\" to begin video consultation</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {isCapturing && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n            <div className=\"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\">\r\n              <FaCamera className=\"mr-2 h-5 w-5 animate-pulse\" />\r\n              Capturing...\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recent Captures */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaImage className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recent Captures ({capturedImages.length})</h3>\r\n        </div>\r\n\r\n        {capturedImages.length > 0 ? (\r\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\r\n            {capturedImages.map((image) => (\r\n              <motion.div\r\n                key={image.id}\r\n                whileHover={{ scale: 1.05 }}\r\n                className=\"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\"\r\n              >\r\n                <img\r\n                  src={image.src}\r\n                  alt=\"Captured\"\r\n                  className=\"w-full h-24 object-cover\"\r\n                />\r\n                <div className=\"p-2\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">\r\n                    {new Date(image.timestamp).toLocaleTimeString()}\r\n                  </p>\r\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                    image.analyzed\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : 'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}\r\n                  </span>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <FaImage className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\r\n            <p>No captures yet. Start streaming and capture images for analysis.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoCall; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM4B,SAAS,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4B,WAAW,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAM6B,gBAAgB,GAAG;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,gBAAgB,GAAG/B,WAAW,CAAC,MAAM;IACzC,IAAI0B,WAAW,CAACM,OAAO,EAAE;MACvBC,aAAa,CAACP,WAAW,CAACM,OAAO,CAAC;IACpC;IAEAN,WAAW,CAACM,OAAO,GAAGE,WAAW,CAAC,MAAM;MACtC,IAAInB,WAAW,IAAIU,SAAS,CAACO,OAAO,EAAE;QACpCG,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAEZ,eAAe,CAAC;EACrB,CAAC,EAAE,CAACR,WAAW,EAAEQ,eAAe,CAAC,CAAC;EAElC,MAAMa,WAAW,GAAGpC,WAAW,CAAC,MAAM;IACpCgB,cAAc,CAAC,IAAI,CAAC;IACpBL,kBAAkB,CAAC,IAAI,CAAC;IACxBT,KAAK,CAACmC,OAAO,CAAC,sBAAsB,CAAC;;IAErC;IACA,IAAIhB,WAAW,EAAE;MACfU,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACV,WAAW,EAAEV,kBAAkB,EAAEoB,gBAAgB,CAAC,CAAC;EAEvD,MAAMO,UAAU,GAAGtC,WAAW,CAAC,MAAM;IACnCgB,cAAc,CAAC,KAAK,CAAC;IACrBL,kBAAkB,CAAC,KAAK,CAAC;IACzB4B,eAAe,CAAC,CAAC;IACjBrC,KAAK,CAACsC,IAAI,CAAC,sBAAsB,CAAC;EACpC,CAAC,EAAE,CAAC7B,kBAAkB,CAAC,CAAC;EAIxB,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIb,WAAW,CAACM,OAAO,EAAE;MACvBC,aAAa,CAACP,WAAW,CAACM,OAAO,CAAC;MAClCN,WAAW,CAACM,OAAO,GAAG,IAAI;IAC5B;EACF,CAAC;EAED,MAAMG,YAAY,GAAGnC,WAAW,CAAC,MAAM;IACrC,IAAIyB,SAAS,CAACO,OAAO,IAAIjB,WAAW,EAAE;MACpCG,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMuB,QAAQ,GAAGhB,SAAS,CAACO,OAAO,CAACU,aAAa,CAAC,CAAC;MAElD,IAAID,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAG;UACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,GAAG,EAAEN,QAAQ;UACbO,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCC,QAAQ,EAAE;QACZ,CAAC;QAED9B,iBAAiB,CAAC+B,IAAI,IAAI,CAACR,QAAQ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAIvC,eAAe,EAAE;UACnBA,eAAe,CAAC4B,QAAQ,CAAC;QAC3B;;QAEA;QACAY,oBAAoB,CAACZ,QAAQ,CAAC;QAE9BvC,KAAK,CAACmC,OAAO,CAAC,sCAAsC,CAAC;MACvD;MAEAnB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEF,eAAe,CAAC,CAAC;EAElC,MAAMwC,oBAAoB,GAAG,MAAOZ,QAAQ,IAAK;IAC/C,IAAI;MACF7B,eAAe,CAAC,CAAC;;MAEjB;MACA,MAAM0C,UAAU,GAAGb,QAAQ,CAACc,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;MACpE,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAAC,0BAA0BH,UAAU,EAAE,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC;;MAExF;MACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,IAAI,EAAE,aAAa,CAAC;;MAE7C;MACA,MAAMO,QAAQ,GAAG,MAAMN,KAAK,CAAC,cAAc,EAAE;QAC3CO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEL;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAMC,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACrCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,OAAO,CAAC;IAEzC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDtE,KAAK,CAACsE,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnD,cAAc,CAAC,CAACD,WAAW,CAAC;IAC5B,IAAI,CAACA,WAAW,EAAE;MAChBU,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLQ,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAEDxC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwC,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENxC,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,IAAIN,WAAW,EAAE;MAC9BgB,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLQ,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAClB,WAAW,EAAEE,eAAe,EAAER,WAAW,CAAC,CAAC;EAE/C,oBACEN,OAAA;IAAKiE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlE,OAAA;MAAKiE,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChElE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlE,OAAA,CAACN,MAAM,CAACyE,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BJ,SAAS,EAAE,oFACT3D,WAAW,GACP,mFAAmF,GACnF,mFAAmF,EACtF;UACHiE,OAAO,EAAEjE,WAAW,GAAGuB,UAAU,GAAGF,WAAY;UAAAuC,QAAA,GAE/C5D,WAAW,gBAAGN,OAAA,CAACJ,MAAM;YAACqE,SAAS,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG3E,OAAA,CAACL,MAAM;YAACsE,SAAS,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvFrE,WAAW,GAAG,aAAa,GAAG,cAAc;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEhB3E,OAAA,CAACN,MAAM,CAACyE,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BJ,SAAS,EAAC,oNAAoN;UAC9NM,OAAO,EAAE7C,YAAa;UACtBkD,QAAQ,EAAE,CAACtE,WAAW,IAAIE,WAAY;UAAA0D,QAAA,gBAEtClE,OAAA,CAACH,QAAQ;YAACoE,SAAS,EAAC;UAAc;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpCnE,WAAW,GAAG,cAAc,GAAG,eAAe;QAAA;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEN3E,OAAA;QAAKiE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtClE,OAAA;UAAOiE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBACpElE,OAAA;YACE6E,IAAI,EAAC,UAAU;YACfC,OAAO,EAAElE,WAAY;YACrBmE,QAAQ,EAAEf,iBAAkB;YAC5BY,QAAQ,EAAE,CAACtE,WAAY;YACvB2D,SAAS,EAAC;UAA0E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAEP/D,WAAW,iBACVZ,OAAA;UACEgF,KAAK,EAAElE,eAAgB;UACvBiE,QAAQ,EAAGE,CAAC,IAAKlE,kBAAkB,CAACmE,MAAM,CAACD,CAAC,CAACE,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5DJ,QAAQ,EAAE,CAACtE,WAAY;UACvB2D,SAAS,EAAC,8GAA8G;UAAAC,QAAA,gBAExHlE,OAAA;YAAQgF,KAAK,EAAE,IAAK;YAAAd,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC3E,OAAA;YAAQgF,KAAK,EAAE,IAAK;YAAAd,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC3E,OAAA;YAAQgF,KAAK,EAAE,KAAM;YAAAd,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA;MAAKiE,SAAS,EAAC,2DAA2D;MAAAC,QAAA,GACvE5D,WAAW,gBACVN,OAAA,CAACR,MAAM;QACL4F,GAAG,EAAEpE,SAAU;QACfqE,KAAK,EAAE,KAAM;QACbC,gBAAgB,EAAC,YAAY;QAC7BpE,gBAAgB,EAAEA,gBAAiB;QACnC+C,SAAS,EAAC;MAAkC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,gBAEF3E,OAAA;QAAKiE,SAAS,EAAC,kGAAkG;QAAAC,QAAA,eAC/GlE,OAAA;UAAKiE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrClE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC3E,OAAA;YAAGiE,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAgD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAnE,WAAW,iBACVR,OAAA;QAAKiE,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvFlE,OAAA;UAAKiE,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAC3FlE,OAAA,CAACH,QAAQ;YAACoE,SAAS,EAAC;UAA4B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3E,OAAA;MAAKiE,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDlE,OAAA;QAAKiE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrClE,OAAA;UAAKiE,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1ElE,OAAA,CAACF,OAAO;YAACmE,SAAS,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACN3E,OAAA;UAAIiE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,mBAAiB,EAACxD,cAAc,CAAC6E,MAAM,EAAC,GAAC;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC,EAELjE,cAAc,CAAC6E,MAAM,GAAG,CAAC,gBACxBvF,OAAA;QAAKiE,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClExD,cAAc,CAAC8E,GAAG,CAAEC,KAAK,iBACxBzF,OAAA,CAACN,MAAM,CAACgG,GAAG;UAETtB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BJ,SAAS,EAAC,2HAA2H;UAAAC,QAAA,gBAErIlE,OAAA;YACEsC,GAAG,EAAEmD,KAAK,CAACnD,GAAI;YACfqD,GAAG,EAAC,UAAU;YACd1B,SAAS,EAAC;UAA0B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACF3E,OAAA;YAAKiE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBlE,OAAA;cAAGiE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,IAAI9B,IAAI,CAACqD,KAAK,CAAClD,SAAS,CAAC,CAACqD,kBAAkB,CAAC;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJ3E,OAAA;cAAMiE,SAAS,EAAE,uEACfwB,KAAK,CAAChD,QAAQ,GACV,6BAA6B,GAC7B,+BAA+B,EAClC;cAAAyB,QAAA,EACAuB,KAAK,CAAChD,QAAQ,GAAG,YAAY,GAAG;YAAW;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GApBDc,KAAK,CAACtD,EAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBH,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN3E,OAAA;QAAKiE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ClE,OAAA,CAACF,OAAO;UAACmE,SAAS,EAAC;QAAmC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzD3E,OAAA;UAAAkE,QAAA,EAAG;QAAiE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA/QIJ,SAAS;AAAA4F,EAAA,GAAT5F,SAAS;AAiRf,eAAeA,SAAS;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}