{"ast": null, "code": "import { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nimport { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, {\n  container = document.documentElement,\n  ...options\n} = {}) {\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) handler.measure();\n    };\n    const updateAll = () => {\n      for (const handler of containerHandlers) {\n        handler.update(frameData.timestamp);\n      }\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) handler.notify();\n    };\n    const listener = () => {\n      frame.read(measureAll, false, true);\n      frame.read(updateAll, false, true);\n      frame.update(notifyAll, false, true);\n    };\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    var _a;\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["resize", "createScrollInfo", "createOnScrollHandler", "frame", "cancelFrame", "frameData", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "documentElement", "window", "scrollInfo", "onScroll", "container", "options", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "updateAll", "update", "timestamp", "notifyAll", "notify", "listener", "read", "target", "addEventListener", "passive", "_a", "currentHandlers", "delete", "size", "scrollListener", "removeEventListener"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nimport { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            frame.read(measureAll, false, true);\n            frame.read(updateAll, false, true);\n            frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        var _a;\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,8BAA8B;AAE5E,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,eAAe,GAAGC,MAAM,GAAGH,OAAO;AAC3F,SAASI,UAAUA,CAACC,QAAQ,EAAE;EAAEC,SAAS,GAAGL,QAAQ,CAACC,eAAe;EAAE,GAAGK;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EACrF,IAAIC,iBAAiB,GAAGV,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACE,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7BZ,gBAAgB,CAACa,GAAG,CAACL,SAAS,EAAEE,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGtB,gBAAgB,CAAC,CAAC;EAC/B,MAAMuB,gBAAgB,GAAGtB,qBAAqB,CAACe,SAAS,EAAED,QAAQ,EAAEO,IAAI,EAAEL,OAAO,CAAC;EAClFC,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAAClB,eAAe,CAACoB,GAAG,CAACT,SAAS,CAAC,EAAE;IACjC,MAAMU,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACC,OAAO,CAAC,CAAC;IACzB,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMF,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACG,MAAM,CAAC1B,SAAS,CAAC2B,SAAS,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAML,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACM,MAAM,CAAC,CAAC;IACxB,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBhC,KAAK,CAACiC,IAAI,CAACT,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;MACnCxB,KAAK,CAACiC,IAAI,CAACN,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;MAClC3B,KAAK,CAAC4B,MAAM,CAACE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;IACxC,CAAC;IACD3B,eAAe,CAACgB,GAAG,CAACL,SAAS,EAAEkB,QAAQ,CAAC;IACxC,MAAME,MAAM,GAAG3B,cAAc,CAACO,SAAS,CAAC;IACxCH,MAAM,CAACwB,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAItB,SAAS,KAAKL,QAAQ,CAACC,eAAe,EAAE;MACxCL,eAAe,CAACc,GAAG,CAACL,SAAS,EAAEjB,MAAM,CAACiB,SAAS,EAAEkB,QAAQ,CAAC,CAAC;IAC/D;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,QAAQ,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAClE;EACA,MAAMJ,QAAQ,GAAG7B,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;EAC/Cd,KAAK,CAACiC,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACT,IAAIK,EAAE;IACNpC,WAAW,CAAC+B,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMM,eAAe,GAAGhC,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;IACvD,IAAI,CAACwB,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAAClB,gBAAgB,CAAC;IACxC,IAAIiB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAGtC,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;IACrDX,eAAe,CAACoC,MAAM,CAACzB,SAAS,CAAC;IACjC,IAAI2B,cAAc,EAAE;MAChBlC,cAAc,CAACO,SAAS,CAAC,CAAC4B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;MACvE,CAACJ,EAAE,GAAGhC,eAAe,CAACY,GAAG,CAACH,SAAS,CAAC,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC;MAC/E1B,MAAM,CAAC+B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAAS7B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}