{"version": 3, "file": "no-implicit-any-catch.js", "sourceRoot": "", "sources": ["../../src/rules/no-implicit-any-catch.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAYhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,kBAAkB,EAAE,+BAA+B;YACnD,kBAAkB,EAAE,+BAA+B;YACnD,sBAAsB,EACpB,kGAAkG;SACrG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,WAAW,EACT,+FAA+F;wBACjG,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;SACxB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC;QACpC,OAAO;YACL,WAAW,CAAC,IAAI;gBACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACf,OAAO,CAAC,gCAAgC;iBACzC;gBAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;oBAC9B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,oBAAoB;wBAC/B,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,wBAAwB;gCACnC,GAAG,CAAC,KAAK;oCACP,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAM,EAAE,WAAW,CAAC,CAAC;gCACzD,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;iBACJ;qBAAM,IACL,CAAC,gBAAgB;oBACjB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI;wBAC3C,sBAAc,CAAC,YAAY,EAC7B;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,oBAAoB;wBAC/B,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,wBAAwB;gCACnC,GAAG,CAAC,KAAK;oCACP,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,CAAC,KAAM,CAAC,cAAe,EAC3B,WAAW,CACZ,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}