{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaBrain, FaChartBar, FaHistory, FaSpinner } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const classColors = {\n    'decaycavity': '#ef4444',\n    'early-decay': '#f59e0b',\n    'healthy tooth': '#10b981'\n  };\n  const classIcons = {\n    'decaycavity': '🦷',\n    'early-decay': '⚠️',\n    'healthy tooth': '✅'\n  };\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n  useEffect(() => {\n    // Simulate receiving detection results\n    if (isAnalyzing) {\n      const mockResults = generateMockResults();\n      setTimeout(() => {\n        handleDetectionResults(mockResults);\n      }, 2000);\n    }\n  }, [isAnalyzing]);\n  const generateMockResults = () => {\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\n    const numDetections = Math.floor(Math.random() * 3) + 1;\n    const results = [];\n    for (let i = 0; i < numDetections; i++) {\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\n      results.push({\n        class: randomClass,\n        confidence: Math.random() * 0.4 + 0.6,\n        // 60-100% confidence\n        bbox: {\n          x: Math.random() * 0.8,\n          y: Math.random() * 0.8,\n          width: Math.random() * 0.3 + 0.1,\n          height: Math.random() * 0.3 + 0.1\n        }\n      });\n    }\n    return results;\n  };\n  const handleDetectionResults = results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  };\n  const generateAnnotatedImage = results => {\n    // Create a canvas to draw the annotated image\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = 640;\n    canvas.height = 480;\n\n    // If we have a current image, use it as background\n    if (currentImage) {\n      const img = new Image();\n      img.onload = () => {\n        // Draw the original image\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n        // Draw detection boxes on top\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n\n        // Set the annotated image\n        setAnnotatedImage(canvas.toDataURL());\n      };\n      img.src = currentImage;\n    } else {\n      // Fallback to placeholder if no image\n      ctx.fillStyle = '#f5f5f5';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Draw detection boxes\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n      setAnnotatedImage(canvas.toDataURL());\n    }\n  };\n  const drawDetectionBoxes = (ctx, results, canvasWidth, canvasHeight) => {\n    results.forEach((result, index) => {\n      const {\n        bbox\n      } = result;\n      const x = bbox.x * canvasWidth;\n      const y = bbox.y * canvasHeight;\n      const width = bbox.width * canvasWidth;\n      const height = bbox.height * canvasHeight;\n\n      // Draw bounding box\n      ctx.strokeStyle = classColors[result.class];\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\n      const labelWidth = ctx.measureText(label).width + 20;\n      const labelHeight = 25;\n      ctx.fillStyle = classColors[result.class];\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.font = 'bold 14px Arial';\n      ctx.fillText(label, x + 10, y - 8);\n    });\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#10b981';\n    if (confidence >= 0.6) return '#f59e0b';\n    return '#ef4444';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-[#0077B6] flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaBrain, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), \"YOLOv8 Analysis\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] text-xs font-medium rounded-full\",\n          children: \"Model: best.pt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] text-xs font-medium rounded-full\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] rounded-lg p-4 flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n        className: \"animate-spin text-[#0077B6]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-[#333333] font-medium\",\n        children: \"Analyzing image with YOLOv8...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this), annotatedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6]\",\n        children: \"Annotated Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-100 rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\",\n          className: \"w-full h-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6] flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), \"Detection Statistics\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-[#333333] capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6] flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaHistory, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), \"Recent Detections\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 max-h-64 overflow-y-auto\",\n        children: detectionHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No detections yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: \"Start capturing images to see analysis results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this) : detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500\",\n              children: new Date(detection.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-[#0077B6]\",\n              children: [detection.results.length, \" detection(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1\",\n            children: detection.results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: classIcons[result.class]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"capitalize\",\n                  children: result.class.replace('-', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                style: {\n                  color: getConfidenceColor(result.confidence)\n                },\n                children: [(result.confidence * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 17\n          }, this)]\n        }, detection.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"vBN12p95qDZCWRquH0V3SwSbCs4=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaBrain", "FaChartBar", "FaHistory", "FaSpinner", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "classColors", "classIcons", "console", "log", "mockResults", "generateMockResults", "setTimeout", "handleDetectionResults", "classes", "numDetections", "Math", "floor", "random", "results", "i", "randomClass", "length", "push", "class", "confidence", "bbox", "x", "y", "width", "height", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "for<PERSON>ach", "result", "generateAnnotatedImage", "canvas", "document", "createElement", "ctx", "getContext", "img", "Image", "onload", "drawImage", "drawDetectionBoxes", "toDataURL", "src", "fillStyle", "fillRect", "canvasWidth", "canvasHeight", "index", "strokeStyle", "lineWidth", "strokeRect", "label", "toFixed", "labelWidth", "measureText", "labelHeight", "font", "fillText", "getConfidenceColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "Object", "entries", "map", "count", "replace", "detection", "toLocaleTimeString", "style", "color", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport { FaBrain, FaChartBar, FaHistory, FaSpinner } from 'react-icons/fa';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ef4444',\r\n    'early-decay': '#f59e0b',\r\n    'healthy tooth': '#10b981'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': '🦷',\r\n    'early-decay': '⚠️',\r\n    'healthy tooth': '✅'\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Update current image when prop changes\r\n    if (currentImage) {\r\n      console.log('📸 Received new captured image for analysis');\r\n    }\r\n  }, [currentImage]);\r\n\r\n  useEffect(() => {\r\n    // Simulate receiving detection results\r\n    if (isAnalyzing) {\r\n      const mockResults = generateMockResults();\r\n      setTimeout(() => {\r\n        handleDetectionResults(mockResults);\r\n      }, 2000);\r\n    }\r\n  }, [isAnalyzing]);\r\n\r\n  const generateMockResults = () => {\r\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\r\n    const numDetections = Math.floor(Math.random() * 3) + 1;\r\n    const results = [];\r\n\r\n    for (let i = 0; i < numDetections; i++) {\r\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\r\n      results.push({\r\n        class: randomClass,\r\n        confidence: Math.random() * 0.4 + 0.6, // 60-100% confidence\r\n        bbox: {\r\n          x: Math.random() * 0.8,\r\n          y: Math.random() * 0.8,\r\n          width: Math.random() * 0.3 + 0.1,\r\n          height: Math.random() * 0.3 + 0.1\r\n        }\r\n      });\r\n    }\r\n\r\n    return results;\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n    \r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  };\r\n\r\n  const generateAnnotatedImage = (results) => {\r\n    // Create a canvas to draw the annotated image\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n    \r\n    // Set canvas size\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n\r\n    // If we have a current image, use it as background\r\n    if (currentImage) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        // Draw the original image\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n        \r\n        // Draw detection boxes on top\r\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n        \r\n        // Set the annotated image\r\n        setAnnotatedImage(canvas.toDataURL());\r\n      };\r\n      img.src = currentImage;\r\n    } else {\r\n      // Fallback to placeholder if no image\r\n      ctx.fillStyle = '#f5f5f5';\r\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n      \r\n      // Draw detection boxes\r\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n      \r\n      setAnnotatedImage(canvas.toDataURL());\r\n    }\r\n  };\r\n\r\n  const drawDetectionBoxes = (ctx, results, canvasWidth, canvasHeight) => {\r\n    results.forEach((result, index) => {\r\n      const { bbox } = result;\r\n      const x = bbox.x * canvasWidth;\r\n      const y = bbox.y * canvasHeight;\r\n      const width = bbox.width * canvasWidth;\r\n      const height = bbox.height * canvasHeight;\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = classColors[result.class];\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\r\n      const labelWidth = ctx.measureText(label).width + 20;\r\n      const labelHeight = 25;\r\n\r\n      ctx.fillStyle = classColors[result.class];\r\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.font = 'bold 14px Arial';\r\n      ctx.fillText(label, x + 10, y - 8);\r\n    });\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#10b981';\r\n    if (confidence >= 0.6) return '#f59e0b';\r\n    return '#ef4444';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-semibold text-[#0077B6] flex items-center gap-2\">\r\n          <FaBrain />\r\n          YOLOv8 Analysis\r\n        </h3>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] text-xs font-medium rounded-full\">\r\n            Model: best.pt\r\n          </span>\r\n          <span className=\"px-2 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] text-xs font-medium rounded-full\">\r\n            Classes: 3\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Analysis Status */}\r\n      {isAnalyzing && (\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] rounded-lg p-4 flex items-center gap-3\">\r\n          <FaSpinner className=\"animate-spin text-[#0077B6]\" />\r\n          <p className=\"text-[#333333] font-medium\">Analyzing image with YOLOv8...</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Annotated Image */}\r\n      {annotatedImage && (\r\n        <div className=\"space-y-3\">\r\n          <h4 className=\"text-md font-semibold text-[#0077B6]\">Annotated Results</h4>\r\n          <div className=\"bg-gray-100 rounded-lg overflow-hidden\">\r\n            <img src={annotatedImage} alt=\"Annotated detection\" className=\"w-full h-auto\" />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Detection Statistics */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6] flex items-center gap-2\">\r\n          <FaChartBar />\r\n          Detection Statistics\r\n        </h4>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n          {Object.entries(detectionStats).map(([className, count]) => (\r\n            <div key={className} className=\"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"text-2xl\">{classIcons[className]}</div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-[#333333] capitalize\">{className.replace('-', ' ')}</p>\r\n                  <p className=\"text-lg font-bold text-[#0077B6]\">{count}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detection History */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6] flex items-center gap-2\">\r\n          <FaHistory />\r\n          Recent Detections\r\n        </h4>\r\n        <div className=\"space-y-3 max-h-64 overflow-y-auto\">\r\n          {detectionHistory.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              <p>No detections yet</p>\r\n              <p className=\"text-sm\">Start capturing images to see analysis results</p>\r\n            </div>\r\n          ) : (\r\n            detectionHistory.map((detection) => (\r\n              <div key={detection.id} className=\"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\">\r\n                <div className=\"flex justify-between items-start mb-2\">\r\n                  <span className=\"text-xs text-gray-500\">\r\n                    {new Date(detection.timestamp).toLocaleTimeString()}\r\n                  </span>\r\n                  <span className=\"text-xs font-medium text-[#0077B6]\">\r\n                    {detection.results.length} detection(s)\r\n                  </span>\r\n                </div>\r\n                \r\n                <div className=\"space-y-1\">\r\n                  {detection.results.map((result, index) => (\r\n                    <div key={index} className=\"flex justify-between items-center text-sm\">\r\n                      <span className=\"flex items-center gap-1\">\r\n                        <span>{classIcons[result.class]}</span>\r\n                        <span className=\"capitalize\">{result.class.replace('-', ' ')}</span>\r\n                      </span>\r\n                      <span \r\n                        className=\"font-medium\"\r\n                        style={{ color: getConfidenceColor(result.confidence) }}\r\n                      >\r\n                        {(result.confidence * 100).toFixed(1)}%\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            ))\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAC1E,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC;IACnDoB,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE;EACnB,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACd;IACA,IAAIW,YAAY,EAAE;MAChBW,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC,EAAE,CAACZ,YAAY,CAAC,CAAC;EAElBX,SAAS,CAAC,MAAM;IACd;IACA,IAAIU,WAAW,EAAE;MACf,MAAMc,WAAW,GAAGC,mBAAmB,CAAC,CAAC;MACzCC,UAAU,CAAC,MAAM;QACfC,sBAAsB,CAACH,WAAW,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACd,WAAW,CAAC,CAAC;EAEjB,MAAMe,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMG,OAAO,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,EAAEK,CAAC,EAAE,EAAE;MACtC,MAAMC,WAAW,GAAGP,OAAO,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,OAAO,CAACQ,MAAM,CAAC,CAAC;MACvEH,OAAO,CAACI,IAAI,CAAC;QACXC,KAAK,EAAEH,WAAW;QAClBI,UAAU,EAAET,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAAE;QACvCQ,IAAI,EAAE;UACJC,CAAC,EAAEX,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBU,CAAC,EAAEZ,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBW,KAAK,EAAEb,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCY,MAAM,EAAEd,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOC,OAAO;EAChB,CAAC;EAED,MAAMN,sBAAsB,GAAIM,OAAO,IAAK;IAC1C,MAAMY,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCjB,OAAO,EAAEA,OAAO;MAChBkB,KAAK,EAAExC,YAAY,IAAI;IACzB,CAAC;IAEDG,mBAAmB,CAACsC,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAGrC;IAAe,CAAC;IACtCgB,OAAO,CAACsB,OAAO,CAACC,MAAM,IAAI;MACxBF,QAAQ,CAACE,MAAM,CAAClB,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFpB,iBAAiB,CAACoC,QAAQ,CAAC;;IAE3B;IACAG,sBAAsB,CAACxB,OAAO,CAAC;;IAE/B;IACAxB,SAAS,CAACwB,OAAO,CAAC;EACpB,CAAC;EAED,MAAMwB,sBAAsB,GAAIxB,OAAO,IAAK;IAC1C;IACA,MAAMyB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAJ,MAAM,CAACf,KAAK,GAAG,GAAG;IAClBe,MAAM,CAACd,MAAM,GAAG,GAAG;;IAEnB;IACA,IAAIjC,YAAY,EAAE;MAChB,MAAMoD,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB;QACAJ,GAAG,CAACK,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEL,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;QAErD;QACAuB,kBAAkB,CAACN,GAAG,EAAE5B,OAAO,EAAEyB,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;QAE7D;QACA5B,iBAAiB,CAAC0C,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC;MACvC,CAAC;MACDL,GAAG,CAACM,GAAG,GAAG1D,YAAY;IACxB,CAAC,MAAM;MACL;MACAkD,GAAG,CAACS,SAAS,GAAG,SAAS;MACzBT,GAAG,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEb,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;;MAE/C;MACAuB,kBAAkB,CAACN,GAAG,EAAE5B,OAAO,EAAEyB,MAAM,CAACf,KAAK,EAAEe,MAAM,CAACd,MAAM,CAAC;MAE7D5B,iBAAiB,CAAC0C,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMD,kBAAkB,GAAGA,CAACN,GAAG,EAAE5B,OAAO,EAAEuC,WAAW,EAAEC,YAAY,KAAK;IACtExC,OAAO,CAACsB,OAAO,CAAC,CAACC,MAAM,EAAEkB,KAAK,KAAK;MACjC,MAAM;QAAElC;MAAK,CAAC,GAAGgB,MAAM;MACvB,MAAMf,CAAC,GAAGD,IAAI,CAACC,CAAC,GAAG+B,WAAW;MAC9B,MAAM9B,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG+B,YAAY;MAC/B,MAAM9B,KAAK,GAAGH,IAAI,CAACG,KAAK,GAAG6B,WAAW;MACtC,MAAM5B,MAAM,GAAGJ,IAAI,CAACI,MAAM,GAAG6B,YAAY;;MAEzC;MACAZ,GAAG,CAACc,WAAW,GAAGvD,WAAW,CAACoC,MAAM,CAAClB,KAAK,CAAC;MAC3CuB,GAAG,CAACe,SAAS,GAAG,CAAC;MACjBf,GAAG,CAACgB,UAAU,CAACpC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMkC,KAAK,GAAG,GAAGtB,MAAM,CAAClB,KAAK,IAAI,CAACkB,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,GAAG;MACxE,MAAMC,UAAU,GAAGnB,GAAG,CAACoB,WAAW,CAACH,KAAK,CAAC,CAACnC,KAAK,GAAG,EAAE;MACpD,MAAMuC,WAAW,GAAG,EAAE;MAEtBrB,GAAG,CAACS,SAAS,GAAGlD,WAAW,CAACoC,MAAM,CAAClB,KAAK,CAAC;MACzCuB,GAAG,CAACU,QAAQ,CAAC9B,CAAC,EAAEC,CAAC,GAAGwC,WAAW,EAAEF,UAAU,EAAEE,WAAW,CAAC;;MAEzD;MACArB,GAAG,CAACS,SAAS,GAAG,OAAO;MACvBT,GAAG,CAACsB,IAAI,GAAG,iBAAiB;MAC5BtB,GAAG,CAACuB,QAAQ,CAACN,KAAK,EAAErC,CAAC,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2C,kBAAkB,GAAI9C,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEhC,OAAA;IAAK+E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhF,OAAA;MAAK+E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhF,OAAA;QAAI+E,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC1EhF,OAAA,CAACL,OAAO;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAEb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpF,OAAA;QAAK+E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhF,OAAA;UAAM+E,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EAAC;QAErG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpF,OAAA;UAAM+E,SAAS,EAAC,qFAAqF;UAAAC,QAAA,EAAC;QAEtG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjF,WAAW,iBACVH,OAAA;MAAK+E,SAAS,EAAC,kEAAkE;MAAAC,QAAA,gBAC/EhF,OAAA,CAACF,SAAS;QAACiF,SAAS,EAAC;MAA6B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDpF,OAAA;QAAG+E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CACN,EAGA5E,cAAc,iBACbR,OAAA;MAAK+E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhF,OAAA;QAAI+E,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3EpF,OAAA;QAAK+E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDhF,OAAA;UAAK8D,GAAG,EAAEtD,cAAe;UAAC6E,GAAG,EAAC,qBAAqB;UAACN,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpF,OAAA;MAAK+E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhF,OAAA;QAAI+E,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC1EhF,OAAA,CAACJ,UAAU;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wBAEhB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpF,OAAA;QAAK+E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDM,MAAM,CAACC,OAAO,CAAC7E,cAAc,CAAC,CAAC8E,GAAG,CAAC,CAAC,CAACT,SAAS,EAAEU,KAAK,CAAC,kBACrDzF,OAAA;UAAqB+E,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtHhF,OAAA;YAAK+E,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtChF,OAAA;cAAK+E,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAElE,UAAU,CAACiE,SAAS;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvDpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAG+E,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAAED,SAAS,CAACW,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9FpF,OAAA;gBAAG+E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAES;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAPEL,SAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA;MAAK+E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhF,OAAA;QAAI+E,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC1EhF,OAAA,CAACH,SAAS;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEf;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpF,OAAA;QAAK+E,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAChD1E,gBAAgB,CAACuB,MAAM,KAAK,CAAC,gBAC5B7B,OAAA;UAAK+E,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7ChF,OAAA;YAAAgF,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxBpF,OAAA;YAAG+E,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,GAEN9E,gBAAgB,CAACkF,GAAG,CAAEG,SAAS,iBAC7B3F,OAAA;UAAwB+E,SAAS,EAAC,yFAAyF;UAAAC,QAAA,gBACzHhF,OAAA;YAAK+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDhF,OAAA;cAAM+E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpC,IAAIxC,IAAI,CAACmD,SAAS,CAACjD,SAAS,CAAC,CAACkD,kBAAkB,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACPpF,OAAA;cAAM+E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GACjDW,SAAS,CAACjE,OAAO,CAACG,MAAM,EAAC,eAC5B;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBW,SAAS,CAACjE,OAAO,CAAC8D,GAAG,CAAC,CAACvC,MAAM,EAAEkB,KAAK,kBACnCnE,OAAA;cAAiB+E,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACpEhF,OAAA;gBAAM+E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvChF,OAAA;kBAAAgF,QAAA,EAAOlE,UAAU,CAACmC,MAAM,CAAClB,KAAK;gBAAC;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvCpF,OAAA;kBAAM+E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE/B,MAAM,CAAClB,KAAK,CAAC2D,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACPpF,OAAA;gBACE+E,SAAS,EAAC,aAAa;gBACvBc,KAAK,EAAE;kBAAEC,KAAK,EAAEhB,kBAAkB,CAAC7B,MAAM,CAACjB,UAAU;gBAAE,CAAE;gBAAAgD,QAAA,GAEvD,CAAC/B,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAVCjB,KAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAzBEO,SAAS,CAACpD,EAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BjB,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAjQIJ,aAAa;AAAA8F,EAAA,GAAb9F,aAAa;AAmQnB,eAAeA,aAAa;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}