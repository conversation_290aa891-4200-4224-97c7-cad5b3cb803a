{"version": 3, "names": ["_regexpuCore", "require", "_core", "_helperAnnotateAsPure", "_semver", "_features", "_util", "version<PERSON>ey", "createRegExpFeaturePlugin", "name", "feature", "options", "manipulateOptions", "pre", "_file$get", "file", "features", "get", "<PERSON><PERSON><PERSON>", "newFeatures", "enableFeature", "FEATURES", "useUnicodeFlag", "runtime", "unicodeFlag", "set", "undefined", "has", "runtimeKey", "hasFeature", "duplicateNamedCaptureGroups", "Error", "semver", "lt", "visitor", "RegExpLiteral", "path", "_file$get2", "node", "regexpuOptions", "generateRegexpuOptions", "pattern", "canSkipRegexpu", "namedCaptureGroups", "__proto__", "namedGroups", "onNamedGroup", "index", "prev", "Array", "isArray", "push", "newFlags", "modifiers", "onNewFlags", "flags", "rewritePattern", "Object", "keys", "length", "isRegExpTest", "call", "t", "callExpression", "addHelper", "valueToNode", "annotateAsPure", "replaceWith", "transformFlags", "parentPath", "isMemberExpression", "object", "computed", "isIdentifier"], "sources": ["../src/index.ts"], "sourcesContent": ["import rewritePattern from \"regexpu-core\";\nimport { types as t, type PluginObject, type NodePath } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport semver from \"semver\";\n\nimport {\n  featuresKey,\n  FEATURES,\n  enableFeature,\n  runtimeKey,\n  hasFeature,\n} from \"./features.ts\";\nimport {\n  generateRegexpuOptions,\n  canSkipRegexpu,\n  transformFlags,\n} from \"./util.ts\";\n\nconst versionKey = \"@babel/plugin-regexp-features/version\";\n\nexport interface Options {\n  name: string;\n  feature: keyof typeof FEATURES;\n  options?: {\n    useUnicodeFlag?: boolean;\n    runtime?: boolean;\n  };\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n}\n\nexport function createRegExpFeaturePlugin({\n  name,\n  feature,\n  options = {},\n  manipulateOptions = () => {},\n}: Options): PluginObject {\n  return {\n    name,\n\n    manipulateOptions,\n\n    pre() {\n      const { file } = this;\n      const features = file.get(featuresKey) ?? 0;\n      let newFeatures = enableFeature(features, FEATURES[feature]);\n\n      const { useUnicodeFlag, runtime } = options;\n      if (useUnicodeFlag === false) {\n        newFeatures = enableFeature(newFeatures, FEATURES.unicodeFlag);\n      }\n      if (newFeatures !== features) {\n        file.set(featuresKey, newFeatures);\n      }\n\n      if (runtime !== undefined) {\n        if (\n          file.has(runtimeKey) &&\n          file.get(runtimeKey) !== runtime &&\n          (process.env.BABEL_8_BREAKING ||\n            // This check. Is necessary because in Babel 7 we allow multiple\n            // copies of transform-named-capturing-groups-regex with\n            // conflicting 'runtime' options.\n            hasFeature(newFeatures, FEATURES.duplicateNamedCaptureGroups))\n        ) {\n          throw new Error(\n            `The 'runtime' option must be the same for ` +\n              `'@babel/plugin-transform-named-capturing-groups-regex' and ` +\n              `'@babel/plugin-transform-duplicate-named-capturing-groups-regex'.`,\n          );\n        }\n\n        if (process.env.BABEL_8_BREAKING) {\n          file.set(runtimeKey, runtime);\n        } else if (\n          // This check. Is necessary because in Babel 7 we allow multiple\n          // copies of transform-named-capturing-groups-regex with\n          // conflicting 'runtime' options.\n          feature === \"namedCaptureGroups\"\n        ) {\n          if (!runtime || !file.has(runtimeKey)) file.set(runtimeKey, runtime);\n        } else {\n          file.set(runtimeKey, runtime);\n        }\n      }\n\n      if (!process.env.BABEL_8_BREAKING) {\n        // Until 7.21.4, we used to encode the version as a number.\n        // If file.get(versionKey) is a number, it has thus been\n        // set by an older version of this plugin.\n        if (typeof file.get(versionKey) === \"number\") {\n          file.set(versionKey, PACKAGE_JSON.version);\n          return;\n        }\n      }\n      if (\n        !file.get(versionKey) ||\n        semver.lt(file.get(versionKey), PACKAGE_JSON.version)\n      ) {\n        file.set(versionKey, PACKAGE_JSON.version);\n      }\n    },\n\n    visitor: {\n      RegExpLiteral(path) {\n        const { node } = path;\n        const { file } = this;\n        const features = file.get(featuresKey);\n        const runtime = file.get(runtimeKey) ?? true;\n\n        const regexpuOptions = generateRegexpuOptions(node.pattern, features);\n        if (canSkipRegexpu(node, regexpuOptions)) {\n          return;\n        }\n\n        const namedCaptureGroups: Record<string, number | number[]> = {\n          __proto__: null,\n        };\n        if (regexpuOptions.namedGroups === \"transform\") {\n          regexpuOptions.onNamedGroup = (name, index) => {\n            const prev = namedCaptureGroups[name];\n            if (typeof prev === \"number\") {\n              namedCaptureGroups[name] = [prev, index];\n            } else if (Array.isArray(prev)) {\n              prev.push(index);\n            } else {\n              namedCaptureGroups[name] = index;\n            }\n          };\n        }\n\n        let newFlags;\n        if (regexpuOptions.modifiers === \"transform\") {\n          regexpuOptions.onNewFlags = flags => {\n            newFlags = flags;\n          };\n        }\n\n        node.pattern = rewritePattern(node.pattern, node.flags, regexpuOptions);\n\n        if (\n          regexpuOptions.namedGroups === \"transform\" &&\n          Object.keys(namedCaptureGroups).length > 0 &&\n          runtime &&\n          !isRegExpTest(path)\n        ) {\n          const call = t.callExpression(this.addHelper(\"wrapRegExp\"), [\n            node,\n            t.valueToNode(namedCaptureGroups),\n          ]);\n          annotateAsPure(call);\n\n          path.replaceWith(call);\n        }\n\n        node.flags = transformFlags(regexpuOptions, newFlags ?? node.flags);\n      },\n    },\n  };\n}\n\nfunction isRegExpTest(path: NodePath<t.RegExpLiteral>) {\n  return (\n    path.parentPath.isMemberExpression({\n      object: path.node,\n      computed: false,\n    }) && path.parentPath.get(\"property\").isIdentifier({ name: \"test\" })\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,SAAA,GAAAJ,OAAA;AAOA,IAAAK,KAAA,GAAAL,OAAA;AAMA,MAAMM,UAAU,GAAG,uCAAuC;AAYnD,SAASC,yBAAyBA,CAAC;EACxCC,IAAI;EACJC,OAAO;EACPC,OAAO,GAAG,CAAC,CAAC;EACZC,iBAAiB,GAAGA,CAAA,KAAM,CAAC;AACpB,CAAC,EAAgB;EACxB,OAAO;IACLH,IAAI;IAEJG,iBAAiB;IAEjBC,GAAGA,CAAA,EAAG;MAAA,IAAAC,SAAA;MACJ,MAAM;QAAEC;MAAK,CAAC,GAAG,IAAI;MACrB,MAAMC,QAAQ,IAAAF,SAAA,GAAGC,IAAI,CAACE,GAAG,CAACC,qBAAW,CAAC,YAAAJ,SAAA,GAAI,CAAC;MAC3C,IAAIK,WAAW,GAAG,IAAAC,uBAAa,EAACJ,QAAQ,EAAEK,kBAAQ,CAACX,OAAO,CAAC,CAAC;MAE5D,MAAM;QAAEY,cAAc;QAAEC;MAAQ,CAAC,GAAGZ,OAAO;MAC3C,IAAIW,cAAc,KAAK,KAAK,EAAE;QAC5BH,WAAW,GAAG,IAAAC,uBAAa,EAACD,WAAW,EAAEE,kBAAQ,CAACG,WAAW,CAAC;MAChE;MACA,IAAIL,WAAW,KAAKH,QAAQ,EAAE;QAC5BD,IAAI,CAACU,GAAG,CAACP,qBAAW,EAAEC,WAAW,CAAC;MACpC;MAEA,IAAII,OAAO,KAAKG,SAAS,EAAE;QACzB,IACEX,IAAI,CAACY,GAAG,CAACC,oBAAU,CAAC,IACpBb,IAAI,CAACE,GAAG,CAACW,oBAAU,CAAC,KAAKL,OAAO,IAK9B,IAAAM,oBAAU,EAACV,WAAW,EAAEE,kBAAQ,CAACS,2BAA2B,CAAC,EAC/D;UACA,MAAM,IAAIC,KAAK,CACb,4CAA4C,GAC1C,6DAA6D,GAC7D,mEACJ,CAAC;QACH;QAIO,IAILrB,OAAO,KAAK,oBAAoB,EAChC;UACA,IAAI,CAACa,OAAO,IAAI,CAACR,IAAI,CAACY,GAAG,CAACC,oBAAU,CAAC,EAAEb,IAAI,CAACU,GAAG,CAACG,oBAAU,EAAEL,OAAO,CAAC;QACtE,CAAC,MAAM;UACLR,IAAI,CAACU,GAAG,CAACG,oBAAU,EAAEL,OAAO,CAAC;QAC/B;MACF;MAEmC;QAIjC,IAAI,OAAOR,IAAI,CAACE,GAAG,CAACV,UAAU,CAAC,KAAK,QAAQ,EAAE;UAC5CQ,IAAI,CAACU,GAAG,CAAClB,UAAU,UAAsB,CAAC;UAC1C;QACF;MACF;MACA,IACE,CAACQ,IAAI,CAACE,GAAG,CAACV,UAAU,CAAC,IACrByB,OAAM,CAACC,EAAE,CAAClB,IAAI,CAACE,GAAG,CAACV,UAAU,CAAC,UAAsB,CAAC,EACrD;QACAQ,IAAI,CAACU,GAAG,CAAClB,UAAU,UAAsB,CAAC;MAC5C;IACF,CAAC;IAED2B,OAAO,EAAE;MACPC,aAAaA,CAACC,IAAI,EAAE;QAAA,IAAAC,UAAA;QAClB,MAAM;UAAEC;QAAK,CAAC,GAAGF,IAAI;QACrB,MAAM;UAAErB;QAAK,CAAC,GAAG,IAAI;QACrB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,GAAG,CAACC,qBAAW,CAAC;QACtC,MAAMK,OAAO,IAAAc,UAAA,GAAGtB,IAAI,CAACE,GAAG,CAACW,oBAAU,CAAC,YAAAS,UAAA,GAAI,IAAI;QAE5C,MAAME,cAAc,GAAG,IAAAC,4BAAsB,EAACF,IAAI,CAACG,OAAO,EAAEzB,QAAQ,CAAC;QACrE,IAAI,IAAA0B,oBAAc,EAACJ,IAAI,EAAEC,cAAc,CAAC,EAAE;UACxC;QACF;QAEA,MAAMI,kBAAqD,GAAG;UAC5DC,SAAS,EAAE;QACb,CAAC;QACD,IAAIL,cAAc,CAACM,WAAW,KAAK,WAAW,EAAE;UAC9CN,cAAc,CAACO,YAAY,GAAG,CAACrC,IAAI,EAAEsC,KAAK,KAAK;YAC7C,MAAMC,IAAI,GAAGL,kBAAkB,CAAClC,IAAI,CAAC;YACrC,IAAI,OAAOuC,IAAI,KAAK,QAAQ,EAAE;cAC5BL,kBAAkB,CAAClC,IAAI,CAAC,GAAG,CAACuC,IAAI,EAAED,KAAK,CAAC;YAC1C,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;cAC9BA,IAAI,CAACG,IAAI,CAACJ,KAAK,CAAC;YAClB,CAAC,MAAM;cACLJ,kBAAkB,CAAClC,IAAI,CAAC,GAAGsC,KAAK;YAClC;UACF,CAAC;QACH;QAEA,IAAIK,QAAQ;QACZ,IAAIb,cAAc,CAACc,SAAS,KAAK,WAAW,EAAE;UAC5Cd,cAAc,CAACe,UAAU,GAAGC,KAAK,IAAI;YACnCH,QAAQ,GAAGG,KAAK;UAClB,CAAC;QACH;QAEAjB,IAAI,CAACG,OAAO,GAAGe,YAAc,CAAClB,IAAI,CAACG,OAAO,EAAEH,IAAI,CAACiB,KAAK,EAAEhB,cAAc,CAAC;QAEvE,IACEA,cAAc,CAACM,WAAW,KAAK,WAAW,IAC1CY,MAAM,CAACC,IAAI,CAACf,kBAAkB,CAAC,CAACgB,MAAM,GAAG,CAAC,IAC1CpC,OAAO,IACP,CAACqC,YAAY,CAACxB,IAAI,CAAC,EACnB;UACA,MAAMyB,IAAI,GAAGC,WAAC,CAACC,cAAc,CAAC,IAAI,CAACC,SAAS,CAAC,YAAY,CAAC,EAAE,CAC1D1B,IAAI,EACJwB,WAAC,CAACG,WAAW,CAACtB,kBAAkB,CAAC,CAClC,CAAC;UACF,IAAAuB,6BAAc,EAACL,IAAI,CAAC;UAEpBzB,IAAI,CAAC+B,WAAW,CAACN,IAAI,CAAC;QACxB;QAEAvB,IAAI,CAACiB,KAAK,GAAG,IAAAa,oBAAc,EAAC7B,cAAc,EAAEa,QAAQ,WAARA,QAAQ,GAAId,IAAI,CAACiB,KAAK,CAAC;MACrE;IACF;EACF,CAAC;AACH;AAEA,SAASK,YAAYA,CAACxB,IAA+B,EAAE;EACrD,OACEA,IAAI,CAACiC,UAAU,CAACC,kBAAkB,CAAC;IACjCC,MAAM,EAAEnC,IAAI,CAACE,IAAI;IACjBkC,QAAQ,EAAE;EACZ,CAAC,CAAC,IAAIpC,IAAI,CAACiC,UAAU,CAACpD,GAAG,CAAC,UAAU,CAAC,CAACwD,YAAY,CAAC;IAAEhE,IAAI,EAAE;EAAO,CAAC,CAAC;AAExE", "ignoreList": []}