{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { motion } from 'framer-motion';\nimport { <PERSON>a<PERSON>ooth, FaVideo, FaUser, FaChartBar, FaWifi, FaBan } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [patientInfo, setPatientInfo] = useState({\n    name: '<PERSON>',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to dentist successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = results => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1 flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaTooth, {\n                    className: \"mr-3 text-[#20B2AA]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 21\n                  }, this), \"Intraoral Patient Dashboard\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Real-time dental analysis and consultation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                className: `flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 shadow-sm ${isConnected ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'}`,\n                children: [isConnected ? /*#__PURE__*/_jsxDEV(FaWifi, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 34\n                }, this) : /*#__PURE__*/_jsxDEV(FaBan, {\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 64\n                }, this), isConnected ? 'Connected to Dentist' : 'Disconnected']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 94,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 97,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: patientInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 98,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Video Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Detections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: detectionResults.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 130,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Analysis Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isAnalyzing ? 'Running' : 'Ready'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Live Video Consultation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n                  onConnectionStatus: handleConnectionStatus,\n                  onStartAnalysis: startAnalysis,\n                  onImageCaptured: handleImageCaptured\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n                  patient: patientInfo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"AI Dental Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n                  onResults: handleDetectionResults,\n                  isAnalyzing: isAnalyzing,\n                  currentImage: currentCapturedImage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Analysis Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n                  results: detectionResults,\n                  isAnalyzing: isAnalyzing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"uWyDeOcdKrAN6UfbTi9fgjAQcRY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ToastContainer", "toast", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaVideo", "FaUser", "FaChartBar", "FaWifi", "FaBan", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "jsxDEV", "_jsxDEV", "App", "_s", "isConnected", "setIsConnected", "patientInfo", "setPatientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "handleConnectionStatus", "status", "success", "error", "handleDetectionResults", "results", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "handleImageCaptured", "imageSrc", "className", "children", "div", "initial", "opacity", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "whileHover", "scale", "y", "delay", "x", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "patient", "onResults", "currentImage", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport { motion } from 'framer-motion';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>hart<PERSON>ar, FaWifi, FaBan } from 'react-icons/fa';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [patientInfo, setPatientInfo] = useState({\r\n    name: '<PERSON>',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to dentist successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    if (results.length > 0) {\r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  const handleImageCaptured = (imageSrc) => {\r\n    setCurrentCapturedImage(imageSrc);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1 flex items-center\">\r\n                    <FaTooth className=\"mr-3 text-[#20B2AA]\" />\r\n                    Intraoral Patient Dashboard\r\n                  </h1>\r\n                  <p className=\"text-[#333333]\">Real-time dental analysis and consultation</p>\r\n                </div>\r\n                <motion.div\r\n                  whileHover={{ scale: 1.05 }}\r\n                  className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 shadow-sm ${\r\n                    isConnected\r\n                      ? 'bg-green-100 text-green-800 border border-green-200'\r\n                      : 'bg-red-100 text-red-800 border border-red-200'\r\n                  }`}\r\n                >\r\n                  {isConnected ? <FaWifi className=\"mr-2\" /> : <FaBan className=\"mr-2\" />}\r\n                  {isConnected ? 'Connected to Dentist' : 'Disconnected'}\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Stats Cards */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.1 }}\r\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n              >\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaUser className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Patient</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{patientInfo.name}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaVideo className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Video Status</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isConnected ? 'Live' : 'Offline'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaTooth className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Detections</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{detectionResults.length}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\r\n                      <FaChartBar className=\"h-6 w-6\" />\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <h2 className=\"text-sm font-medium text-gray-500\">Analysis Status</h2>\r\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isAnalyzing ? 'Running' : 'Ready'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Main Content Grid */}\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\r\n                {/* Video Call Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaVideo className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Live Video Consultation</h2>\r\n                  </div>\r\n                  <VideoCall\r\n                    onConnectionStatus={handleConnectionStatus}\r\n                    onStartAnalysis={startAnalysis}\r\n                    onImageCaptured={handleImageCaptured}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Patient Info Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, x: 20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: 0.2 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaUser className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Information</h2>\r\n                  </div>\r\n                  <PatientInfo patient={patientInfo} />\r\n                </motion.div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                {/* AI Analysis Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaTooth className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">AI Dental Analysis</h2>\r\n                  </div>\r\n                  <YOLODetection\r\n                    onResults={handleDetectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                    currentImage={currentCapturedImage}\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Results Section */}\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.3 }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\r\n                >\r\n                  <div className=\"flex items-center mb-6\">\r\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n                      <FaChartBar className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Analysis Results</h2>\r\n                  </div>\r\n                  <AnalysisResults\r\n                    results={detectionResults}\r\n                    isAnalyzing={isAnalyzing}\r\n                  />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AACpF,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC;IAC7CuB,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAEtE,MAAMiC,sBAAsB,GAAIC,MAAM,IAAK;IACzCd,cAAc,CAACc,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACV/B,KAAK,CAACgC,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACLhC,KAAK,CAACiC,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;IAC1CV,mBAAmB,CAACU,OAAO,CAAC;IAC5BR,cAAc,CAAC,KAAK,CAAC;IAErB,IAAIQ,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGF,OAAO,CAACG,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtDxC,KAAK,CAACyC,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BhB,cAAc,CAAC,IAAI,CAAC;IACpB3B,KAAK,CAACyC,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;IACxChB,uBAAuB,CAACgB,QAAQ,CAAC;EACnC,CAAC;EAED,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvClC,OAAA;MAAKiC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDlC,OAAA;QAAMiC,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAN,QAAA,gBAG9BlC,OAAA;cAAKiC,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FlC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAIiC,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBAClFlC,OAAA,CAACX,OAAO;oBAAC4C,SAAS,EAAC;kBAAqB;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,+BAE7C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAGiC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACN5C,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTU,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5Bb,SAAS,EAAE,8FACT9B,WAAW,GACP,qDAAqD,GACrD,+CAA+C,EAClD;gBAAA+B,QAAA,GAEF/B,WAAW,gBAAGH,OAAA,CAACP,MAAM;kBAACwC,SAAS,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACN,KAAK;kBAACuC,SAAS,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACtEzC,WAAW,GAAG,sBAAsB,GAAG,cAAc;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGN5C,OAAA,CAACZ,MAAM,CAAC+C,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3Bf,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErElC,OAAA;gBAAKiC,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlC,OAAA;kBAAKiC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClC,OAAA;oBAAKiC,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElC,OAAA,CAACT,MAAM;sBAAC0C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACN5C,OAAA;oBAAKiC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlC,OAAA;sBAAIiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9D5C,OAAA;sBAAGiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE7B,WAAW,CAACE;oBAAI;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKiC,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlC,OAAA;kBAAKiC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClC,OAAA;oBAAKiC,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElC,OAAA,CAACV,OAAO;sBAAC2C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN5C,OAAA;oBAAKiC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlC,OAAA;sBAAIiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnE5C,OAAA;sBAAGiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE/B,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKiC,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlC,OAAA;kBAAKiC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClC,OAAA;oBAAKiC,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElC,OAAA,CAACX,OAAO;sBAAC4C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN5C,OAAA;oBAAKiC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlC,OAAA;sBAAIiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjE5C,OAAA;sBAAGiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvB,gBAAgB,CAACY;oBAAM;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKiC,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIlC,OAAA;kBAAKiC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClC,OAAA;oBAAKiC,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvElC,OAAA,CAACR,UAAU;sBAACyC,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACN5C,OAAA;oBAAKiC,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBlC,OAAA;sBAAIiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtE5C,OAAA;sBAAGiC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAErB,WAAW,GAAG,SAAS,GAAG;oBAAO;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGb5C,OAAA;cAAKiC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDlC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3Bf,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlC,OAAA;kBAAKiC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClC,OAAA;oBAAKiC,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElC,OAAA,CAACV,OAAO;sBAAC2C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN5C,OAAA;oBAAIiC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACN5C,OAAA,CAACL,SAAS;kBACRuD,kBAAkB,EAAEjC,sBAAuB;kBAC3CkC,eAAe,EAAErB,aAAc;kBAC/BsB,eAAe,EAAErB;gBAAoB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGb5C,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3Bf,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlC,OAAA;kBAAKiC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClC,OAAA;oBAAKiC,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElC,OAAA,CAACT,MAAM;sBAAC0C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACN5C,OAAA;oBAAIiC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACN5C,OAAA,CAACH,WAAW;kBAACwD,OAAO,EAAEhD;gBAAY;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN5C,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDlC,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3Bf,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlC,OAAA;kBAAKiC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClC,OAAA;oBAAKiC,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElC,OAAA,CAACX,OAAO;sBAAC4C,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACN5C,OAAA;oBAAIiC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACN5C,OAAA,CAACJ,aAAa;kBACZ0D,SAAS,EAAEjC,sBAAuB;kBAClCR,WAAW,EAAEA,WAAY;kBACzB0C,YAAY,EAAExC;gBAAqB;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGb5C,OAAA,CAACZ,MAAM,CAAC+C,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3Bf,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HlC,OAAA;kBAAKiC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClC,OAAA;oBAAKiC,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1ElC,OAAA,CAACR,UAAU;sBAACyC,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACN5C,OAAA;oBAAIiC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACN5C,OAAA,CAACF,eAAe;kBACdwB,OAAO,EAAEX,gBAAiB;kBAC1BE,WAAW,EAAEA;gBAAY;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN5C,OAAA,CAACd,cAAc;MACbsE,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC1C,EAAA,CAlOQD,GAAG;AAAAiE,EAAA,GAAHjE,GAAG;AAoOZ,eAAeA,GAAG;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}