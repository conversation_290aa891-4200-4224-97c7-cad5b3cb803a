# Intraoral Patient Dashboard

A real-time dental analysis dashboard with USB camera support and **real YOLOv8 AI detection** (no mock data).

## Features

- **USB Camera Support**: Automatically detects and allows selection of USB cameras
- **Real-time Video Streaming**: Live video consultation with patient
- **AI Dental Analysis**: YOLOv8-powered detection of dental conditions (via Python backend)
- **Automatic Image Capture**: Configurable auto-capture intervals
- **Analysis History**: Track patient analysis over time
- **Server Status Monitoring**: Real-time connection status to YOLOv8 backend

## Prerequisites

- Node.js (v14 or higher)
- Python 3.8+
- Ultralytics YOLOv8 Python package: `pip install ultralytics`
- Your trained YOLOv8 model file (`best.pt`) in the `intraoral/` directory
- USB camera connected to your computer

## Installation

1. **Install Node.js dependencies:**
   ```bash
   npm install
   ```
2. **Install Python dependencies:**
   ```bash
   pip install ultralytics
   ```
3. **Place your YOLOv8 model**
   - Copy your `best.pt` file to the `intraoral/` directory.

## Usage

### Starting the Application

**Option 1: Start both frontend and backend together (Recommended)**
```bash
npm run dev
```

**Option 2: Start servers separately**
```bash
# Terminal 1 - Start backend server
npm run dev:server

# Terminal 2 - Start frontend
npm run dev:frontend
```

### Camera Setup

1. **Connect USB Camera**: Plug in your USB camera to your computer
2. **Open Application**: Navigate to `http://localhost:3000`
3. **Select Camera**: 
   - Click "Show Settings" in the camera section
   - Select your USB camera from the dropdown
   - The system will auto-detect USB cameras first
4. **Start Streaming**: Click "Start Stream" to begin video capture

### AI Analysis (Real YOLOv8)

1. **Ensure Server is Running**: Check that the YOLOv8 server status shows "Connected"
2. **Start Analysis**: Click "Start Analysis" button
3. **View Results**: Analysis results will appear in the AI Analysis section

## How Real YOLOv8 Detection Works

- When you capture an image, the Node.js backend calls the Python script `yolo_detect.py`.
- The Python script loads your `best.pt` model and runs detection on the image.
- Detection results are returned as JSON and displayed in the dashboard.
- **No mock data is ever used.**

## Troubleshooting

### Camera Issues
- **No cameras detected**: Check USB connection and browser permissions
- **Camera not working**: Try refreshing the page or selecting a different camera
- **Permission denied**: Allow camera access in your browser

### YOLOv8 Server Issues
- **Server not connecting**: Make sure Python and `ultralytics` are installed, and `best.pt` is present
- **Analysis not working**: Check backend logs for Python errors
- **Model not loading**: Check that `best.pt` file exists in the `intraoral/` directory

### Development
- **Frontend only**: `npm run dev:frontend` (runs on port 3000)
- **Backend only**: `npm run dev:server` (runs on port 5000)
- **Both**: `npm run dev` (runs both simultaneously)

## File Structure

```
intraoral/
├── src/
│   ├── components/
│   │   ├── VideoCall.js          # USB camera handling
│   │   ├── YOLODetection.js      # AI analysis display
│   │   └── ...
│   └── App.js                    # Main application
├── server.js                     # Node.js backend (calls Python for YOLO)
├── yolo_detect.py                # Python YOLOv8 detection script
├── best.pt                       # YOLOv8 model file
└── package.json
```

## API Endpoints

- `GET /api/health` - Server health check
- `POST /api/analyze` - Image analysis with YOLOv8

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is part of the Dentlyzer system. 