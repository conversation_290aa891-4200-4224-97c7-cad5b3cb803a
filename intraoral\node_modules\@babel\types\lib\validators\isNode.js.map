{"version": 3, "names": ["_index", "require", "isNode", "node", "VISITOR_KEYS", "type"], "sources": ["../../src/validators/isNode.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function isNode(node: any): node is t.Node {\n  return !!(node && VISITOR_KEYS[node.type]);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGe,SAASC,MAAMA,CAACC,IAAS,EAAkB;EACxD,OAAO,CAAC,EAAEA,IAAI,IAAIC,mBAAY,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC;AAC5C", "ignoreList": []}