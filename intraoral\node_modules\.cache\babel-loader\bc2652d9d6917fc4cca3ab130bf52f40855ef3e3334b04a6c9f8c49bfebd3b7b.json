{"ast": null, "code": "/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({\n  top,\n  left,\n  right,\n  bottom\n}) {\n  return {\n    x: {\n      min: left,\n      max: right\n    },\n    y: {\n      min: top,\n      max: bottom\n    }\n  };\n}\nfunction convertBoxToBoundingBox({\n  x,\n  y\n}) {\n  return {\n    top: y.min,\n    right: x.max,\n    bottom: y.max,\n    left: x.min\n  };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n  if (!transformPoint) return point;\n  const topLeft = transformPoint({\n    x: point.left,\n    y: point.top\n  });\n  const bottomRight = transformPoint({\n    x: point.right,\n    y: point.bottom\n  });\n  return {\n    top: topLeft.y,\n    left: topLeft.x,\n    bottom: bottomRight.y,\n    right: bottomRight.x\n  };\n}\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };", "map": {"version": 3, "names": ["convertBoundingBoxToBox", "top", "left", "right", "bottom", "x", "min", "max", "y", "convertBoxToBoundingBox", "transformBoxPoints", "point", "transformPoint", "topLeft", "bottomRight"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs"], "sourcesContent": ["/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAAC;EAAEC,GAAG;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAQ,CAAC,EAAE;EAC5D,OAAO;IACHC,CAAC,EAAE;MAAEC,GAAG,EAAEJ,IAAI;MAAEK,GAAG,EAAEJ;IAAM,CAAC;IAC5BK,CAAC,EAAE;MAAEF,GAAG,EAAEL,GAAG;MAAEM,GAAG,EAAEH;IAAO;EAC/B,CAAC;AACL;AACA,SAASK,uBAAuBA,CAAC;EAAEJ,CAAC;EAAEG;AAAE,CAAC,EAAE;EACvC,OAAO;IAAEP,GAAG,EAAEO,CAAC,CAACF,GAAG;IAAEH,KAAK,EAAEE,CAAC,CAACE,GAAG;IAAEH,MAAM,EAAEI,CAAC,CAACD,GAAG;IAAEL,IAAI,EAAEG,CAAC,CAACC;EAAI,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,kBAAkBA,CAACC,KAAK,EAAEC,cAAc,EAAE;EAC/C,IAAI,CAACA,cAAc,EACf,OAAOD,KAAK;EAChB,MAAME,OAAO,GAAGD,cAAc,CAAC;IAAEP,CAAC,EAAEM,KAAK,CAACT,IAAI;IAAEM,CAAC,EAAEG,KAAK,CAACV;EAAI,CAAC,CAAC;EAC/D,MAAMa,WAAW,GAAGF,cAAc,CAAC;IAAEP,CAAC,EAAEM,KAAK,CAACR,KAAK;IAAEK,CAAC,EAAEG,KAAK,CAACP;EAAO,CAAC,CAAC;EACvE,OAAO;IACHH,GAAG,EAAEY,OAAO,CAACL,CAAC;IACdN,IAAI,EAAEW,OAAO,CAACR,CAAC;IACfD,MAAM,EAAEU,WAAW,CAACN,CAAC;IACrBL,KAAK,EAAEW,WAAW,CAACT;EACvB,CAAC;AACL;AAEA,SAASL,uBAAuB,EAAES,uBAAuB,EAAEC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}