{"version": 3, "names": ["TopLevelOptions", "exports", "config<PERSON><PERSON>", "corejs", "debug", "exclude", "forceAllTransforms", "ignoreBrowserslistConfig", "include", "modules", "shippedProposals", "targets", "useBuiltIns", "browserslistEnv", "Object", "assign", "bugfixes", "loose", "spec", "ModulesOption", "false", "auto", "amd", "commonjs", "cjs", "systemjs", "umd", "UseBuiltInsOption", "entry", "usage"], "sources": ["../src/options.ts"], "sourcesContent": ["export const TopLevelOptions = {\n  configPath: \"configPath\",\n  corejs: \"corejs\",\n  debug: \"debug\",\n  exclude: \"exclude\",\n  forceAllTransforms: \"forceAllTransforms\",\n  ignoreBrowserslistConfig: \"ignoreBrowserslistConfig\",\n  include: \"include\",\n  modules: \"modules\",\n  shippedProposals: \"shippedProposals\",\n  targets: \"targets\",\n  useBuiltIns: \"useBuiltIns\",\n  browserslistEnv: \"browserslistEnv\",\n} as const;\n\nif (!process.env.BABEL_8_BREAKING) {\n  Object.assign(TopLevelOptions, {\n    bugfixes: \"bugfixes\",\n    loose: \"loose\",\n    spec: \"spec\",\n  });\n}\n\nexport const ModulesOption = {\n  false: false,\n  auto: \"auto\",\n  amd: \"amd\",\n  commonjs: \"commonjs\",\n  cjs: \"cjs\",\n  systemjs: \"systemjs\",\n  umd: \"umd\",\n} as const;\n\nexport const UseBuiltInsOption = {\n  false: false,\n  entry: \"entry\",\n  usage: \"usage\",\n} as const;\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG;EAC7BE,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,kBAAkB,EAAE,oBAAoB;EACxCC,wBAAwB,EAAE,0BAA0B;EACpDC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,gBAAgB,EAAE,kBAAkB;EACpCC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE;AACnB,CAAU;AAEyB;EACjCC,MAAM,CAACC,MAAM,CAACf,eAAe,EAAE;IAC7BgB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEO,MAAMC,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,GAAG;EAC3BC,KAAK,EAAE,KAAK;EACZC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE;AACP,CAAU;AAEH,MAAMC,iBAAiB,GAAA1B,OAAA,CAAA0B,iBAAA,GAAG;EAC/BP,KAAK,EAAE,KAAK;EACZQ,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAU", "ignoreList": []}