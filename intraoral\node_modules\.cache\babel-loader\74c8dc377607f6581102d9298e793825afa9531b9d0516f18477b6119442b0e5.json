{"ast": null, "code": "/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mix = (from, to, progress) => -progress * from + progress * to + from;\nexport { mix };", "map": {"version": 3, "names": ["mix", "from", "to", "progress"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/utils/mix.mjs"], "sourcesContent": ["/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mix = (from, to, progress) => -progress * from + progress * to + from;\n\nexport { mix };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,GAAG,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,QAAQ,KAAK,CAACA,QAAQ,GAAGF,IAAI,GAAGE,QAAQ,GAAGD,EAAE,GAAGD,IAAI;AAE3E,SAASD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}