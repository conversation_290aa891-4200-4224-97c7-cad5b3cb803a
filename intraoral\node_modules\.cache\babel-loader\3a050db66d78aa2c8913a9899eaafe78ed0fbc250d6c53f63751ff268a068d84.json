{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\PatientInfo.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FaUser, FaEdit, FaSave, FaTimes, FaHistory, FaPhone, FaCalendar, FaFileAlt } from 'react-icons/fa';\nimport './PatientInfo.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientInfo = ({\n  patient\n}) => {\n  _s();\n  const [isEditing, setIsEditing] = useState(false);\n  const [patientData, setPatientData] = useState(patient);\n  const handleEdit = () => {\n    setIsEditing(true);\n  };\n  const handleSave = () => {\n    setIsEditing(false);\n    // In a real app, you would save to backend here\n  };\n  const handleCancel = () => {\n    setPatientData(patient);\n    setIsEditing(false);\n  };\n  const handleInputChange = (field, value) => {\n    setPatientData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const consultationHistory = [{\n    id: 1,\n    date: '2024-01-15',\n    type: 'Regular Checkup',\n    dentist: 'Dr. Smith',\n    status: 'Completed'\n  }, {\n    id: 2,\n    date: '2023-12-10',\n    type: 'Cavity Filling',\n    dentist: 'Dr. Johnson',\n    status: 'Completed'\n  }, {\n    id: 3,\n    date: '2023-11-05',\n    type: 'Cleaning',\n    dentist: 'Dr. Williams',\n    status: 'Completed'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-[#0077B6] flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), \"Patient Information\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), !isEditing ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"flex items-center px-3 py-1 text-sm bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n        onClick: handleEdit,\n        children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n          className: \"mr-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this), \"Edit\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center px-3 py-1 text-sm bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300\",\n          onClick: handleSave,\n          children: [/*#__PURE__*/_jsxDEV(FaSave, {\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this), \"Save\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center px-3 py-1 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-300\",\n          onClick: handleCancel,\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), \"Cancel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Patient ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: patientData.id,\n          onChange: e => handleInputChange('id', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-sm text-[#333333] font-medium\",\n          children: patientData.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Full Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: patientData.name,\n          onChange: e => handleInputChange('name', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-sm text-[#333333] font-medium\",\n          children: patientData.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: patientData.age,\n          onChange: e => handleInputChange('age', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-sm text-[#333333] font-medium\",\n          children: [patientData.age, \" years\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Last Visit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: patientData.lastVisit,\n          onChange: e => handleInputChange('lastVisit', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"block text-sm text-[#333333] font-medium\",\n          children: patientData.lastVisit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] rounded-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6] mb-3\",\n        children: \"Current Consultation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-green-500 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-[#333333]\",\n            children: \"Active Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: [\"Started: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6] mb-3 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaHistory, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), \"Recent Consultations\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: consultationHistory.map(consultation => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-[#333333]\",\n                children: consultation.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: consultation.dentist\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: consultation.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-2 py-1 text-xs font-medium rounded-full ${consultation.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                children: consultation.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)\n        }, consultation.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6] mb-3\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), \"View Medical History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), \"Contact Dentist\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaCalendar, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), \"Schedule Next Visit\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientInfo, \"gPFKxqWEgX0pxuo2cwO+9DzRA2g=\");\n_c = PatientInfo;\nexport default PatientInfo;\nvar _c;\n$RefreshReg$(_c, \"PatientInfo\");", "map": {"version": 3, "names": ["React", "useState", "FaUser", "FaEdit", "FaSave", "FaTimes", "FaHistory", "FaPhone", "FaCalendar", "FaFileAlt", "jsxDEV", "_jsxDEV", "PatientInfo", "patient", "_s", "isEditing", "setIsEditing", "patientData", "setPatientData", "handleEdit", "handleSave", "handleCancel", "handleInputChange", "field", "value", "prev", "consultationHistory", "id", "date", "type", "dentist", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "e", "target", "name", "age", "lastVisit", "Date", "toLocaleTimeString", "map", "consultation", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/PatientInfo.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { FaUser, FaEdit, FaSave, FaTimes, FaHistory, FaPhone, FaCalendar, FaFileAlt } from 'react-icons/fa';\r\nimport './PatientInfo.css';\r\n\r\nconst PatientInfo = ({ patient }) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [patientData, setPatientData] = useState(patient);\r\n\r\n  const handleEdit = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  const handleSave = () => {\r\n    setIsEditing(false);\r\n    // In a real app, you would save to backend here\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setPatientData(patient);\r\n    setIsEditing(false);\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setPatientData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const consultationHistory = [\r\n    {\r\n      id: 1,\r\n      date: '2024-01-15',\r\n      type: 'Regular Checkup',\r\n      dentist: '<PERSON><PERSON> <PERSON>',\r\n      status: 'Completed'\r\n    },\r\n    {\r\n      id: 2,\r\n      date: '2023-12-10',\r\n      type: '<PERSON><PERSON><PERSON>lling',\r\n      dentist: 'Dr<PERSON> <PERSON>',\r\n      status: 'Completed'\r\n    },\r\n    {\r\n      id: 3,\r\n      date: '2023-11-05',\r\n      type: 'Cleaning',\r\n      dentist: 'Dr. Williams',\r\n      status: 'Completed'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-semibold text-[#0077B6] flex items-center gap-2\">\r\n          <FaUser />\r\n          Patient Information\r\n        </h3>\r\n        {!isEditing ? (\r\n          <button \r\n            className=\"flex items-center px-3 py-1 text-sm bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n            onClick={handleEdit}\r\n          >\r\n            <FaEdit className=\"mr-1\" />\r\n            Edit\r\n          </button>\r\n        ) : (\r\n          <div className=\"flex gap-2\">\r\n            <button \r\n              className=\"flex items-center px-3 py-1 text-sm bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300\"\r\n              onClick={handleSave}\r\n            >\r\n              <FaSave className=\"mr-1\" />\r\n              Save\r\n            </button>\r\n            <button \r\n              className=\"flex items-center px-3 py-1 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-300\"\r\n              onClick={handleCancel}\r\n            >\r\n              <FaTimes className=\"mr-1\" />\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Patient Details Grid */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n        <div className=\"space-y-2\">\r\n          <label className=\"block text-sm font-medium text-gray-700\">Patient ID</label>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={patientData.id}\r\n              onChange={(e) => handleInputChange('id', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n            />\r\n          ) : (\r\n            <span className=\"block text-sm text-[#333333] font-medium\">{patientData.id}</span>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"text\"\r\n              value={patientData.name}\r\n              onChange={(e) => handleInputChange('name', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n            />\r\n          ) : (\r\n            <span className=\"block text-sm text-[#333333] font-medium\">{patientData.name}</span>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <label className=\"block text-sm font-medium text-gray-700\">Age</label>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"number\"\r\n              value={patientData.age}\r\n              onChange={(e) => handleInputChange('age', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n            />\r\n          ) : (\r\n            <span className=\"block text-sm text-[#333333] font-medium\">{patientData.age} years</span>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <label className=\"block text-sm font-medium text-gray-700\">Last Visit</label>\r\n          {isEditing ? (\r\n            <input\r\n              type=\"date\"\r\n              value={patientData.lastVisit}\r\n              onChange={(e) => handleInputChange('lastVisit', e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]\"\r\n            />\r\n          ) : (\r\n            <span className=\"block text-sm text-[#333333] font-medium\">{patientData.lastVisit}</span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Current Consultation Status */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] rounded-lg p-4\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6] mb-3\">Current Consultation</h4>\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <span className=\"text-sm font-medium text-[#333333]\">Active Session</span>\r\n            <p className=\"text-xs text-gray-500\">Started: {new Date().toLocaleTimeString()}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Consultations */}\r\n      <div>\r\n        <h4 className=\"text-md font-semibold text-[#0077B6] mb-3 flex items-center gap-2\">\r\n          <FaHistory />\r\n          Recent Consultations\r\n        </h4>\r\n        <div className=\"space-y-3\">\r\n          {consultationHistory.map((consultation) => (\r\n            <div key={consultation.id} className=\"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\">\r\n              <div className=\"flex justify-between items-start\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-[#333333]\">{consultation.type}</p>\r\n                  <p className=\"text-xs text-gray-500\">{consultation.dentist}</p>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <p className=\"text-xs text-gray-500\">{consultation.date}</p>\r\n                  <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${\r\n                    consultation.status === 'Completed' \r\n                      ? 'bg-green-100 text-green-800' \r\n                      : 'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {consultation.status}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <div>\r\n        <h4 className=\"text-md font-semibold text-[#0077B6] mb-3\">Quick Actions</h4>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300 text-sm\">\r\n            <FaFileAlt className=\"mr-2\" />\r\n            View Medical History\r\n          </button>\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\">\r\n            <FaPhone className=\"mr-2\" />\r\n            Contact Dentist\r\n          </button>\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\">\r\n            <FaCalendar className=\"mr-2\" />\r\n            Schedule Next Visit\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PatientInfo; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC3G,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAACY,OAAO,CAAC;EAEvD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBH,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBJ,YAAY,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBH,cAAc,CAACL,OAAO,CAAC;IACvBG,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CN,cAAc,CAACO,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,mBAAmB,GAAG,CAC1B;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,aAAa;IACtBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtB,OAAA;MAAKqB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDtB,OAAA;QAAIqB,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC1EtB,OAAA,CAACT,MAAM;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uBAEZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACJ,CAACtB,SAAS,gBACTJ,OAAA;QACEqB,SAAS,EAAC,4JAA4J;QACtKM,OAAO,EAAEnB,UAAW;QAAAc,QAAA,gBAEpBtB,OAAA,CAACR,MAAM;UAAC6B,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,QAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET1B,OAAA;QAAKqB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtB,OAAA;UACEqB,SAAS,EAAC,4KAA4K;UACtLM,OAAO,EAAElB,UAAW;UAAAa,QAAA,gBAEpBtB,OAAA,CAACP,MAAM;YAAC4B,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,QAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1B,OAAA;UACEqB,SAAS,EAAC,qHAAqH;UAC/HM,OAAO,EAAEjB,YAAa;UAAAY,QAAA,gBAEtBtB,OAAA,CAACN,OAAO;YAAC2B,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDtB,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAOqB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5EtB,SAAS,gBACRJ,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXL,KAAK,EAAEP,WAAW,CAACU,EAAG;UACtBY,QAAQ,EAAGC,CAAC,IAAKlB,iBAAiB,CAAC,IAAI,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;UACzDQ,SAAS,EAAC;QAA6G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC,gBAEF1B,OAAA;UAAMqB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAEhB,WAAW,CAACU;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAClF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAOqB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3EtB,SAAS,gBACRJ,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXL,KAAK,EAAEP,WAAW,CAACyB,IAAK;UACxBH,QAAQ,EAAGC,CAAC,IAAKlB,iBAAiB,CAAC,MAAM,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;UAC3DQ,SAAS,EAAC;QAA6G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC,gBAEF1B,OAAA;UAAMqB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAEhB,WAAW,CAACyB;QAAI;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACpF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAOqB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrEtB,SAAS,gBACRJ,OAAA;UACEkB,IAAI,EAAC,QAAQ;UACbL,KAAK,EAAEP,WAAW,CAAC0B,GAAI;UACvBJ,QAAQ,EAAGC,CAAC,IAAKlB,iBAAiB,CAAC,KAAK,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;UAC1DQ,SAAS,EAAC;QAA6G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC,gBAEF1B,OAAA;UAAMqB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GAAEhB,WAAW,CAAC0B,GAAG,EAAC,QAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtB,OAAA;UAAOqB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5EtB,SAAS,gBACRJ,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXL,KAAK,EAAEP,WAAW,CAAC2B,SAAU;UAC7BL,QAAQ,EAAGC,CAAC,IAAKlB,iBAAiB,CAAC,WAAW,EAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE;UAChEQ,SAAS,EAAC;QAA6G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxH,CAAC,gBAEF1B,OAAA;UAAMqB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAEhB,WAAW,CAAC2B;QAAS;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACzF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKqB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDtB,OAAA;QAAIqB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnF1B,OAAA;QAAKqB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCtB,OAAA;UAAKqB,SAAS,EAAC;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzD1B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAMqB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1E1B,OAAA;YAAGqB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,WAAS,EAAC,IAAIY,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAAsB,QAAA,gBACEtB,OAAA;QAAIqB,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAC/EtB,OAAA,CAACL,SAAS;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wBAEf;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL1B,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBP,mBAAmB,CAACqB,GAAG,CAAEC,YAAY,iBACpCrC,OAAA;UAA2BqB,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eAC5HtB,OAAA;YAAKqB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CtB,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAGqB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAEe,YAAY,CAACnB;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzE1B,OAAA;gBAAGqB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEe,YAAY,CAAClB;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACN1B,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBtB,OAAA;gBAAGqB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEe,YAAY,CAACpB;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D1B,OAAA;gBAAMqB,SAAS,EAAE,2DACfgB,YAAY,CAACjB,MAAM,KAAK,WAAW,GAC/B,6BAA6B,GAC7B,+BAA+B,EAClC;gBAAAE,QAAA,EACAe,YAAY,CAACjB;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAhBEW,YAAY,CAACrB,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBpB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAAsB,QAAA,gBACEtB,OAAA;QAAIqB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E1B,OAAA;QAAKqB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDtB,OAAA;UAAQqB,SAAS,EAAC,2LAA2L;UAAAC,QAAA,gBAC3MtB,OAAA,CAACF,SAAS;YAACuB,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1B,OAAA;UAAQqB,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAC3LtB,OAAA,CAACJ,OAAO;YAACyB,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1B,OAAA;UAAQqB,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAC3LtB,OAAA,CAACH,UAAU;YAACwB,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9MIF,WAAW;AAAAqC,EAAA,GAAXrC,WAAW;AAgNjB,eAAeA,WAAW;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}