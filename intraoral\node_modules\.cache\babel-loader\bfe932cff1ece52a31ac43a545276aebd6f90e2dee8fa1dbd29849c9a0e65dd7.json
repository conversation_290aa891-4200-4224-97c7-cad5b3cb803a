{"ast": null, "code": "import { isMotionValue } from '../utils/is-motion-value.mjs';\nfunction isWillChangeMotionValue(value) {\n  return Boolean(isMotionValue(value) && value.add);\n}\nexport { isWillChangeMotionValue };", "map": {"version": 3, "names": ["isMotionValue", "isWillChangeMotionValue", "value", "Boolean", "add"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/value/use-will-change/is.mjs"], "sourcesContent": ["import { isMotionValue } from '../utils/is-motion-value.mjs';\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean(isMotionValue(value) && value.add);\n}\n\nexport { isWillChangeMotionValue };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,8BAA8B;AAE5D,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EACpC,OAAOC,OAAO,CAACH,aAAa,CAACE,KAAK,CAAC,IAAIA,KAAK,CAACE,GAAG,CAAC;AACrD;AAEA,SAASH,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}