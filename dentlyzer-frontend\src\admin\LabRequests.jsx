import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFlask, FaUniversity, FaBuilding, FaCheck, FaTimes, FaClock, FaUser, FaCalendarAlt, FaEye } from 'react-icons/fa';
import AdminSidebar from './AdminSidebar';

const LabRequests = () => {
  const [labRequests, setLabRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchLabRequests();
  }, []);

  const fetchLabRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5000/api/lab-requests', {
        headers: { Authorization: `Bearer ${token}` }
      });
      setLabRequests(response.data);
    } catch (error) {
      console.error('Error fetching lab requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (request) => {
    setSelectedRequest(request);
    setShowModal(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'approved': return 'text-green-600 bg-green-100 border-green-200';
      case 'rejected': return 'text-red-600 bg-red-100 border-red-200';
      case 'completed': return 'text-[#0077B6] bg-[rgba(0,119,182,0.1)] border-[rgba(0,119,182,0.2)]';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <FaClock className="h-4 w-4" />;
      case 'approved': return <FaCheck className="h-4 w-4" />;
      case 'rejected': return <FaTimes className="h-4 w-4" />;
      case 'completed': return <FaCheck className="h-4 w-4" />;
      default: return <FaFlask className="h-4 w-4" />;
    }
  };

  const getLabTypeIcon = (labType) => {
    return labType === 'university' ? 
      <FaUniversity className="h-5 w-5 text-blue-600" /> : 
      <FaBuilding className="h-5 w-5 text-green-600" />;
  };

  const filteredRequests = labRequests.filter(request => {
    if (filter === 'all') return true;
    return request.status === filter;
  });

  const getStatusCounts = () => {
    return {
      all: labRequests.length,
      pending: labRequests.filter(r => r.status === 'pending').length,
      approved: labRequests.filter(r => r.status === 'approved').length,
      rejected: labRequests.filter(r => r.status === 'rejected').length,
      completed: labRequests.filter(r => r.status === 'completed').length,
    };
  };

  const statusCounts = getStatusCounts();

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex items-center justify-between p-4 bg-white shadow-sm">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#0077B6]"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <h1 className="text-xl font-semibold text-gray-900">Lab Requests</h1>
            <div></div>
          </div>
          <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                  <div className="space-y-4">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="h-24 bg-gray-200 rounded"></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex items-center justify-between p-4 bg-white shadow-sm">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#0077B6]"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 className="text-xl font-semibold text-gray-900">Lab Requests</h1>
          <div></div>
        </div>
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8"
            >
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center">
                  <FaFlask className="h-8 w-8 text-[#0077B6] mr-4" />
                  <h1 className="text-3xl font-bold text-[#333333]">Lab Requests Overview</h1>
                </div>
                <div className="text-sm text-gray-500">
                  Total Requests: {labRequests.length}
                </div>
              </div>

              {/* Filter Tabs */}
              <div className="flex flex-wrap gap-2 mb-6">
                {[
                  { key: 'all', label: 'All', count: statusCounts.all },
                  { key: 'pending', label: 'Pending', count: statusCounts.pending },
                  { key: 'approved', label: 'Approved', count: statusCounts.approved },
                  { key: 'completed', label: 'Completed', count: statusCounts.completed },
                  { key: 'rejected', label: 'Rejected', count: statusCounts.rejected },
                ].map(({ key, label, count }) => (
                  <button
                    key={key}
                    onClick={() => setFilter(key)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      filter === key
                        ? 'bg-[#0077B6] text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-[rgba(0,119,182,0.1)] hover:text-[#0077B6]'
                    }`}
                  >
                    {label} ({count})
                  </button>
                ))}
              </div>

              {/* Lab Requests List */}
              {filteredRequests.length === 0 ? (
                <div className="text-center py-12">
                  <FaFlask className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-500 mb-2">
                    {filter === 'all' ? 'No lab requests found' : `No ${filter} requests found`}
                  </h3>
                  <p className="text-gray-400">
                    Lab requests from students will appear here.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredRequests.map((request) => (
                    <motion.div
                      key={request._id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center">
                          {getLabTypeIcon(request.labType)}
                          <div className="ml-3">
                            <h3 className="text-lg font-semibold text-[#333333]">
                              {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}
                            </h3>
                            <p className="text-sm text-gray-600">Request ID: {request._id.slice(-8)}</p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(request.status)}`}>
                          <div className="flex items-center">
                            {getStatusIcon(request.status)}
                            <span className="ml-1">{request.status.charAt(0).toUpperCase() + request.status.slice(1)}</span>
                          </div>
                        </span>
                      </div>

                      <div className="grid md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <FaUser className="h-4 w-4 mr-2 text-[#0077B6]" />
                          <span className="font-medium">Student:</span>
                          <span className="ml-1">{request.studentName}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaUser className="h-4 w-4 mr-2 text-[#0077B6]" />
                          <span className="font-medium">Patient:</span>
                          <span className="ml-1">{request.patientName}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaCalendarAlt className="h-4 w-4 mr-2 text-[#0077B6]" />
                          <span className="font-medium">Submitted:</span>
                          <span className="ml-1">{new Date(request.submitDate).toLocaleDateString()}</span>
                        </div>
                      </div>

                      {request.notes && (
                        <div className="mb-4">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Notes:</span> {request.notes}
                          </p>
                        </div>
                      )}

                      {request.responseNotes && (
                        <div className="bg-[rgba(0,119,182,0.05)] p-3 rounded-lg border border-[rgba(0,119,182,0.1)] mb-4">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Response:</span> {request.responseNotes}
                          </p>
                          {request.responseDate && (
                            <p className="text-xs text-gray-500 mt-1">
                              Responded on: {new Date(request.responseDate).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      )}

                      {/* View Details Button */}
                      <div className="flex justify-end">
                        <button
                          onClick={() => handleViewDetails(request)}
                          className="px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors flex items-center"
                        >
                          <FaEye className="h-4 w-4 mr-2" />
                          View Details
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          </div>
        </main>
      </div>

      {/* Details Modal */}
      {showModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <FaFlask className="h-6 w-6 text-[#0077B6] mr-3" />
                <h2 className="text-2xl font-bold text-gray-800">Lab Request Details</h2>
              </div>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FaTimes className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Request ID</label>
                  <p className="text-gray-900">{selectedRequest._id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Lab Type</label>
                  <p className="text-gray-900">{selectedRequest.labType === 'university' ? 'University Lab' : 'Outside Lab'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Student Name</label>
                  <p className="text-gray-900">{selectedRequest.studentName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Student ID</label>
                  <p className="text-gray-900">{selectedRequest.studentId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Patient Name</label>
                  <p className="text-gray-900">{selectedRequest.patientName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Patient ID</label>
                  <p className="text-gray-900">{selectedRequest.patientId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Submit Date</label>
                  <p className="text-gray-900">{new Date(selectedRequest.submitDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedRequest.status)}`}>
                    {selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)}
                  </span>
                </div>
              </div>

              {selectedRequest.notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Student Notes</label>
                  <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{selectedRequest.notes}</p>
                </div>
              )}

              {selectedRequest.responseNotes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Response Notes</label>
                  <p className="text-gray-900 bg-[rgba(0,119,182,0.05)] p-3 rounded-lg border border-[rgba(0,119,182,0.1)]">
                    {selectedRequest.responseNotes}
                  </p>
                  {selectedRequest.responseDate && (
                    <p className="text-xs text-gray-500 mt-2">
                      Responded on: {new Date(selectedRequest.responseDate).toLocaleDateString()}
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="px-6 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default LabRequests;
