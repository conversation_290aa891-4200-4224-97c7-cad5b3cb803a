{"ast": null, "code": "var _jsxFileName = \"D:\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ToastContainer, toast } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [patientInfo, setPatientInfo] = useState({\n    name: '<PERSON>',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n    if (status) {\n      toast.success('Connected to dentist successfully!');\n    } else {\n      toast.error('Connection lost. Trying to reconnect...');\n    }\n  };\n  const handleDetectionResults = results => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      toast.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    setIsAnalyzing(true);\n    toast.info('Starting dental analysis...');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"app-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83E\\uDDB7 Intraoral Patient Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"connection-status\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `status-indicator ${isConnected ? 'status-online' : 'status-offline'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), isConnected ? 'Connected to Dentist' : 'Disconnected']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCF9 Live Video Consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n            onConnectionStatus: handleConnectionStatus,\n            onStartAnalysis: startAnalysis\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDC64 Patient Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n            patient: patientInfo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDD0D AI Dental Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n            onResults: handleDetectionResults,\n            isAnalyzing: isAnalyzing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDCCA Analysis Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n            results: detectionResults,\n            isAnalyzing: isAnalyzing\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"8o+W2SDiiCIdFIUYn4J7XnJQw8g=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ToastContainer", "toast", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "jsxDEV", "_jsxDEV", "App", "_s", "isConnected", "setIsConnected", "patientInfo", "setPatientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "handleConnectionStatus", "status", "success", "error", "handleDetectionResults", "results", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onConnectionStatus", "onStartAnalysis", "patient", "onResults", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { ToastContainer, toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport VideoCall from './components/VideoCall';\r\nimport YOLODetection from './components/YOLODetection';\r\nimport PatientInfo from './components/PatientInfo';\r\nimport AnalysisResults from './components/AnalysisResults';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [patientInfo, setPatientInfo] = useState({\r\n    name: '<PERSON>',\r\n    id: 'P001',\r\n    age: 35,\r\n    lastVisit: '2024-01-15'\r\n  });\r\n  const [detectionResults, setDetectionResults] = useState([]);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n\r\n  const handleConnectionStatus = (status) => {\r\n    setIsConnected(status);\r\n    if (status) {\r\n      toast.success('Connected to dentist successfully!');\r\n    } else {\r\n      toast.error('Connection lost. Trying to reconnect...');\r\n    }\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    setDetectionResults(results);\r\n    setIsAnalyzing(false);\r\n    \r\n    if (results.length > 0) {\r\n      const classNames = results.map(result => result.class);\r\n      toast.info(`Detected: ${classNames.join(', ')}`);\r\n    }\r\n  };\r\n\r\n  const startAnalysis = () => {\r\n    setIsAnalyzing(true);\r\n    toast.info('Starting dental analysis...');\r\n  };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      <div className=\"container\">\r\n        <header className=\"app-header\">\r\n          <h1>🦷 Intraoral Patient Dashboard</h1>\r\n          <div className=\"connection-status\">\r\n            <span className={`status-indicator ${isConnected ? 'status-online' : 'status-offline'}`}></span>\r\n            {isConnected ? 'Connected to Dentist' : 'Disconnected'}\r\n          </div>\r\n        </header>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div className=\"card\">\r\n            <h2>📹 Live Video Consultation</h2>\r\n            <VideoCall \r\n              onConnectionStatus={handleConnectionStatus}\r\n              onStartAnalysis={startAnalysis}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h2>👤 Patient Information</h2>\r\n            <PatientInfo patient={patientInfo} />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-2\">\r\n          <div className=\"card\">\r\n            <h2>🔍 AI Dental Analysis</h2>\r\n            <YOLODetection \r\n              onResults={handleDetectionResults}\r\n              isAnalyzing={isAnalyzing}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"card\">\r\n            <h2>📊 Analysis Results</h2>\r\n            <AnalysisResults \r\n              results={detectionResults}\r\n              isAnalyzing={isAnalyzing}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,EAAEC,KAAK,QAAQ,gBAAgB;AACtD,OAAO,uCAAuC;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC7CgB,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMwB,sBAAsB,GAAIC,MAAM,IAAK;IACzCZ,cAAc,CAACY,MAAM,CAAC;IACtB,IAAIA,MAAM,EAAE;MACVtB,KAAK,CAACuB,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,MAAM;MACLvB,KAAK,CAACwB,KAAK,CAAC,yCAAyC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;IAC1CR,mBAAmB,CAACQ,OAAO,CAAC;IAC5BN,cAAc,CAAC,KAAK,CAAC;IAErB,IAAIM,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGF,OAAO,CAACG,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtD/B,KAAK,CAACgC,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1Bd,cAAc,CAAC,IAAI,CAAC;IACpBpB,KAAK,CAACgC,IAAI,CAAC,6BAA6B,CAAC;EAC3C,CAAC;EAED,oBACE1B,OAAA;IAAK6B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB9B,OAAA;MAAK6B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB9B,OAAA;QAAQ6B,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAC5B9B,OAAA;UAAA8B,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvClC,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9B,OAAA;YAAM6B,SAAS,EAAE,oBAAoB1B,WAAW,GAAG,eAAe,GAAG,gBAAgB;UAAG;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC/F/B,WAAW,GAAG,sBAAsB,GAAG,cAAc;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETlC,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAA8B,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnClC,OAAA,CAACL,SAAS;YACRwC,kBAAkB,EAAEpB,sBAAuB;YAC3CqB,eAAe,EAAER;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAA8B,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BlC,OAAA,CAACH,WAAW;YAACwC,OAAO,EAAEhC;UAAY;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAA8B,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BlC,OAAA,CAACJ,aAAa;YACZ0C,SAAS,EAAEnB,sBAAuB;YAClCN,WAAW,EAAEA;UAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAA8B,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BlC,OAAA,CAACF,eAAe;YACdsB,OAAO,EAAET,gBAAiB;YAC1BE,WAAW,EAAEA;UAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlC,OAAA,CAACP,cAAc;MACb8C,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAChC,EAAA,CA9FQD,GAAG;AAAAgD,EAAA,GAAHhD,GAAG;AAgGZ,eAAeA,GAAG;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}