"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class TypeScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON><PERSON><PERSON>, ScopeType_1.ScopeType.type, upperScope, block, false);
    }
}
exports.TypeScope = TypeScope;
//# sourceMappingURL=TypeScope.js.map