"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.lib = void 0;
const decorators_1 = require("./decorators");
const decorators_legacy_1 = require("./decorators.legacy");
const dom_1 = require("./dom");
const dom_iterable_1 = require("./dom.iterable");
const es5_1 = require("./es5");
const es6_1 = require("./es6");
const es7_1 = require("./es7");
const es2015_1 = require("./es2015");
const es2015_collection_1 = require("./es2015.collection");
const es2015_core_1 = require("./es2015.core");
const es2015_generator_1 = require("./es2015.generator");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_promise_1 = require("./es2015.promise");
const es2015_proxy_1 = require("./es2015.proxy");
const es2015_reflect_1 = require("./es2015.reflect");
const es2015_symbol_1 = require("./es2015.symbol");
const es2015_symbol_wellknown_1 = require("./es2015.symbol.wellknown");
const es2016_1 = require("./es2016");
const es2016_array_include_1 = require("./es2016.array.include");
const es2016_full_1 = require("./es2016.full");
const es2017_1 = require("./es2017");
const es2017_full_1 = require("./es2017.full");
const es2017_intl_1 = require("./es2017.intl");
const es2017_object_1 = require("./es2017.object");
const es2017_sharedmemory_1 = require("./es2017.sharedmemory");
const es2017_string_1 = require("./es2017.string");
const es2017_typedarrays_1 = require("./es2017.typedarrays");
const es2018_1 = require("./es2018");
const es2018_asyncgenerator_1 = require("./es2018.asyncgenerator");
const es2018_asynciterable_1 = require("./es2018.asynciterable");
const es2018_full_1 = require("./es2018.full");
const es2018_intl_1 = require("./es2018.intl");
const es2018_promise_1 = require("./es2018.promise");
const es2018_regexp_1 = require("./es2018.regexp");
const es2019_1 = require("./es2019");
const es2019_array_1 = require("./es2019.array");
const es2019_full_1 = require("./es2019.full");
const es2019_intl_1 = require("./es2019.intl");
const es2019_object_1 = require("./es2019.object");
const es2019_string_1 = require("./es2019.string");
const es2019_symbol_1 = require("./es2019.symbol");
const es2020_1 = require("./es2020");
const es2020_bigint_1 = require("./es2020.bigint");
const es2020_date_1 = require("./es2020.date");
const es2020_full_1 = require("./es2020.full");
const es2020_intl_1 = require("./es2020.intl");
const es2020_number_1 = require("./es2020.number");
const es2020_promise_1 = require("./es2020.promise");
const es2020_sharedmemory_1 = require("./es2020.sharedmemory");
const es2020_string_1 = require("./es2020.string");
const es2020_symbol_wellknown_1 = require("./es2020.symbol.wellknown");
const es2021_1 = require("./es2021");
const es2021_full_1 = require("./es2021.full");
const es2021_intl_1 = require("./es2021.intl");
const es2021_promise_1 = require("./es2021.promise");
const es2021_string_1 = require("./es2021.string");
const es2021_weakref_1 = require("./es2021.weakref");
const es2022_1 = require("./es2022");
const es2022_array_1 = require("./es2022.array");
const es2022_error_1 = require("./es2022.error");
const es2022_full_1 = require("./es2022.full");
const es2022_intl_1 = require("./es2022.intl");
const es2022_object_1 = require("./es2022.object");
const es2022_regexp_1 = require("./es2022.regexp");
const es2022_sharedmemory_1 = require("./es2022.sharedmemory");
const es2022_string_1 = require("./es2022.string");
const es2023_1 = require("./es2023");
const es2023_array_1 = require("./es2023.array");
const es2023_full_1 = require("./es2023.full");
const esnext_1 = require("./esnext");
const esnext_array_1 = require("./esnext.array");
const esnext_asynciterable_1 = require("./esnext.asynciterable");
const esnext_bigint_1 = require("./esnext.bigint");
const esnext_full_1 = require("./esnext.full");
const esnext_intl_1 = require("./esnext.intl");
const esnext_promise_1 = require("./esnext.promise");
const esnext_string_1 = require("./esnext.string");
const esnext_symbol_1 = require("./esnext.symbol");
const esnext_weakref_1 = require("./esnext.weakref");
const lib_1 = require("./lib");
const scripthost_1 = require("./scripthost");
const webworker_1 = require("./webworker");
const webworker_importscripts_1 = require("./webworker.importscripts");
const webworker_iterable_1 = require("./webworker.iterable");
const lib = {
    es5: es5_1.es5,
    es6: es6_1.es6,
    es2015: es2015_1.es2015,
    es7: es7_1.es7,
    es2016: es2016_1.es2016,
    es2017: es2017_1.es2017,
    es2018: es2018_1.es2018,
    es2019: es2019_1.es2019,
    es2020: es2020_1.es2020,
    es2021: es2021_1.es2021,
    es2022: es2022_1.es2022,
    es2023: es2023_1.es2023,
    esnext: esnext_1.esnext,
    dom: dom_1.dom,
    'dom.iterable': dom_iterable_1.dom_iterable,
    webworker: webworker_1.webworker,
    'webworker.importscripts': webworker_importscripts_1.webworker_importscripts,
    'webworker.iterable': webworker_iterable_1.webworker_iterable,
    scripthost: scripthost_1.scripthost,
    'es2015.core': es2015_core_1.es2015_core,
    'es2015.collection': es2015_collection_1.es2015_collection,
    'es2015.generator': es2015_generator_1.es2015_generator,
    'es2015.iterable': es2015_iterable_1.es2015_iterable,
    'es2015.promise': es2015_promise_1.es2015_promise,
    'es2015.proxy': es2015_proxy_1.es2015_proxy,
    'es2015.reflect': es2015_reflect_1.es2015_reflect,
    'es2015.symbol': es2015_symbol_1.es2015_symbol,
    'es2015.symbol.wellknown': es2015_symbol_wellknown_1.es2015_symbol_wellknown,
    'es2016.array.include': es2016_array_include_1.es2016_array_include,
    'es2017.object': es2017_object_1.es2017_object,
    'es2017.sharedmemory': es2017_sharedmemory_1.es2017_sharedmemory,
    'es2017.string': es2017_string_1.es2017_string,
    'es2017.intl': es2017_intl_1.es2017_intl,
    'es2017.typedarrays': es2017_typedarrays_1.es2017_typedarrays,
    'es2018.asyncgenerator': es2018_asyncgenerator_1.es2018_asyncgenerator,
    'es2018.asynciterable': es2018_asynciterable_1.es2018_asynciterable,
    'es2018.intl': es2018_intl_1.es2018_intl,
    'es2018.promise': es2018_promise_1.es2018_promise,
    'es2018.regexp': es2018_regexp_1.es2018_regexp,
    'es2019.array': es2019_array_1.es2019_array,
    'es2019.object': es2019_object_1.es2019_object,
    'es2019.string': es2019_string_1.es2019_string,
    'es2019.symbol': es2019_symbol_1.es2019_symbol,
    'es2019.intl': es2019_intl_1.es2019_intl,
    'es2020.bigint': es2020_bigint_1.es2020_bigint,
    'es2020.date': es2020_date_1.es2020_date,
    'es2020.promise': es2020_promise_1.es2020_promise,
    'es2020.sharedmemory': es2020_sharedmemory_1.es2020_sharedmemory,
    'es2020.string': es2020_string_1.es2020_string,
    'es2020.symbol.wellknown': es2020_symbol_wellknown_1.es2020_symbol_wellknown,
    'es2020.intl': es2020_intl_1.es2020_intl,
    'es2020.number': es2020_number_1.es2020_number,
    'es2021.promise': es2021_promise_1.es2021_promise,
    'es2021.string': es2021_string_1.es2021_string,
    'es2021.weakref': es2021_weakref_1.es2021_weakref,
    'es2021.intl': es2021_intl_1.es2021_intl,
    'es2022.array': es2022_array_1.es2022_array,
    'es2022.error': es2022_error_1.es2022_error,
    'es2022.intl': es2022_intl_1.es2022_intl,
    'es2022.object': es2022_object_1.es2022_object,
    'es2022.sharedmemory': es2022_sharedmemory_1.es2022_sharedmemory,
    'es2022.string': es2022_string_1.es2022_string,
    'es2022.regexp': es2022_regexp_1.es2022_regexp,
    'es2023.array': es2023_array_1.es2023_array,
    'esnext.array': esnext_array_1.esnext_array,
    'esnext.symbol': esnext_symbol_1.esnext_symbol,
    'esnext.asynciterable': esnext_asynciterable_1.esnext_asynciterable,
    'esnext.intl': esnext_intl_1.esnext_intl,
    'esnext.bigint': esnext_bigint_1.esnext_bigint,
    'esnext.string': esnext_string_1.esnext_string,
    'esnext.promise': esnext_promise_1.esnext_promise,
    'esnext.weakref': esnext_weakref_1.esnext_weakref,
    decorators: decorators_1.decorators,
    'decorators.legacy': decorators_legacy_1.decorators_legacy,
    'es2016.full': es2016_full_1.es2016_full,
    'es2017.full': es2017_full_1.es2017_full,
    'es2018.full': es2018_full_1.es2018_full,
    'es2019.full': es2019_full_1.es2019_full,
    'es2020.full': es2020_full_1.es2020_full,
    'es2021.full': es2021_full_1.es2021_full,
    'es2022.full': es2022_full_1.es2022_full,
    'es2023.full': es2023_full_1.es2023_full,
    'esnext.full': esnext_full_1.esnext_full,
    lib: lib_1.lib,
};
exports.lib = lib;
//# sourceMappingURL=index.js.map