{"ast": null, "code": "import { progress } from '../../../utils/progress.mjs';\nimport { velocityPerSecond } from '../../../utils/velocity-per-second.mjs';\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n  current: 0,\n  offset: [],\n  progress: 0,\n  scrollLength: 0,\n  targetOffset: 0,\n  targetLength: 0,\n  containerLength: 0,\n  velocity: 0\n});\nconst createScrollInfo = () => ({\n  time: 0,\n  x: createAxisInfo(),\n  y: createAxisInfo()\n});\nconst keys = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n  const axis = info[axisName];\n  const {\n    length,\n    position\n  } = keys[axisName];\n  const prev = axis.current;\n  const prevTime = info.time;\n  axis.current = element[\"scroll\" + position];\n  axis.scrollLength = element[\"scroll\" + length] - element[\"client\" + length];\n  axis.offset.length = 0;\n  axis.offset[0] = 0;\n  axis.offset[1] = axis.scrollLength;\n  axis.progress = progress(0, axis.scrollLength, axis.current);\n  const elapsed = time - prevTime;\n  axis.velocity = elapsed > maxElapsed ? 0 : velocityPerSecond(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n  updateAxisInfo(element, \"x\", info, time);\n  updateAxisInfo(element, \"y\", info, time);\n  info.time = time;\n}\nexport { createScrollInfo, updateScrollInfo };", "map": {"version": 3, "names": ["progress", "velocityPerSecond", "maxElapsed", "createAxisInfo", "current", "offset", "<PERSON><PERSON><PERSON><PERSON>", "targetOffset", "targetLength", "containerLength", "velocity", "createScrollInfo", "time", "x", "y", "keys", "length", "position", "updateAxisInfo", "element", "axisName", "info", "axis", "prev", "prevTime", "elapsed", "updateScrollInfo"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs"], "sourcesContent": ["import { progress } from '../../../utils/progress.mjs';\nimport { velocityPerSecond } from '../../../utils/velocity-per-second.mjs';\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[\"scroll\" + position];\n    axis.scrollLength = element[\"scroll\" + length] - element[\"client\" + length];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = progress(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : velocityPerSecond(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\nexport { createScrollInfo, updateScrollInfo };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,iBAAiB,QAAQ,wCAAwC;;AAE1E;AACA;AACA;AACA,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,cAAc,GAAGA,CAAA,MAAO;EAC1BC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,EAAE;EACVL,QAAQ,EAAE,CAAC;EACXM,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAClBC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAGA,CAAA,MAAO;EAC5BC,IAAI,EAAE,CAAC;EACPC,CAAC,EAAEV,cAAc,CAAC,CAAC;EACnBW,CAAC,EAAEX,cAAc,CAAC;AACtB,CAAC,CAAC;AACF,MAAMY,IAAI,GAAG;EACTF,CAAC,EAAE;IACCG,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACd,CAAC;EACDH,CAAC,EAAE;IACCE,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACd;AACJ,CAAC;AACD,SAASC,cAAcA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAET,IAAI,EAAE;EACnD,MAAMU,IAAI,GAAGD,IAAI,CAACD,QAAQ,CAAC;EAC3B,MAAM;IAAEJ,MAAM;IAAEC;EAAS,CAAC,GAAGF,IAAI,CAACK,QAAQ,CAAC;EAC3C,MAAMG,IAAI,GAAGD,IAAI,CAAClB,OAAO;EACzB,MAAMoB,QAAQ,GAAGH,IAAI,CAACT,IAAI;EAC1BU,IAAI,CAAClB,OAAO,GAAGe,OAAO,CAAC,QAAQ,GAAGF,QAAQ,CAAC;EAC3CK,IAAI,CAAChB,YAAY,GAAGa,OAAO,CAAC,QAAQ,GAAGH,MAAM,CAAC,GAAGG,OAAO,CAAC,QAAQ,GAAGH,MAAM,CAAC;EAC3EM,IAAI,CAACjB,MAAM,CAACW,MAAM,GAAG,CAAC;EACtBM,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;EAClBiB,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,GAAGiB,IAAI,CAAChB,YAAY;EAClCgB,IAAI,CAACtB,QAAQ,GAAGA,QAAQ,CAAC,CAAC,EAAEsB,IAAI,CAAChB,YAAY,EAAEgB,IAAI,CAAClB,OAAO,CAAC;EAC5D,MAAMqB,OAAO,GAAGb,IAAI,GAAGY,QAAQ;EAC/BF,IAAI,CAACZ,QAAQ,GACTe,OAAO,GAAGvB,UAAU,GACd,CAAC,GACDD,iBAAiB,CAACqB,IAAI,CAAClB,OAAO,GAAGmB,IAAI,EAAEE,OAAO,CAAC;AAC7D;AACA,SAASC,gBAAgBA,CAACP,OAAO,EAAEE,IAAI,EAAET,IAAI,EAAE;EAC3CM,cAAc,CAACC,OAAO,EAAE,GAAG,EAAEE,IAAI,EAAET,IAAI,CAAC;EACxCM,cAAc,CAACC,OAAO,EAAE,GAAG,EAAEE,IAAI,EAAET,IAAI,CAAC;EACxCS,IAAI,CAACT,IAAI,GAAGA,IAAI;AACpB;AAEA,SAASD,gBAAgB,EAAEe,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}