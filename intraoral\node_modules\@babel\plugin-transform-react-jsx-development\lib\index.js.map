{"version": 3, "names": ["_development", "require", "_default", "exports", "default", "plugin"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\n\nimport plugin from \"@babel/plugin-transform-react-jsx/lib/development\";\n// We need to explicitly annotate this export because\n// @babel/plugin-transform-react-jsx/lib/development has no type definitions\n// (it's not a public entry point)\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\nexport default plugin as typeof import(\"@babel/plugin-transform-react-jsx\").default;\n"], "mappings": ";;;;;;AAEA,IAAAA,YAAA,GAAAC,OAAA;AAAuE,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKxDC,oBAAM", "ignoreList": []}