{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "assertVersion", "noNewArrows", "assumption", "spec", "name", "visitor", "ArrowFunctionExpression", "path", "isArrowFunctionExpression", "arrowFunctionToExpression", "allowInsertArrow", "specCompliant"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  spec?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const noNewArrows = api.assumption(\"noNewArrows\") ?? !options.spec;\n\n  return {\n    name: \"transform-arrow-functions\",\n\n    visitor: {\n      ArrowFunctionExpression(path) {\n        // In some conversion cases, it may have already been converted to a function while this callback\n        // was queued up.\n        if (!path.isArrowFunctionExpression()) return;\n\n        if (process.env.BABEL_8_BREAKING) {\n          path.arrowFunctionToExpression({\n            // While other utils may be fine inserting other arrows to make more transforms possible,\n            // the arrow transform itself absolutely cannot insert new arrow functions.\n            allowInsertArrow: false,\n            noNewArrows,\n          });\n        } else {\n          path.arrowFunctionToExpression({\n            allowInsertArrow: false,\n            noNewArrows,\n\n            // This is only needed for backward compat with @babel/traverse <7.13.0\n            // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n            specCompliant: !noNewArrows,\n          });\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA;EAChDF,GAAG,CAACG,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,WAAW,IAAAF,eAAA,GAAGF,GAAG,CAACK,UAAU,CAAC,aAAa,CAAC,YAAAH,eAAA,GAAI,CAACD,OAAO,CAACK,IAAI;EAElE,OAAO;IACLC,IAAI,EAAE,2BAA2B;IAEjCC,OAAO,EAAE;MACPC,uBAAuBA,CAACC,IAAI,EAAE;QAG5B,IAAI,CAACA,IAAI,CAACC,yBAAyB,CAAC,CAAC,EAAE;QAShC;UACLD,IAAI,CAACE,yBAAyB,CAAC;YAC7BC,gBAAgB,EAAE,KAAK;YACvBT,WAAW;YAIXU,aAAa,EAAE,CAACV;UAClB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}