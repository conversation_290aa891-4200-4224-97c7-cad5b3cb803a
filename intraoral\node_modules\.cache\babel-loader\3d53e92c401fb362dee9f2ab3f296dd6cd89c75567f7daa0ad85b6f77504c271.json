{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\VideoCall.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { toast } from 'react-toastify';\nimport { motion } from 'framer-motion';\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaEye, FaTimes, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';\nimport './VideoCall.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  onConnectionStatus,\n  onStartAnalysis,\n  onImageCaptured\n}) => {\n  _s();\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const [mouthDetected, setMouthDetected] = useState(false);\n  const [imageQuality, setImageQuality] = useState('unknown'); // unknown, good, poor\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    facingMode: \"user\"\n  };\n\n  // Simulate image quality and mouth detection check\n  const checkImageQuality = useCallback(imageSrc => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        // Simulate quality check - in real implementation this would analyze the image\n        const quality = Math.random() > 0.2 ? 'good' : 'poor';\n        const hasMouth = Math.random() > 0.3; // 70% chance of detecting mouth\n        resolve({\n          quality,\n          hasMouth\n        });\n      }, 500);\n    });\n  }, []);\n  const sendImageForAnalysis = useCallback(async imageSrc => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n      const results = await response.json();\n      console.log('YOLOv8 Results:', results);\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      toast.error('Failed to analyze image');\n    }\n  }, [onStartAnalysis]);\n  const captureImage = useCallback(async () => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n      if (imageSrc) {\n        // Check image quality and mouth detection\n        const {\n          quality,\n          hasMouth\n        } = await checkImageQuality(imageSrc);\n        setImageQuality(quality);\n        setMouthDetected(hasMouth);\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false,\n          quality: quality,\n          mouthDetected: hasMouth\n        };\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Only send for analysis if mouth is detected and quality is good\n        if (hasMouth && quality === 'good') {\n          sendImageForAnalysis(imageSrc);\n          toast.success('Image captured and sent for analysis');\n        } else if (!hasMouth) {\n          toast.warning('No mouth detected in image. Please adjust camera position.');\n        } else if (quality === 'poor') {\n          toast.warning('Image quality is poor. Please ensure good lighting and focus.');\n        }\n      }\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis, checkImageQuality]);\n  const startAutoCapture = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  }, [isStreaming, captureInterval, captureImage]);\n  const startStream = useCallback(() => {\n    setIsStreaming(true);\n    onConnectionStatus(true);\n    setMouthDetected(false);\n    setImageQuality('unknown');\n    toast.success('Video stream started');\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus, startAutoCapture]);\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    setMouthDetected(false);\n    setImageQuality('unknown');\n    stopAutoCapture();\n    toast.info('Video stream stopped');\n  }, [onConnectionStatus]);\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\n  const getQualityIcon = () => {\n    switch (imageQuality) {\n      case 'good':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 16\n        }, this);\n      case 'poor':\n        return /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"h-4 w-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaEye, {\n          className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getQualityText = () => {\n    switch (imageQuality) {\n      case 'good':\n        return 'Good Quality';\n      case 'poor':\n        return 'Poor Quality';\n      default:\n        return 'Unknown';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-3 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: `flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${isStreaming ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg' : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'}`,\n          onClick: isStreaming ? stopStream : startStream,\n          children: [isStreaming ? /*#__PURE__*/_jsxDEV(FaStop, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(FaPlay, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 66\n          }, this), isStreaming ? 'Stop Stream' : 'Start Stream']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n          onClick: captureImage,\n          disabled: !isStreaming || isCapturing,\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), isCapturing ? 'Capturing...' : 'Capture Image']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoCapture,\n            onChange: toggleAutoCapture,\n            disabled: !isStreaming,\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), \"Auto Capture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), autoCapture && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: captureInterval,\n          onChange: e => setCaptureInterval(Number(e.target.value)),\n          disabled: !isStreaming,\n          className: \"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 3000,\n            children: \"Every 3s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5000,\n            children: \"Every 5s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10000,\n            children: \"Every 10s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), isStreaming && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-3 items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: [mouthDetected ? /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-4 w-4 text-green-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {\n          className: \"h-4 w-4 text-red-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: mouthDetected ? 'Mouth Detected' : 'No Mouth'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200\",\n        children: [getQualityIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium text-gray-700 ml-2\",\n          children: getQualityText()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\",\n      children: [isStreaming ? /*#__PURE__*/_jsxDEV(Webcam, {\n        ref: webcamRef,\n        audio: false,\n        screenshotFormat: \"image/jpeg\",\n        videoConstraints: videoConstraints,\n        className: \"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n            className: \"h-16 w-16 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-medium\",\n            children: \"Click \\\"Start Stream\\\" to begin video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: \"Live dental examination and analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-5 w-5 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), \"Capturing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this), isStreaming && imageQuality !== 'unknown' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-4 right-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center px-3 py-2 bg-black bg-opacity-75 rounded-lg text-white\",\n          children: [getQualityIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-sm font-medium\",\n            children: getQualityText()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: [\"Recent Captures (\", capturedImages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), capturedImages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n        children: capturedImages.map(image => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: \"Captured\",\n            className: \"w-full h-24 object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-1\",\n              children: new Date(image.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.mouthDetected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                children: image.mouthDetected ? '✓ Mouth' : '✗ No Mouth'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.quality === 'good' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                children: image.quality === 'good' ? '✓ Good' : '⚠ Poor'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.analyzed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n              children: image.analyzed ? '✓ Analyzed' : '⏳ Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No captures yet. Start streaming and capture images for analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"ICR91ZqSWSjme36In0pG0D+Rl+0=\");\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Webcam", "toast", "motion", "FaPlay", "FaStop", "FaCamera", "FaImage", "FaVideo", "FaEye", "FaTimes", "FaCheckCircle", "FaExclamationTriangle", "jsxDEV", "_jsxDEV", "VideoCall", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "_s", "isStreaming", "setIsStreaming", "isCapturing", "setIsCapturing", "capturedImages", "setCapturedImages", "autoCapture", "setAutoCapture", "captureInterval", "setCaptureInterval", "mouthDetected", "setMouthDetected", "imageQuality", "setImageQuality", "webcamRef", "intervalRef", "videoConstraints", "width", "height", "facingMode", "checkImageQuality", "imageSrc", "Promise", "resolve", "setTimeout", "quality", "Math", "random", "hasMouth", "sendImageForAnalysis", "base64Data", "replace", "blob", "fetch", "then", "res", "formData", "FormData", "append", "response", "method", "body", "ok", "Error", "results", "json", "console", "log", "error", "captureImage", "current", "getScreenshot", "newImage", "id", "Date", "now", "src", "timestamp", "toISOString", "analyzed", "prev", "slice", "success", "warning", "startAutoCapture", "clearInterval", "setInterval", "startStream", "stopStream", "stopAutoCapture", "info", "toggleAutoCapture", "getQualityIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getQualityText", "children", "button", "whileHover", "scale", "whileTap", "onClick", "disabled", "type", "checked", "onChange", "value", "e", "Number", "target", "ref", "audio", "screenshotFormat", "length", "map", "image", "div", "alt", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/VideoCall.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport Webcam from 'react-webcam';\r\nimport { toast } from 'react-toastify';\r\nimport { motion } from 'framer-motion';\r\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaEye, FaTimes, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';\r\nimport './VideoCall.css';\r\n\r\nconst VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured }) => {\r\n  const [isStreaming, setIsStreaming] = useState(false);\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const [capturedImages, setCapturedImages] = useState([]);\r\n  const [autoCapture, setAutoCapture] = useState(true);\r\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\r\n  const [mouthDetected, setMouthDetected] = useState(false);\r\n  const [imageQuality, setImageQuality] = useState('unknown'); // unknown, good, poor\r\n  const webcamRef = useRef(null);\r\n  const intervalRef = useRef(null);\r\n\r\n  const videoConstraints = {\r\n    width: 640,\r\n    height: 480,\r\n    facingMode: \"user\"\r\n  };\r\n\r\n  // Simulate image quality and mouth detection check\r\n  const checkImageQuality = useCallback((imageSrc) => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        // Simulate quality check - in real implementation this would analyze the image\r\n        const quality = Math.random() > 0.2 ? 'good' : 'poor';\r\n        const hasMouth = Math.random() > 0.3; // 70% chance of detecting mouth\r\n        resolve({ quality, hasMouth });\r\n      }, 500);\r\n    });\r\n  }, []);\r\n\r\n  const sendImageForAnalysis = useCallback(async (imageSrc) => {\r\n    try {\r\n      onStartAnalysis();\r\n\r\n      // Convert base64 to blob\r\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\r\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('image', blob, 'capture.jpg');\r\n\r\n      // Send to backend for YOLOv8 analysis\r\n      const response = await fetch('/api/analyze', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Analysis failed');\r\n      }\r\n\r\n      const results = await response.json();\r\n      console.log('YOLOv8 Results:', results);\r\n\r\n    } catch (error) {\r\n      console.error('Error sending image for analysis:', error);\r\n      toast.error('Failed to analyze image');\r\n    }\r\n  }, [onStartAnalysis]);\r\n\r\n  const captureImage = useCallback(async () => {\r\n    if (webcamRef.current && isStreaming) {\r\n      setIsCapturing(true);\r\n      const imageSrc = webcamRef.current.getScreenshot();\r\n\r\n      if (imageSrc) {\r\n        // Check image quality and mouth detection\r\n        const { quality, hasMouth } = await checkImageQuality(imageSrc);\r\n        setImageQuality(quality);\r\n        setMouthDetected(hasMouth);\r\n\r\n        const newImage = {\r\n          id: Date.now(),\r\n          src: imageSrc,\r\n          timestamp: new Date().toISOString(),\r\n          analyzed: false,\r\n          quality: quality,\r\n          mouthDetected: hasMouth\r\n        };\r\n\r\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\r\n\r\n        // Pass the captured image to parent component for YOLOv8 analysis\r\n        if (onImageCaptured) {\r\n          onImageCaptured(imageSrc);\r\n        }\r\n\r\n        // Only send for analysis if mouth is detected and quality is good\r\n        if (hasMouth && quality === 'good') {\r\n          sendImageForAnalysis(imageSrc);\r\n          toast.success('Image captured and sent for analysis');\r\n        } else if (!hasMouth) {\r\n          toast.warning('No mouth detected in image. Please adjust camera position.');\r\n        } else if (quality === 'poor') {\r\n          toast.warning('Image quality is poor. Please ensure good lighting and focus.');\r\n        }\r\n      }\r\n\r\n      setIsCapturing(false);\r\n    }\r\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis, checkImageQuality]);\r\n\r\n  const startAutoCapture = useCallback(() => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n    }\r\n\r\n    intervalRef.current = setInterval(() => {\r\n      if (isStreaming && webcamRef.current) {\r\n        captureImage();\r\n      }\r\n    }, captureInterval);\r\n  }, [isStreaming, captureInterval, captureImage]);\r\n\r\n  const startStream = useCallback(() => {\r\n    setIsStreaming(true);\r\n    onConnectionStatus(true);\r\n    setMouthDetected(false);\r\n    setImageQuality('unknown');\r\n    toast.success('Video stream started');\r\n\r\n    // Start automatic capture if enabled\r\n    if (autoCapture) {\r\n      startAutoCapture();\r\n    }\r\n  }, [autoCapture, onConnectionStatus, startAutoCapture]);\r\n\r\n  const stopStream = useCallback(() => {\r\n    setIsStreaming(false);\r\n    onConnectionStatus(false);\r\n    setMouthDetected(false);\r\n    setImageQuality('unknown');\r\n    stopAutoCapture();\r\n    toast.info('Video stream stopped');\r\n  }, [onConnectionStatus]);\r\n\r\n  const stopAutoCapture = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n      intervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  const toggleAutoCapture = () => {\r\n    setAutoCapture(!autoCapture);\r\n    if (!autoCapture) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      stopAutoCapture();\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (autoCapture && isStreaming) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\r\n\r\n  const getQualityIcon = () => {\r\n    switch (imageQuality) {\r\n      case 'good':\r\n        return <FaCheckCircle className=\"h-4 w-4 text-green-500\" />;\r\n      case 'poor':\r\n        return <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />;\r\n      default:\r\n        return <FaEye className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getQualityText = () => {\r\n    switch (imageQuality) {\r\n      case 'good':\r\n        return 'Good Quality';\r\n      case 'poor':\r\n        return 'Poor Quality';\r\n      default:\r\n        return 'Unknown';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Video Controls */}\r\n      <div className=\"flex flex-wrap gap-3 items-center justify-between\">\r\n        <div className=\"flex gap-3\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${\r\n              isStreaming\r\n                ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg'\r\n                : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'\r\n            }`}\r\n            onClick={isStreaming ? stopStream : startStream}\r\n          >\r\n            {isStreaming ? <FaStop className=\"mr-2 h-4 w-4\" /> : <FaPlay className=\"mr-2 h-4 w-4\" />}\r\n            {isStreaming ? 'Stop Stream' : 'Start Stream'}\r\n          </motion.button>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            onClick={captureImage}\r\n            disabled={!isStreaming || isCapturing}\r\n          >\r\n            <FaCamera className=\"mr-2 h-4 w-4\" />\r\n            {isCapturing ? 'Capturing...' : 'Capture Image'}\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <label className=\"flex items-center text-sm font-medium text-gray-700\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={autoCapture}\r\n              onChange={toggleAutoCapture}\r\n              disabled={!isStreaming}\r\n              className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\r\n            />\r\n            Auto Capture\r\n          </label>\r\n\r\n          {autoCapture && (\r\n            <select\r\n              value={captureInterval}\r\n              onChange={(e) => setCaptureInterval(Number(e.target.value))}\r\n              disabled={!isStreaming}\r\n              className=\"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\r\n            >\r\n              <option value={3000}>Every 3s</option>\r\n              <option value={5000}>Every 5s</option>\r\n              <option value={10000}>Every 10s</option>\r\n            </select>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Status Indicators */}\r\n      {isStreaming && (\r\n        <div className=\"flex flex-wrap gap-3 items-center justify-center\">\r\n          <div className=\"flex items-center px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200\">\r\n            {mouthDetected ? (\r\n              <FaCheckCircle className=\"h-4 w-4 text-green-500 mr-2\" />\r\n            ) : (\r\n              <FaTimes className=\"h-4 w-4 text-red-500 mr-2\" />\r\n            )}\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              {mouthDetected ? 'Mouth Detected' : 'No Mouth'}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"flex items-center px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200\">\r\n            {getQualityIcon()}\r\n            <span className=\"text-sm font-medium text-gray-700 ml-2\">\r\n              {getQualityText()}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Video Container */}\r\n      <div className=\"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\">\r\n        {isStreaming ? (\r\n          <Webcam\r\n            ref={webcamRef}\r\n            audio={false}\r\n            screenshotFormat=\"image/jpeg\"\r\n            videoConstraints={videoConstraints}\r\n            className=\"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\">\r\n            <div className=\"text-center text-white\">\r\n              <FaVideo className=\"h-16 w-16 mx-auto mb-4 text-gray-400\" />\r\n              <p className=\"text-lg font-medium\">Click \"Start Stream\" to begin video consultation</p>\r\n              <p className=\"text-sm text-gray-400 mt-2\">Live dental examination and analysis</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {isCapturing && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n            <div className=\"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\">\r\n              <FaCamera className=\"mr-2 h-5 w-5 animate-pulse\" />\r\n              Capturing...\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Quality overlay */}\r\n        {isStreaming && imageQuality !== 'unknown' && (\r\n          <div className=\"absolute top-4 right-4\">\r\n            <div className=\"flex items-center px-3 py-2 bg-black bg-opacity-75 rounded-lg text-white\">\r\n              {getQualityIcon()}\r\n              <span className=\"ml-2 text-sm font-medium\">{getQualityText()}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recent Captures */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaImage className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recent Captures ({capturedImages.length})</h3>\r\n        </div>\r\n\r\n        {capturedImages.length > 0 ? (\r\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\r\n            {capturedImages.map((image) => (\r\n              <motion.div\r\n                key={image.id}\r\n                whileHover={{ scale: 1.05 }}\r\n                className=\"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\"\r\n              >\r\n                <img\r\n                  src={image.src}\r\n                  alt=\"Captured\"\r\n                  className=\"w-full h-24 object-cover\"\r\n                />\r\n                <div className=\"p-2\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">\r\n                    {new Date(image.timestamp).toLocaleTimeString()}\r\n                  </p>\r\n                  <div className=\"flex items-center justify-between mb-1\">\r\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                      image.mouthDetected\r\n                        ? 'bg-green-100 text-green-800'\r\n                        : 'bg-red-100 text-red-800'\r\n                    }`}>\r\n                      {image.mouthDetected ? '✓ Mouth' : '✗ No Mouth'}\r\n                    </span>\r\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                      image.quality === 'good'\r\n                        ? 'bg-blue-100 text-blue-800'\r\n                        : 'bg-yellow-100 text-yellow-800'\r\n                    }`}>\r\n                      {image.quality === 'good' ? '✓ Good' : '⚠ Poor'}\r\n                    </span>\r\n                  </div>\r\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                    image.analyzed\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : 'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}\r\n                  </span>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <FaImage className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\r\n            <p>No captures yet. Start streaming and capture images for analysis.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoCall; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,gBAAgB;AACjI,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAMqC,SAAS,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMqC,WAAW,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMsC,gBAAgB,GAAG;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGxC,WAAW,CAAEyC,QAAQ,IAAK;IAClD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAM;QACf;QACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;QACrD,MAAMC,QAAQ,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACtCJ,OAAO,CAAC;UAAEE,OAAO;UAAEG;QAAS,CAAC,CAAC;MAChC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAGjD,WAAW,CAAC,MAAOyC,QAAQ,IAAK;IAC3D,IAAI;MACFxB,eAAe,CAAC,CAAC;;MAEjB;MACA,MAAMiC,UAAU,GAAGT,QAAQ,CAACU,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;MACpE,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAAC,0BAA0BH,UAAU,EAAE,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC;;MAExF;MACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,IAAI,EAAE,aAAa,CAAC;;MAE7C;MACA,MAAMO,QAAQ,GAAG,MAAMN,KAAK,CAAC,cAAc,EAAE;QAC3CO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEL;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAMC,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACrCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,OAAO,CAAC;IAEzC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDlE,KAAK,CAACkE,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC,EAAE,CAACnD,eAAe,CAAC,CAAC;EAErB,MAAMoD,YAAY,GAAGrE,WAAW,CAAC,YAAY;IAC3C,IAAIkC,SAAS,CAACoC,OAAO,IAAIlD,WAAW,EAAE;MACpCG,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMkB,QAAQ,GAAGP,SAAS,CAACoC,OAAO,CAACC,aAAa,CAAC,CAAC;MAElD,IAAI9B,QAAQ,EAAE;QACZ;QACA,MAAM;UAAEI,OAAO;UAAEG;QAAS,CAAC,GAAG,MAAMR,iBAAiB,CAACC,QAAQ,CAAC;QAC/DR,eAAe,CAACY,OAAO,CAAC;QACxBd,gBAAgB,CAACiB,QAAQ,CAAC;QAE1B,MAAMwB,QAAQ,GAAG;UACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,GAAG,EAAEnC,QAAQ;UACboC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCC,QAAQ,EAAE,KAAK;UACflC,OAAO,EAAEA,OAAO;UAChBf,aAAa,EAAEkB;QACjB,CAAC;QAEDvB,iBAAiB,CAACuD,IAAI,IAAI,CAACR,QAAQ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAI/D,eAAe,EAAE;UACnBA,eAAe,CAACuB,QAAQ,CAAC;QAC3B;;QAEA;QACA,IAAIO,QAAQ,IAAIH,OAAO,KAAK,MAAM,EAAE;UAClCI,oBAAoB,CAACR,QAAQ,CAAC;UAC9BvC,KAAK,CAACgF,OAAO,CAAC,sCAAsC,CAAC;QACvD,CAAC,MAAM,IAAI,CAAClC,QAAQ,EAAE;UACpB9C,KAAK,CAACiF,OAAO,CAAC,4DAA4D,CAAC;QAC7E,CAAC,MAAM,IAAItC,OAAO,KAAK,MAAM,EAAE;UAC7B3C,KAAK,CAACiF,OAAO,CAAC,+DAA+D,CAAC;QAChF;MACF;MAEA5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEF,eAAe,EAAE+B,oBAAoB,EAAET,iBAAiB,CAAC,CAAC;EAE3E,MAAM4C,gBAAgB,GAAGpF,WAAW,CAAC,MAAM;IACzC,IAAImC,WAAW,CAACmC,OAAO,EAAE;MACvBe,aAAa,CAAClD,WAAW,CAACmC,OAAO,CAAC;IACpC;IAEAnC,WAAW,CAACmC,OAAO,GAAGgB,WAAW,CAAC,MAAM;MACtC,IAAIlE,WAAW,IAAIc,SAAS,CAACoC,OAAO,EAAE;QACpCD,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAEzC,eAAe,CAAC;EACrB,CAAC,EAAE,CAACR,WAAW,EAAEQ,eAAe,EAAEyC,YAAY,CAAC,CAAC;EAEhD,MAAMkB,WAAW,GAAGvF,WAAW,CAAC,MAAM;IACpCqB,cAAc,CAAC,IAAI,CAAC;IACpBL,kBAAkB,CAAC,IAAI,CAAC;IACxBe,gBAAgB,CAAC,KAAK,CAAC;IACvBE,eAAe,CAAC,SAAS,CAAC;IAC1B/B,KAAK,CAACgF,OAAO,CAAC,sBAAsB,CAAC;;IAErC;IACA,IAAIxD,WAAW,EAAE;MACf0D,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC1D,WAAW,EAAEV,kBAAkB,EAAEoE,gBAAgB,CAAC,CAAC;EAEvD,MAAMI,UAAU,GAAGxF,WAAW,CAAC,MAAM;IACnCqB,cAAc,CAAC,KAAK,CAAC;IACrBL,kBAAkB,CAAC,KAAK,CAAC;IACzBe,gBAAgB,CAAC,KAAK,CAAC;IACvBE,eAAe,CAAC,SAAS,CAAC;IAC1BwD,eAAe,CAAC,CAAC;IACjBvF,KAAK,CAACwF,IAAI,CAAC,sBAAsB,CAAC;EACpC,CAAC,EAAE,CAAC1E,kBAAkB,CAAC,CAAC;EAExB,MAAMyE,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItD,WAAW,CAACmC,OAAO,EAAE;MACvBe,aAAa,CAAClD,WAAW,CAACmC,OAAO,CAAC;MAClCnC,WAAW,CAACmC,OAAO,GAAG,IAAI;IAC5B;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhE,cAAc,CAAC,CAACD,WAAW,CAAC;IAC5B,IAAI,CAACA,WAAW,EAAE;MAChB0D,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED1F,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX0F,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN1F,SAAS,CAAC,MAAM;IACd,IAAI2B,WAAW,IAAIN,WAAW,EAAE;MAC9BgE,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC/D,WAAW,EAAEE,eAAe,EAAER,WAAW,EAAEgE,gBAAgB,CAAC,CAAC;EAEjE,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQ5D,YAAY;MAClB,KAAK,MAAM;QACT,oBAAOlB,OAAA,CAACH,aAAa;UAACkF,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,MAAM;QACT,oBAAOnF,OAAA,CAACF,qBAAqB;UAACiF,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE;QACE,oBAAOnF,OAAA,CAACL,KAAK;UAACoF,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQlE,YAAY;MAClB,KAAK,MAAM;QACT,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,cAAc;MACvB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACElB,OAAA;IAAK+E,SAAS,EAAC,WAAW;IAAAM,QAAA,gBAExBrF,OAAA;MAAK+E,SAAS,EAAC,mDAAmD;MAAAM,QAAA,gBAChErF,OAAA;QAAK+E,SAAS,EAAC,YAAY;QAAAM,QAAA,gBACzBrF,OAAA,CAACX,MAAM,CAACiG,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BT,SAAS,EAAE,oFACTzE,WAAW,GACP,mFAAmF,GACnF,mFAAmF,EACtF;UACHoF,OAAO,EAAEpF,WAAW,GAAGoE,UAAU,GAAGD,WAAY;UAAAY,QAAA,GAE/C/E,WAAW,gBAAGN,OAAA,CAACT,MAAM;YAACwF,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnF,OAAA,CAACV,MAAM;YAACyF,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvF7E,WAAW,GAAG,aAAa,GAAG,cAAc;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEhBnF,OAAA,CAACX,MAAM,CAACiG,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BT,SAAS,EAAC,oNAAoN;UAC9NW,OAAO,EAAEnC,YAAa;UACtBoC,QAAQ,EAAE,CAACrF,WAAW,IAAIE,WAAY;UAAA6E,QAAA,gBAEtCrF,OAAA,CAACR,QAAQ;YAACuF,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpC3E,WAAW,GAAG,cAAc,GAAG,eAAe;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAENnF,OAAA;QAAK+E,SAAS,EAAC,yBAAyB;QAAAM,QAAA,gBACtCrF,OAAA;UAAO+E,SAAS,EAAC,qDAAqD;UAAAM,QAAA,gBACpErF,OAAA;YACE4F,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEjF,WAAY;YACrBkF,QAAQ,EAAEjB,iBAAkB;YAC5Bc,QAAQ,EAAE,CAACrF,WAAY;YACvByE,SAAS,EAAC;UAA0E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAEPvE,WAAW,iBACVZ,OAAA;UACE+F,KAAK,EAAEjF,eAAgB;UACvBgF,QAAQ,EAAGE,CAAC,IAAKjF,kBAAkB,CAACkF,MAAM,CAACD,CAAC,CAACE,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5DJ,QAAQ,EAAE,CAACrF,WAAY;UACvByE,SAAS,EAAC,8GAA8G;UAAAM,QAAA,gBAExHrF,OAAA;YAAQ+F,KAAK,EAAE,IAAK;YAAAV,QAAA,EAAC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnF,OAAA;YAAQ+F,KAAK,EAAE,IAAK;YAAAV,QAAA,EAAC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCnF,OAAA;YAAQ+F,KAAK,EAAE,KAAM;YAAAV,QAAA,EAAC;UAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7E,WAAW,iBACVN,OAAA;MAAK+E,SAAS,EAAC,kDAAkD;MAAAM,QAAA,gBAC/DrF,OAAA;QAAK+E,SAAS,EAAC,kFAAkF;QAAAM,QAAA,GAC9FrE,aAAa,gBACZhB,OAAA,CAACH,aAAa;UAACkF,SAAS,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzDnF,OAAA,CAACJ,OAAO;UAACmF,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACjD,eACDnF,OAAA;UAAM+E,SAAS,EAAC,mCAAmC;UAAAM,QAAA,EAChDrE,aAAa,GAAG,gBAAgB,GAAG;QAAU;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnF,OAAA;QAAK+E,SAAS,EAAC,kFAAkF;QAAAM,QAAA,GAC9FP,cAAc,CAAC,CAAC,eACjB9E,OAAA;UAAM+E,SAAS,EAAC,wCAAwC;UAAAM,QAAA,EACrDD,cAAc,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnF,OAAA;MAAK+E,SAAS,EAAC,2DAA2D;MAAAM,QAAA,GACvE/E,WAAW,gBACVN,OAAA,CAACb,MAAM;QACLgH,GAAG,EAAE/E,SAAU;QACfgF,KAAK,EAAE,KAAM;QACbC,gBAAgB,EAAC,YAAY;QAC7B/E,gBAAgB,EAAEA,gBAAiB;QACnCyD,SAAS,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAEFnF,OAAA;QAAK+E,SAAS,EAAC,+GAA+G;QAAAM,QAAA,eAC5HrF,OAAA;UAAK+E,SAAS,EAAC,wBAAwB;UAAAM,QAAA,gBACrCrF,OAAA,CAACN,OAAO;YAACqF,SAAS,EAAC;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DnF,OAAA;YAAG+E,SAAS,EAAC,qBAAqB;YAAAM,QAAA,EAAC;UAAgD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvFnF,OAAA;YAAG+E,SAAS,EAAC,4BAA4B;YAAAM,QAAA,EAAC;UAAoC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA3E,WAAW,iBACVR,OAAA;QAAK+E,SAAS,EAAC,0EAA0E;QAAAM,QAAA,eACvFrF,OAAA;UAAK+E,SAAS,EAAC,8EAA8E;UAAAM,QAAA,gBAC3FrF,OAAA,CAACR,QAAQ;YAACuF,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA7E,WAAW,IAAIY,YAAY,KAAK,SAAS,iBACxClB,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAM,QAAA,eACrCrF,OAAA;UAAK+E,SAAS,EAAC,0EAA0E;UAAAM,QAAA,GACtFP,cAAc,CAAC,CAAC,eACjB9E,OAAA;YAAM+E,SAAS,EAAC,0BAA0B;YAAAM,QAAA,EAAED,cAAc,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnF,OAAA;MAAK+E,SAAS,EAAC,0CAA0C;MAAAM,QAAA,gBACvDrF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAM,QAAA,gBACrCrF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAM,QAAA,eAC1ErF,OAAA,CAACP,OAAO;YAACsF,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNnF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAM,QAAA,GAAC,mBAAiB,EAAC3E,cAAc,CAAC4F,MAAM,EAAC,GAAC;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC,EAELzE,cAAc,CAAC4F,MAAM,GAAG,CAAC,gBACxBtG,OAAA;QAAK+E,SAAS,EAAC,sDAAsD;QAAAM,QAAA,EAClE3E,cAAc,CAAC6F,GAAG,CAAEC,KAAK,iBACxBxG,OAAA,CAACX,MAAM,CAACoH,GAAG;UAETlB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BT,SAAS,EAAC,2HAA2H;UAAAM,QAAA,gBAErIrF,OAAA;YACE8D,GAAG,EAAE0C,KAAK,CAAC1C,GAAI;YACf4C,GAAG,EAAC,UAAU;YACd3B,SAAS,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFnF,OAAA;YAAK+E,SAAS,EAAC,KAAK;YAAAM,QAAA,gBAClBrF,OAAA;cAAG+E,SAAS,EAAC,4BAA4B;cAAAM,QAAA,EACtC,IAAIzB,IAAI,CAAC4C,KAAK,CAACzC,SAAS,CAAC,CAAC4C,kBAAkB,CAAC;YAAC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJnF,OAAA;cAAK+E,SAAS,EAAC,wCAAwC;cAAAM,QAAA,gBACrDrF,OAAA;gBAAM+E,SAAS,EAAE,uEACfyB,KAAK,CAACxF,aAAa,GACf,6BAA6B,GAC7B,yBAAyB,EAC5B;gBAAAqE,QAAA,EACAmB,KAAK,CAACxF,aAAa,GAAG,SAAS,GAAG;cAAY;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACPnF,OAAA;gBAAM+E,SAAS,EAAE,uEACfyB,KAAK,CAACzE,OAAO,KAAK,MAAM,GACpB,2BAA2B,GAC3B,+BAA+B,EAClC;gBAAAsD,QAAA,EACAmB,KAAK,CAACzE,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG;cAAQ;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnF,OAAA;cAAM+E,SAAS,EAAE,uEACfyB,KAAK,CAACvC,QAAQ,GACV,6BAA6B,GAC7B,+BAA+B,EAClC;cAAAoB,QAAA,EACAmB,KAAK,CAACvC,QAAQ,GAAG,YAAY,GAAG;YAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GApCDqB,KAAK,CAAC7C,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqCH,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENnF,OAAA;QAAK+E,SAAS,EAAC,gCAAgC;QAAAM,QAAA,gBAC7CrF,OAAA,CAACP,OAAO;UAACsF,SAAS,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDnF,OAAA;UAAAqF,QAAA,EAAG;QAAiE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAnXIJ,SAAS;AAAA2G,EAAA,GAAT3G,SAAS;AAqXf,eAAeA,SAAS;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}