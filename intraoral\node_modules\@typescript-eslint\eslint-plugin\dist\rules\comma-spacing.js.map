{"version": 3, "file": "comma-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/comma-spacing.ts"], "names": [], "mappings": ";;AACA,oDAA2D;AAE3D,kCAOiB;AAUjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE;wBACN,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,KAAK;qBACf;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI;qBACd;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,uCAAuC;YACnD,OAAO,EAAE,kCAAkC;SAC5C;KACF;IACD,cAAc,EAAE;QACd;YACE,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,IAAI;SACZ;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;QAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;QACvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAA4B,CAAC;QAE1D;;;WAGG;QACH,SAAS,2BAA2B,CAClC,IAAsD;YAEtD,IAAI,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACnD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACnC,IAAI,KAA4B,CAAC;gBACjC,IAAI,OAAO,IAAI,IAAI,EAAE;oBACnB,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,aAAc,CAAC,CAAC;oBACjD,IAAI,KAAK,IAAI,IAAA,mBAAY,EAAC,KAAK,CAAC,EAAE;wBAChC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;qBAC1B;iBACF;qBAAM;oBACL,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;iBAC3C;gBAED,aAAa,GAAG,KAAK,CAAC;aACvB;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,0CAA0C,CACjD,IAAyC;YAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YACvC,IAAI,WAAW,EAAE;gBACf,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;gBAC3C,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,UAAU,IAAI,IAAA,mBAAY,EAAC,UAAU,CAAC,EAAE;oBAC1C,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAC/B;aACF;QACH,CAAC;QAED;;;;;WAKG;QACH,SAAS,oBAAoB,CAC3B,UAAoC,EACpC,SAAgC,EAChC,SAAgC;YAEhC,IACE,SAAS;gBACT,IAAA,wBAAiB,EAAC,SAAS,EAAE,UAAU,CAAC;gBACxC,yGAAyG;gBACzG,WAAW,KAAK,UAAU,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,EACtE;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE;wBACJ,GAAG,EAAE,QAAQ;qBACd;oBACD,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY;oBACjD,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW;wBACT,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC;wBACzC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CACpB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACzC,EAAE,CACH;iBACR,CAAC,CAAC;aACJ;YAED,IAAI,SAAS,IAAI,IAAA,0BAAmB,EAAC,SAAS,CAAC,EAAE;gBAC/C,OAAO;aACR;YAED,IACE,UAAU;gBACV,SAAS;gBACT,CAAC,IAAA,0BAAmB,EAAC,SAAS,CAAC,IAAI,IAAA,4BAAqB,EAAC,SAAS,CAAC,CAAC,EACpE;gBACA,OAAO;aACR;YAED,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,EAAE;gBACvE,OAAO;aACR;YAED,IACE,SAAS;gBACT,IAAA,wBAAiB,EAAC,UAAU,EAAE,SAAS,CAAC;gBACxC,yGAAyG;gBACzG,UAAU,KAAK,UAAU,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,CAAC,EACrE;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE;wBACJ,GAAG,EAAE,OAAO;qBACb;oBACD,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY;oBAChD,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,UAAU;wBACR,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,UAAU,EAAE,GAAG,CAAC;wBACxC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CACpB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACzC,EAAE,CACH;iBACR,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,0BAA0B,EAAE,0CAA0C;YACtE,eAAe,EAAE,2BAA2B;YAC5C,YAAY,EAAE,2BAA2B;YAEzC,cAAc;gBACZ,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;oBACrC,IAAI,CAAC,IAAA,mBAAY,EAAC,KAAK,CAAC,EAAE;wBACxB,OAAO;qBACR;oBAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC3C,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAE3C,oBAAoB,CAClB,KAAK,EACL,IAAA,mBAAY,EAAC,SAAS,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;wBACjD,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,SAAS,EACb,CAAC,SAAS,IAAI,IAAA,mBAAY,EAAC,SAAS,CAAC,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC;wBAChE,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,SAAS,CACd,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}