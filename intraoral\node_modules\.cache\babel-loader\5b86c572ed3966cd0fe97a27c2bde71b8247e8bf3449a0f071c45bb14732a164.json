{"ast": null, "code": "import { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nimport { frame } from '../frameloop/frame.mjs';\nfunction useForceUpdate() {\n  const isMounted = useIsMounted();\n  const [forcedRenderCount, setForcedRenderCount] = useState(0);\n  const forceRender = useCallback(() => {\n    isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n  }, [forcedRenderCount]);\n  /**\n   * Defer this to the end of the next animation frame in case there are multiple\n   * synchronous calls.\n   */\n  const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n  return [deferredForceRender, forcedRenderCount];\n}\nexport { useForceUpdate };", "map": {"version": 3, "names": ["useState", "useCallback", "useIsMounted", "frame", "useForceUpdate", "isMounted", "forcedRenderCount", "setForcedRenderCount", "forceRender", "current", "deferred<PERSON><PERSON><PERSON><PERSON><PERSON>", "postRender"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/utils/use-force-update.mjs"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction useForceUpdate() {\n    const isMounted = useIsMounted();\n    const [forcedRenderCount, setForcedRenderCount] = useState(0);\n    const forceRender = useCallback(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\nexport { useForceUpdate };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,KAAK,QAAQ,wBAAwB;AAE9C,SAASC,cAAcA,CAAA,EAAG;EACtB,MAAMC,SAAS,GAAGH,YAAY,CAAC,CAAC;EAChC,MAAM,CAACI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAMQ,WAAW,GAAGP,WAAW,CAAC,MAAM;IAClCI,SAAS,CAACI,OAAO,IAAIF,oBAAoB,CAACD,iBAAiB,GAAG,CAAC,CAAC;EACpE,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EACvB;AACJ;AACA;AACA;EACI,MAAMI,mBAAmB,GAAGT,WAAW,CAAC,MAAME,KAAK,CAACQ,UAAU,CAACH,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAC3F,OAAO,CAACE,mBAAmB,EAAEJ,iBAAiB,CAAC;AACnD;AAEA,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}