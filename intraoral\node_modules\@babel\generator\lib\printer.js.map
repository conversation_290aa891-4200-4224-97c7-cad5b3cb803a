{"version": 3, "names": ["_buffer", "require", "_index", "n", "_t", "_tokenMap", "generatorFunctions", "_deprecated", "isExpression", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumMember", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "HAS_NEWLINE", "HAS_NEWLINE_OR_BlOCK_COMMENT_END", "commentIsNewline", "c", "type", "test", "value", "needsParens", "Printer", "constructor", "format", "map", "tokens", "originalCode", "tokenContext", "TokenContext", "normal", "_tokens", "_originalCode", "_currentNode", "_indent", "_indentRepeat", "_insideAux", "_noLineTerminator", "_noLineTerminatorAfterNode", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_endsWithDiv", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "tokenMap", "_boundGetRawIdentifier", "_getRawIdentifier", "bind", "_printSemicolonBeforeNextNode", "_printSemicolonBeforeNextToken", "indent", "style", "length", "_inputMap", "_buf", "<PERSON><PERSON><PERSON>", "enterForStatementInit", "forInitHead", "forInOrInitHeadAccumulate", "enterForXStatementInit", "isForOf", "forOfHead", "forInHead", "enterDelimited", "oldTokenContext", "oldNoLineTerminatorAfterNode", "generate", "ast", "preserveFormat", "TokenMap", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "node", "start", "end", "endMatches", "getCurrentLine", "indexes", "getIndexes", "_catchUpTo", "loc", "_queue", "rightBrace", "minified", "removeLastSemicolon", "sourceWithOffset", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "forInOrInitHeadAccumulatePassThroughMask", "_maybePrintInnerComments", "_catchUpToCurrentToken", "charCodeAt", "_append", "number", "isNonDecimalLiteral", "secondChar", "Number", "isInteger", "maybeNewline", "occurrenceCount", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "String", "fromCharCode", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWith", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "findMatching", "appendChar", "_maybeIndent", "append", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "catchUp", "line", "count", "column", "index", "spacesCount", "getCurrentColumn", "spaces", "slice", "replace", "repeat", "printTerminatorless", "trailingCommentsLineOffset", "_node$extra", "_node$leadingComments", "_node$leadingComments2", "nodeType", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "parent", "oldInAux", "parenthesized", "extra", "shouldPrintParens", "retainFunctionParens", "leadingComments", "parentType", "callee", "indentParenthesized", "some", "isLastChild", "_node$trailingComment", "trailingComments", "_printLeadingComments", "_printTrailingComments", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "statement", "separator", "printTrailingSeparator", "addNewlines", "iterator", "_nodes$0$loc", "startLine", "newlineOpts", "nextNodeStartLine", "boundSeparator", "len", "_printNewline", "_node$trailingComment2", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "printBlock", "body", "lineOffset", "innerComments", "_printComments", "comments", "nextTokenStr", "nextTokenOccurrenceCount", "_this$tokenMap", "printInnerComments", "nextToken", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "printList", "items", "commaSeparator", "shouldPrintTrailingComma", "listEnd", "listEndIndex", "findLastIndex", "matchesOriginal", "newLine", "opts", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "commentTok", "find", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "newlineRegex", "RegExp", "indentSize", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "addDeprecatedGenerators", "_default", "exports", "default", "last"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isExpression,\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumMember,\n} from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport { TokenMap } from \"./token-map.ts\";\nimport type { GeneratorOptions } from \"./index.ts\";\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport {\n  addDeprecatedGenerators,\n  type DeprecatedBabel7ASTTypes,\n} from \"./generators/deprecated.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\nimport type { Token } from \"@babel/parser\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_NEWLINE_OR_BlOCK_COMMENT_END = /[\\n\\r\\u2028\\u2029]|\\*\\//;\n\nfunction commentIsNewline(c: t.Comment) {\n  return c.type === \"CommentLine\" || HAS_NEWLINE.test(c.value);\n}\n\nconst { needsParens } = n;\n\nimport { TokenContext } from \"./node/index.ts\";\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  preserveFormat: boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  /**\n   * @deprecated Removed in Babel 8, syntax type is always 'hash'\n   */\n  recordAndTupleSyntaxType?: GeneratorOptions[\"recordAndTupleSyntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: GeneratorOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer, occurrenceCount: number, last: boolean) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n  printTrailingSeparator?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(\n    format: Format,\n    map: SourceMap,\n    tokens?: Token[],\n    originalCode?: string,\n  ) {\n    this.format = format;\n\n    this._tokens = tokens;\n    this._originalCode = originalCode;\n\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n\n    this._buf = new Buffer(map, format.indent.style[0]);\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n\n  enterForStatementInit() {\n    this.tokenContext |=\n      TokenContext.forInitHead | TokenContext.forInOrInitHeadAccumulate;\n    return () => (this.tokenContext = TokenContext.normal);\n  }\n\n  enterForXStatementInit(isForOf: boolean) {\n    if (isForOf) {\n      this.tokenContext |= TokenContext.forOfHead;\n      return null;\n    } else {\n      this.tokenContext |=\n        TokenContext.forInHead | TokenContext.forInOrInitHeadAccumulate;\n      return () => (this.tokenContext = TokenContext.normal);\n    }\n  }\n\n  enterDelimited() {\n    const oldTokenContext = this.tokenContext;\n    const oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n    if (\n      !(oldTokenContext & TokenContext.forInOrInitHeadAccumulate) &&\n      oldNoLineTerminatorAfterNode === null\n    ) {\n      return () => {};\n    }\n    this._noLineTerminatorAfterNode = null;\n    this.tokenContext = TokenContext.normal;\n    return () => {\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n      this.tokenContext = oldTokenContext;\n    };\n  }\n\n  tokenContext: number = TokenContext.normal;\n\n  _tokens: Token[] = null;\n  _originalCode: string | null = null;\n\n  declare _buf: Buffer;\n  _currentNode: t.Node = null;\n  _indent: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _noLineTerminator: boolean = false;\n  _noLineTerminatorAfterNode: t.Node | null = null;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _endsWithDiv = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n  tokenMap: TokenMap = null;\n\n  _boundGetRawIdentifier = this._getRawIdentifier.bind(this);\n\n  generate(ast: t.Node) {\n    if (this.format.preserveFormat) {\n      this.tokenMap = new TokenMap(ast, this._tokens, this._originalCode);\n    }\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent--;\n  }\n\n  /**\n   * If the next token is on the same line, we must first print a semicolon.\n   * This option is only used in `preserveFormat` node, for semicolons that\n   * might have omitted due to them being absent in the original code (thanks\n   * to ASI).\n   *\n   * We need both *NextToken and *NextNode because we only want to insert the\n   * semicolon when the next token starts a new node, and not in cases like\n   * foo} (where } is not starting a new node). So we first set *NextNode, and\n   * then the print() method will move it to *NextToken.\n   */\n  _printSemicolonBeforeNextNode: number = -1;\n  _printSemicolonBeforeNextToken: number = -1;\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n      this._noLineTerminator = false;\n      return;\n    }\n    if (this.tokenMap) {\n      const node = this._currentNode;\n      if (node.start != null && node.end != null) {\n        if (!this.tokenMap.endMatches(node, \";\")) {\n          // no semicolon\n          this._printSemicolonBeforeNextNode = this._buf.getCurrentLine();\n          return;\n        }\n        const indexes = this.tokenMap.getIndexes(this._currentNode);\n        this._catchUpTo(this._tokens[indexes[indexes.length - 1]].loc.start);\n      }\n    }\n    this._queue(charCodes.semicolon);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    const { format } = this;\n    if (format.compact || format.preserveFormat) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this.tokenContext &= TokenContext.forInOrInitHeadAccumulatePassThroughMask;\n\n    this._maybePrintInnerComments(str);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str);\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (this._endsWithDiv && str.charCodeAt(0) === charCodes.slash)\n    ) {\n      this._space();\n    }\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string, number?: number): void {\n    // const NON_DECIMAL_LITERAL = /^0[box]/;\n    function isNonDecimalLiteral(str: string) {\n      if (str.length > 2 && str.charCodeAt(0) === charCodes.digit0) {\n        const secondChar = str.charCodeAt(1);\n        return (\n          secondChar === charCodes.lowercaseB ||\n          secondChar === charCodes.lowercaseO ||\n          secondChar === charCodes.lowercaseX\n        );\n      }\n      return false;\n    }\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(number) &&\n      !isNonDecimalLiteral(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   *\n   * @param {string} str The string to append.\n   * @param {boolean} [maybeNewline=false] Wether `str` might potentially\n   *    contain a line terminator or not.\n   * @param {number} [occurrenceCount=0] The occurrence count of this token in\n   *    the current node. This is used when printing in `preserveFormat` mode,\n   *    to know which token we should map to (for example, to disambiguate the\n   *    commas in an array literal).\n   */\n  token(str: string, maybeNewline = false, occurrenceCount = 0): void {\n    this.tokenContext &= TokenContext.forInOrInitHeadAccumulatePassThroughMask;\n\n    this._maybePrintInnerComments(str, occurrenceCount);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str, occurrenceCount);\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this.tokenContext &= TokenContext.forInOrInitHeadAccumulatePassThroughMask;\n\n    const str = String.fromCharCode(char);\n    this._maybePrintInnerComments(str);\n\n    this._maybeAddAuxComment();\n\n    if (this.tokenMap) this._catchUpToCurrentToken(str);\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc || this.format.preserveFormat) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _catchUpToCurrentToken(str: string, occurrenceCount: number = 0): void {\n    // Assert: this.tokenMap\n\n    const token = this.tokenMap.findMatching(\n      this._currentNode,\n      str,\n      occurrenceCount,\n    );\n    if (token) this._catchUpTo(token.loc.start);\n\n    if (\n      this._printSemicolonBeforeNextToken !== -1 &&\n      this._printSemicolonBeforeNextToken === this._buf.getCurrentLine()\n    ) {\n      this._buf.appendChar(charCodes.semicolon);\n      this._endsWithWord = false;\n      this._endsWithInteger = false;\n      this._endsWithDiv = false;\n    }\n    this._printSemicolonBeforeNextToken = -1;\n    this._printSemicolonBeforeNextNode = -1;\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _queue(char: number) {\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    const { format } = this;\n    if (!format.preserveFormat) {\n      if (format.retainLines && loc?.[prop]) {\n        this.catchUp(loc[prop].line);\n      }\n      return;\n    }\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc?.[prop];\n    if (pos != null) this._catchUpTo(pos);\n  }\n\n  _catchUpTo({ line, column, index }: Pos) {\n    const count = line - this._buf.getCurrentLine();\n    if (count > 0 && this._noLineTerminator) {\n      // We cannot inject new lines when _noLineTemrinator is set\n      // to `true`, or we would generate invalid code.\n      return;\n    }\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n\n    const spacesCount =\n      count > 0 ? column : column - this._buf.getCurrentColumn();\n    if (spacesCount > 0) {\n      const spaces = this._originalCode\n        ? this._originalCode\n            .slice(index - spacesCount, index)\n            // https://tc39.es/ecma262/#sec-white-space\n            .replace(/[^\\t\\v\\f\\uFEFF\\p{Space_Separator}]/gu, \" \")\n        : \" \".repeat(spacesCount);\n      this._append(spaces, false);\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    this._noLineTerminator = true;\n    this.print(node);\n  }\n\n  print(\n    node: t.Node | null,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          | DeprecatedBabel7ASTTypes\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    const parent = this._currentNode;\n    this._currentNode = node;\n\n    if (this.tokenMap) {\n      this._printSemicolonBeforeNextToken = this._printSemicolonBeforeNextNode;\n    }\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == null;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const parenthesized = node.extra?.parenthesized as boolean | undefined;\n    let shouldPrintParens =\n      (parenthesized && format.preserveFormat) ||\n      (parenthesized &&\n        format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\") ||\n      needsParens(\n        node,\n        parent,\n        this.tokenContext,\n        format.preserveFormat ? this._boundGetRawIdentifier : undefined,\n      );\n\n    if (\n      !shouldPrintParens &&\n      parenthesized &&\n      node.leadingComments?.length &&\n      node.leadingComments[0].type === \"CommentBlock\"\n    ) {\n      const parentType = parent?.type;\n      switch (parentType) {\n        case \"ExpressionStatement\":\n        case \"VariableDeclarator\":\n        case \"AssignmentExpression\":\n        case \"ReturnStatement\":\n          break;\n        case \"CallExpression\":\n        case \"OptionalCallExpression\":\n        case \"NewExpression\":\n          if (parent.callee !== node) break;\n        // falls through\n        default:\n          shouldPrintParens = true;\n      }\n    }\n\n    let indentParenthesized = false;\n    if (\n      !shouldPrintParens &&\n      this._noLineTerminator &&\n      (node.leadingComments?.some(commentIsNewline) ||\n        (this.format.retainLines &&\n          node.loc &&\n          node.loc.start.line > this._buf.getCurrentLine()))\n    ) {\n      shouldPrintParens = true;\n      indentParenthesized = true;\n    }\n\n    let oldNoLineTerminatorAfterNode;\n    let oldTokenContext;\n    if (!shouldPrintParens) {\n      noLineTerminatorAfter ||=\n        parent &&\n        this._noLineTerminatorAfterNode === parent &&\n        n.isLastChild(parent, node);\n      if (noLineTerminatorAfter) {\n        if (node.trailingComments?.some(commentIsNewline)) {\n          if (isExpression(node)) shouldPrintParens = true;\n        } else {\n          oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n          this._noLineTerminatorAfterNode = node;\n        }\n      }\n    }\n\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      if (indentParenthesized) this.indent();\n      this._endsWithInnerRaw = false;\n      if (this.tokenContext & TokenContext.forInOrInitHeadAccumulate) {\n        oldTokenContext = this.tokenContext;\n        this.tokenContext = TokenContext.normal;\n      }\n      oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n      this._noLineTerminatorAfterNode = null;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(\n      loc,\n      // @ts-expect-error Expected 1 arguments, but got 3.\n      printMethod.bind(this, node, parent),\n    );\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      if (indentParenthesized) {\n        this.dedent();\n        this.newline();\n      }\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n      if (oldTokenContext) this.tokenContext = oldTokenContext;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._currentNode = parent;\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    if (oldNoLineTerminatorAfterNode !== undefined) {\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    }\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    statement?: boolean,\n    indent?: boolean,\n    separator?: PrintJoinOptions[\"separator\"],\n    printTrailingSeparator?: boolean,\n    addNewlines?: PrintJoinOptions[\"addNewlines\"],\n    iterator?: PrintJoinOptions[\"iterator\"],\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!nodes?.length) return;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const boundSeparator = separator?.bind(this);\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, undefined, trailingCommentsLineOffset || 0);\n\n      iterator?.(node, i);\n\n      if (boundSeparator != null) {\n        if (i < len - 1) boundSeparator(i, false);\n        else if (printTrailingSeparator) boundSeparator(i, true);\n      }\n\n      if (statement) {\n        if (!node.trailingComments?.length) {\n          this._lastCommentLine = 0;\n        }\n\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments(\n    nextTokenStr: string,\n    nextTokenOccurrenceCount?: number,\n  ) {\n    if (this._endsWithInnerRaw) {\n      this.printInnerComments(\n        this.tokenMap?.findMatching(\n          this._currentNode,\n          nextTokenStr,\n          nextTokenOccurrenceCount,\n        ),\n      );\n    }\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments(nextToken?: Token) {\n    const node = this._currentNode;\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(\n      COMMENT_TYPE.INNER,\n      comments,\n      node,\n      undefined,\n      undefined,\n      nextToken,\n    );\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    indent?: boolean,\n    trailingCommentsLineOffset?: number,\n    addNewlines?: PrintSequenceOptions[\"addNewlines\"],\n  ) {\n    this.printJoin(\n      nodes,\n      true,\n      indent ?? false,\n      undefined,\n      undefined,\n      addNewlines,\n      undefined,\n      trailingCommentsLineOffset,\n    );\n  }\n\n  printList(\n    items: t.Node[],\n    printTrailingSeparator?: boolean,\n    statement?: boolean,\n    indent?: boolean,\n    separator?: PrintListOptions[\"separator\"],\n    iterator?: PrintListOptions[\"iterator\"],\n  ) {\n    this.printJoin(\n      items,\n      statement,\n      indent,\n      separator ?? commaSeparator,\n      printTrailingSeparator,\n      undefined,\n      iterator,\n    );\n  }\n\n  shouldPrintTrailingComma(listEnd: string): boolean | null {\n    if (!this.tokenMap) return null;\n\n    const listEndIndex = this.tokenMap.findLastIndex(this._currentNode, token =>\n      this.tokenMap.matchesOriginal(token, listEnd),\n    );\n    if (listEndIndex <= 0) return null;\n    return this.tokenMap.matchesOriginal(this._tokens[listEndIndex - 1], \",\");\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(\n    comment: t.Comment,\n    nextToken?: Token,\n  ): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      HAS_NEWLINE_OR_BlOCK_COMMENT_END.test(comment.value)\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    if (nextToken && this.tokenMap) {\n      const commentTok = this.tokenMap.find(\n        this._currentNode,\n        token => token.value === comment.value,\n      );\n      if (commentTok && commentTok.start > nextToken.start) {\n        return PRINT_COMMENT_HINT.DEFER;\n      }\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace &&\n      lastCharCode !== charCodes.leftParenthesis\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n        if (this.format.concise) {\n          val = val.replace(/\\n(?!$)/g, `\\n`);\n        } else {\n          let indentSize = this.format.retainLines\n            ? 0\n            : this._buf.getCurrentColumn();\n\n          if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n            indentSize += this._getIndent();\n          }\n\n          val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n        }\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid converting a / operator into a line comment by appending /* to it\n    if (this._endsWithDiv) this._space();\n\n    if (this.tokenMap) {\n      const { _printSemicolonBeforeNextToken, _printSemicolonBeforeNextNode } =\n        this;\n      this._printSemicolonBeforeNextToken = -1;\n      this._printSemicolonBeforeNextNode = -1;\n      this.source(\"start\", comment.loc);\n      this._append(val, isBlockComment);\n      this._printSemicolonBeforeNextNode = _printSemicolonBeforeNextNode;\n      this._printSemicolonBeforeNextToken = _printSemicolonBeforeNextToken;\n    } else {\n      this.source(\"start\", comment.loc);\n      this._append(val, isBlockComment);\n    }\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n    nextToken?: Token,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment, nextToken);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine !== commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumMember(node);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  addDeprecatedGenerators(Printer);\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer, occurrenceCount: number, last: boolean) {\n  this.token(\",\", false, occurrenceCount);\n  if (!last) this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAAqC,IAAAE,CAAA,GAAAD,MAAA;AAErC,IAAAE,EAAA,GAAAH,OAAA;AAUA,IAAAI,SAAA,GAAAJ,OAAA;AAEA,IAAAK,kBAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAN,OAAA;AAGoC;EAflCO,YAAY;EACZC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAc,IAAAT,EAAA;AAmBhB,MAAMU,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,gCAAgC,GAAG,yBAAyB;AAElE,SAASC,gBAAgBA,CAACC,CAAY,EAAE;EACtC,OAAOA,CAAC,CAACC,IAAI,KAAK,aAAa,IAAIJ,WAAW,CAACK,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;AAC9D;AAEA,MAAM;EAAEC;AAAY,CAAC,GAAGpB,CAAC;AAqFzB,MAAMqB,OAAO,CAAC;EACZC,WAAWA,CACTC,MAAc,EACdC,GAAc,EACdC,MAAgB,EAChBC,YAAqB,EACrB;IAAA,KAkDFC,YAAY,GAAWC,mBAAY,CAACC,MAAM;IAAA,KAE1CC,OAAO,GAAY,IAAI;IAAA,KACvBC,aAAa,GAAkB,IAAI;IAAA,KAGnCC,YAAY,GAAW,IAAI;IAAA,KAC3BC,OAAO,GAAW,CAAC;IAAA,KACnBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,0BAA0B,GAAkB,IAAI;IAAA,KAChDC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,YAAY,GAAG,KAAK;IAAA,KACpBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IAAA,KACpCC,QAAQ,GAAa,IAAI;IAAA,KAEzBC,sBAAsB,GAAG,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;IAAA,KAiD1DC,6BAA6B,GAAW,CAAC,CAAC;IAAA,KAC1CC,8BAA8B,GAAW,CAAC,CAAC;IAzHzC,IAAI,CAAC7B,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACO,OAAO,GAAGL,MAAM;IACrB,IAAI,CAACM,aAAa,GAAGL,YAAY;IAEjC,IAAI,CAACQ,aAAa,GAAGX,MAAM,CAAC8B,MAAM,CAACC,KAAK,CAACC,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAGhC,GAAG,oBAAHA,GAAG,CAAEgC,SAAS;IAE/B,IAAI,CAACC,IAAI,GAAG,IAAIC,eAAM,CAAClC,GAAG,EAAED,MAAM,CAAC8B,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD;EAKAK,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAChC,YAAY,IACfC,mBAAY,CAACgC,WAAW,GAAGhC,mBAAY,CAACiC,yBAAyB;IACnE,OAAO,MAAO,IAAI,CAAClC,YAAY,GAAGC,mBAAY,CAACC,MAAO;EACxD;EAEAiC,sBAAsBA,CAACC,OAAgB,EAAE;IACvC,IAAIA,OAAO,EAAE;MACX,IAAI,CAACpC,YAAY,IAAIC,mBAAY,CAACoC,SAAS;MAC3C,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IAAI,CAACrC,YAAY,IACfC,mBAAY,CAACqC,SAAS,GAAGrC,mBAAY,CAACiC,yBAAyB;MACjE,OAAO,MAAO,IAAI,CAAClC,YAAY,GAAGC,mBAAY,CAACC,MAAO;IACxD;EACF;EAEAqC,cAAcA,CAAA,EAAG;IACf,MAAMC,eAAe,GAAG,IAAI,CAACxC,YAAY;IACzC,MAAMyC,4BAA4B,GAAG,IAAI,CAAC/B,0BAA0B;IACpE,IACE,EAAE8B,eAAe,GAAGvC,mBAAY,CAACiC,yBAAyB,CAAC,IAC3DO,4BAA4B,KAAK,IAAI,EACrC;MACA,OAAO,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAC/B,0BAA0B,GAAG,IAAI;IACtC,IAAI,CAACV,YAAY,GAAGC,mBAAY,CAACC,MAAM;IACvC,OAAO,MAAM;MACX,IAAI,CAACQ,0BAA0B,GAAG+B,4BAA4B;MAC9D,IAAI,CAACzC,YAAY,GAAGwC,eAAe;IACrC,CAAC;EACH;EA0BAE,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,IAAI,CAAC/C,MAAM,CAACgD,cAAc,EAAE;MAC9B,IAAI,CAACxB,QAAQ,GAAG,IAAIyB,kBAAQ,CAACF,GAAG,EAAE,IAAI,CAACxC,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC;IACrE;IACA,IAAI,CAAC0C,KAAK,CAACH,GAAG,CAAC;IACf,IAAI,CAACI,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAC,CAAC;EACxB;EAMAtB,MAAMA,CAAA,EAAS;IACb,MAAM;MAAE9B;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACgD,cAAc,IAAIhD,MAAM,CAACqD,OAAO,IAAIrD,MAAM,CAACsD,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAAC5C,OAAO,EAAE;EAChB;EAMA6C,MAAMA,CAAA,EAAS;IACb,MAAM;MAAEvD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACgD,cAAc,IAAIhD,MAAM,CAACqD,OAAO,IAAIrD,MAAM,CAACsD,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAAC5C,OAAO,EAAE;EAChB;EAmBA8C,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;MACrC,IAAI,CAAC7C,iBAAiB,GAAG,KAAK;MAC9B;IACF;IACA,IAAI,IAAI,CAACW,QAAQ,EAAE;MACjB,MAAMmC,IAAI,GAAG,IAAI,CAAClD,YAAY;MAC9B,IAAIkD,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACE,GAAG,IAAI,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,CAACrC,QAAQ,CAACsC,UAAU,CAACH,IAAI,EAAE,GAAG,CAAC,EAAE;UAExC,IAAI,CAAC/B,6BAA6B,GAAG,IAAI,CAACM,IAAI,CAAC6B,cAAc,CAAC,CAAC;UAC/D;QACF;QACA,MAAMC,OAAO,GAAG,IAAI,CAACxC,QAAQ,CAACyC,UAAU,CAAC,IAAI,CAACxD,YAAY,CAAC;QAC3D,IAAI,CAACyD,UAAU,CAAC,IAAI,CAAC3D,OAAO,CAACyD,OAAO,CAACA,OAAO,CAAChC,MAAM,GAAG,CAAC,CAAC,CAAC,CAACmC,GAAG,CAACP,KAAK,CAAC;MACtE;IACF;IACA,IAAI,CAACQ,MAAM,GAAoB,CAAC;IAChC,IAAI,CAACvD,iBAAiB,GAAG,KAAK;EAChC;EAMAwD,UAAUA,CAACV,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAAC3D,MAAM,CAACsE,QAAQ,EAAE;MACxB,IAAI,CAACpC,IAAI,CAACqC,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEb,IAAI,CAACQ,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACf,IAAY,EAAQ;IAC9B,IAAI,CAACa,gBAAgB,CAAC,KAAK,EAAEb,IAAI,CAACQ,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAAClB,KAAc,GAAG,KAAK,EAAQ;IAClC,MAAM;MAAEzD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACqD,OAAO,IAAIrD,MAAM,CAACgD,cAAc,EAAE;IAE7C,IAAIS,KAAK,EAAE;MACT,IAAI,CAACmB,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAAC1C,IAAI,CAAC2C,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAAC9E,YAAY,IAAIC,mBAAY,CAAC8E,wCAAwC;IAE1E,IAAI,CAACC,wBAAwB,CAACH,GAAG,CAAC;IAElC,IAAI,CAAC9B,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAAC3B,QAAQ,EAAE,IAAI,CAAC6D,sBAAsB,CAACJ,GAAG,CAAC;IAGnD,IACE,IAAI,CAAC9D,aAAa,IACjB,IAAI,CAACC,YAAY,IAAI6D,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,OAAqB,EAC5D;MACA,IAAI,CAACV,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAACW,OAAO,CAACN,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAAC9D,aAAa,GAAG,IAAI;IACzB,IAAI,CAACN,iBAAiB,GAAGqE,qBAAqB;EAChD;EAMAM,MAAMA,CAACP,GAAW,EAAEO,MAAe,EAAQ;IAEzC,SAASC,mBAAmBA,CAACR,GAAW,EAAE;MACxC,IAAIA,GAAG,CAACjD,MAAM,GAAG,CAAC,IAAIiD,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,OAAqB,EAAE;QAC5D,MAAMI,UAAU,GAAGT,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC;QACpC,OACEI,UAAU,OAAyB,IACnCA,UAAU,QAAyB,IACnCA,UAAU,QAAyB;MAEvC;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACV,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAAC/D,gBAAgB,GACnByE,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC,IACxB,CAACC,mBAAmB,CAACR,GAAG,CAAC,IACzB,CAAC7F,mBAAmB,CAACO,IAAI,CAACsF,GAAG,CAAC,IAC9B,CAAC5F,oBAAoB,CAACM,IAAI,CAACsF,GAAG,CAAC,IAC/BA,GAAG,CAACK,UAAU,CAACL,GAAG,CAACjD,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAaAyC,KAAKA,CAACQ,GAAW,EAAEY,YAAY,GAAG,KAAK,EAAEC,eAAe,GAAG,CAAC,EAAQ;IAClE,IAAI,CAAC1F,YAAY,IAAIC,mBAAY,CAAC8E,wCAAwC;IAE1E,IAAI,CAACC,wBAAwB,CAACH,GAAG,EAAEa,eAAe,CAAC;IAEnD,IAAI,CAAC3C,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAAC3B,QAAQ,EAAE,IAAI,CAAC6D,sBAAsB,CAACJ,GAAG,EAAEa,eAAe,CAAC;IAEpE,MAAMC,QAAQ,GAAG,IAAI,CAAChB,WAAW,CAAC,CAAC;IACnC,MAAMiB,QAAQ,GAAGf,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC;IAClC,IACGS,QAAQ,OAA8B,KAGpCd,GAAG,KAAK,IAAI,IAEXe,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAAC9E,gBAAiB,EACrD;MACA,IAAI,CAAC0D,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAACW,OAAO,CAACN,GAAG,EAAEY,YAAY,CAAC;IAC/B,IAAI,CAAChF,iBAAiB,GAAG,KAAK;EAChC;EAEAoF,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAAC9F,YAAY,IAAIC,mBAAY,CAAC8E,wCAAwC;IAE1E,MAAMF,GAAG,GAAGkB,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC;IACrC,IAAI,CAACd,wBAAwB,CAACH,GAAG,CAAC;IAElC,IAAI,CAAC9B,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAAC3B,QAAQ,EAAE,IAAI,CAAC6D,sBAAsB,CAACJ,GAAG,CAAC;IAEnD,MAAMc,QAAQ,GAAG,IAAI,CAAChB,WAAW,CAAC,CAAC;IACnC,IAEGmB,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAAChF,gBAAiB,EACjD;MACA,IAAI,CAAC0D,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAAClB,WAAW,CAACwC,IAAI,CAAC;IACtB,IAAI,CAACrF,iBAAiB,GAAG,KAAK;EAChC;EAQAwF,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAE7C,KAAe,EAAQ;IAC5C,IAAI6C,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAC7C,KAAK,EAAE;MACV,IAAI,IAAI,CAACzD,MAAM,CAACuG,WAAW,IAAI,IAAI,CAACvG,MAAM,CAACqD,OAAO,EAAE;MAEpD,IAAI,IAAI,CAACrD,MAAM,CAACsD,OAAO,EAAE;QACvB,IAAI,CAACqB,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAACpE,IAAI,CAACsE,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAC,QAAQA,CAACT,IAAY,EAAW;IAC9B,OAAO,IAAI,CAACnB,WAAW,CAAC,CAAC,KAAKmB,IAAI;EACpC;EAEAnB,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAAC7C,IAAI,CAAC6C,WAAW,CAAC,CAAC;EAChC;EAEA6B,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAAC1E,IAAI,CAAC0E,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAAC3E,IAAI,CAAC2E,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAAC3C,GAAoB,EAAE4C,EAAc,EAAE;IAChD,IAAI,CAAC5C,GAAG,EAAE;MACR4C,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAE7C,GAAG,CAAC;IAE3B,IAAI,CAACjC,IAAI,CAAC4E,WAAW,CAAC3C,GAAG,EAAE4C,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAE/C,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAAC6C,QAAQ,CAACE,IAAI,EAAE/C,GAAG,CAAC;IAExB,IAAI,CAACjC,IAAI,CAAC+E,MAAM,CAACC,IAAI,EAAE/C,GAAG,CAAC;EAC7B;EAEAK,gBAAgBA,CACd0C,IAAqB,EACrB/C,GAAoB,EACpBgD,YAAoB,EACd;IACN,IAAI,CAAChD,GAAG,IAAI,IAAI,CAACnE,MAAM,CAACgD,cAAc,EAAE;IAExC,IAAI,CAACgE,QAAQ,CAACE,IAAI,EAAE/C,GAAG,CAAC;IAExB,IAAI,CAACjC,IAAI,CAACsC,gBAAgB,CAAC0C,IAAI,EAAE/C,GAAG,EAAEgD,YAAY,CAAC;EACrD;EAEAC,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAACpF,IAAI,CAACqF,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAACtF,IAAI,CAACuF,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEAzC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACR,MAAM,GAAgB,CAAC;EAC9B;EAEAsC,QAAQA,CAAA,EAAS;IACf,IAAI,CAACtC,MAAM,GAAmB,CAAC;EACjC;EAEAiB,sBAAsBA,CAACJ,GAAW,EAAEa,eAAuB,GAAG,CAAC,EAAQ;IAGrE,MAAMrB,KAAK,GAAG,IAAI,CAACjD,QAAQ,CAACmG,YAAY,CACtC,IAAI,CAAClH,YAAY,EACjBwE,GAAG,EACHa,eACF,CAAC;IACD,IAAIrB,KAAK,EAAE,IAAI,CAACP,UAAU,CAACO,KAAK,CAACN,GAAG,CAACP,KAAK,CAAC;IAE3C,IACE,IAAI,CAAC/B,8BAA8B,KAAK,CAAC,CAAC,IAC1C,IAAI,CAACA,8BAA8B,KAAK,IAAI,CAACK,IAAI,CAAC6B,cAAc,CAAC,CAAC,EAClE;MACA,IAAI,CAAC7B,IAAI,CAAC0F,UAAU,GAAoB,CAAC;MACzC,IAAI,CAACzG,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;IAC3B;IACA,IAAI,CAACS,8BAA8B,GAAG,CAAC,CAAC;IACxC,IAAI,CAACD,6BAA6B,GAAG,CAAC,CAAC;EACzC;EAEA2D,OAAOA,CAACN,GAAW,EAAEY,YAAqB,EAAQ;IAChD,IAAI,CAACgC,YAAY,CAAC5C,GAAG,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAACpD,IAAI,CAAC4F,MAAM,CAAC7C,GAAG,EAAEY,YAAY,CAAC;IAGnC,IAAI,CAAC1E,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAsC,WAAWA,CAACwC,IAAY,EAAQ;IAC9B,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAAChE,IAAI,CAAC0F,UAAU,CAAC1B,IAAI,CAAC;IAG1B,IAAI,CAAC/E,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAgD,MAAMA,CAAC8B,IAAY,EAAE;IACnB,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAAChE,IAAI,CAAC6F,KAAK,CAAC7B,IAAI,CAAC;IAErB,IAAI,CAAC/E,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEA2G,YAAYA,CAACG,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAACtH,OAAO,IACZsH,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAACzE,IAAI,CAAC+F,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC/C;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAACtH,OAAO,IACZsH,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAyB,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAACrI,MAAM,CAACuG,WAAW,EAAE;IAG9B,MAAM+B,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACnG,IAAI,CAAC6B,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAE/C,GAAS,EAAE;IACzC,MAAM;MAAEnE;IAAO,CAAC,GAAG,IAAI;IACvB,IAAI,CAACA,MAAM,CAACgD,cAAc,EAAE;MAC1B,IAAIhD,MAAM,CAACuG,WAAW,IAAIpC,GAAG,YAAHA,GAAG,CAAG+C,IAAI,CAAC,EAAE;QACrC,IAAI,CAACkB,OAAO,CAACjE,GAAG,CAAC+C,IAAI,CAAC,CAACmB,IAAI,CAAC;MAC9B;MACA;IACF;IAGA,MAAMf,GAAG,GAAGnD,GAAG,oBAAHA,GAAG,CAAG+C,IAAI,CAAC;IACvB,IAAII,GAAG,IAAI,IAAI,EAAE,IAAI,CAACpD,UAAU,CAACoD,GAAG,CAAC;EACvC;EAEApD,UAAUA,CAAC;IAAEmE,IAAI;IAAEE,MAAM;IAAEC;EAAW,CAAC,EAAE;IACvC,MAAMF,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACnG,IAAI,CAAC6B,cAAc,CAAC,CAAC;IAC/C,IAAIuE,KAAK,GAAG,CAAC,IAAI,IAAI,CAACzH,iBAAiB,EAAE;MAGvC;IACF;IAEA,KAAK,IAAIyF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;IAEA,MAAM+B,WAAW,GACfH,KAAK,GAAG,CAAC,GAAGC,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACrG,IAAI,CAACwG,gBAAgB,CAAC,CAAC;IAC5D,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,MAAME,MAAM,GAAG,IAAI,CAACnI,aAAa,GAC7B,IAAI,CAACA,aAAa,CACfoI,KAAK,CAACJ,KAAK,GAAGC,WAAW,EAAED,KAAK,CAAC,CAEjCK,OAAO,CAAC,+DAAsC,EAAE,GAAG,CAAC,GACvD,GAAG,CAACC,MAAM,CAACL,WAAW,CAAC;MAC3B,IAAI,CAAClD,OAAO,CAACoD,MAAM,EAAE,KAAK,CAAC;IAC7B;EACF;EAMAT,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACvH,aAAa,GAAG,IAAI,CAACD,OAAO;EAC1C;EAEAqI,mBAAmBA,CAACpF,IAAY,EAAE;IAgBhC,IAAI,CAAC9C,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACqC,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAT,KAAKA,CACHS,IAAmB,EACnBuB,qBAA+B,EAG/B8D,0BAAmC,EACnC;IAAA,IAAAC,WAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACA,IAAI,CAACxF,IAAI,EAAE;IAEX,IAAI,CAACrC,iBAAiB,GAAG,KAAK;IAE9B,MAAM8H,QAAQ,GAAGzF,IAAI,CAACjE,IAAI;IAC1B,MAAMM,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAMqJ,UAAU,GAAGrJ,MAAM,CAACsD,OAAO;IACjC,IAEEK,IAAI,CAAC2F,QAAQ,EACb;MACAtJ,MAAM,CAACsD,OAAO,GAAG,IAAI;IACvB;IAEA,MAAMiG,WAAW,GACf,IAAI,CACFH,QAAQ,CAMT;IACH,IAAIG,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACtB,wBAAwBC,IAAI,CAACC,SAAS,CACpCP,QACF,CAAC,qBAAqBM,IAAI,CAACC,SAAS,CAAChG,IAAI,CAAC5D,WAAW,CAAC6J,IAAI,CAAC,EAC7D,CAAC;IACH;IAEA,MAAMC,MAAM,GAAG,IAAI,CAACpJ,YAAY;IAChC,IAAI,CAACA,YAAY,GAAGkD,IAAI;IAExB,IAAI,IAAI,CAACnC,QAAQ,EAAE;MACjB,IAAI,CAACK,8BAA8B,GAAG,IAAI,CAACD,6BAA6B;IAC1E;IAEA,MAAMkI,QAAQ,GAAG,IAAI,CAAClJ,UAAU;IAChC,IAAI,CAACA,UAAU,GAAG+C,IAAI,CAACQ,GAAG,IAAI,IAAI;IAClC,IAAI,CAAChB,mBAAmB,CAAC,IAAI,CAACvC,UAAU,IAAI,CAACkJ,QAAQ,CAAC;IAEtD,MAAMC,aAAa,IAAAd,WAAA,GAAGtF,IAAI,CAACqG,KAAK,qBAAVf,WAAA,CAAYc,aAAoC;IACtE,IAAIE,iBAAiB,GAClBF,aAAa,IAAI/J,MAAM,CAACgD,cAAc,IACtC+G,aAAa,IACZ/J,MAAM,CAACkK,oBAAoB,IAC3Bd,QAAQ,KAAK,oBAAqB,IACpCvJ,WAAW,CACT8D,IAAI,EACJkG,MAAM,EACN,IAAI,CAACzJ,YAAY,EACjBJ,MAAM,CAACgD,cAAc,GAAG,IAAI,CAACvB,sBAAsB,GAAG+H,SACxD,CAAC;IAEH,IACE,CAACS,iBAAiB,IAClBF,aAAa,KAAAb,qBAAA,GACbvF,IAAI,CAACwG,eAAe,aAApBjB,qBAAA,CAAsBlH,MAAM,IAC5B2B,IAAI,CAACwG,eAAe,CAAC,CAAC,CAAC,CAACzK,IAAI,KAAK,cAAc,EAC/C;MACA,MAAM0K,UAAU,GAAGP,MAAM,oBAANA,MAAM,CAAEnK,IAAI;MAC/B,QAAQ0K,UAAU;QAChB,KAAK,qBAAqB;QAC1B,KAAK,oBAAoB;QACzB,KAAK,sBAAsB;QAC3B,KAAK,iBAAiB;UACpB;QACF,KAAK,gBAAgB;QACrB,KAAK,wBAAwB;QAC7B,KAAK,eAAe;UAClB,IAAIP,MAAM,CAACQ,MAAM,KAAK1G,IAAI,EAAE;QAE9B;UACEsG,iBAAiB,GAAG,IAAI;MAC5B;IACF;IAEA,IAAIK,mBAAmB,GAAG,KAAK;IAC/B,IACE,CAACL,iBAAiB,IAClB,IAAI,CAACpJ,iBAAiB,KACrB,CAAAsI,sBAAA,GAAAxF,IAAI,CAACwG,eAAe,aAApBhB,sBAAA,CAAsBoB,IAAI,CAAC/K,gBAAgB,CAAC,IAC1C,IAAI,CAACQ,MAAM,CAACuG,WAAW,IACtB5C,IAAI,CAACQ,GAAG,IACRR,IAAI,CAACQ,GAAG,CAACP,KAAK,CAACyE,IAAI,GAAG,IAAI,CAACnG,IAAI,CAAC6B,cAAc,CAAC,CAAE,CAAC,EACtD;MACAkG,iBAAiB,GAAG,IAAI;MACxBK,mBAAmB,GAAG,IAAI;IAC5B;IAEA,IAAIzH,4BAA4B;IAChC,IAAID,eAAe;IACnB,IAAI,CAACqH,iBAAiB,EAAE;MACtB/E,qBAAqB,KAArBA,qBAAqB,GACnB2E,MAAM,IACN,IAAI,CAAC/I,0BAA0B,KAAK+I,MAAM,IAC1CpL,CAAC,CAAC+L,WAAW,CAACX,MAAM,EAAElG,IAAI,CAAC;MAC7B,IAAIuB,qBAAqB,EAAE;QAAA,IAAAuF,qBAAA;QACzB,KAAAA,qBAAA,GAAI9G,IAAI,CAAC+G,gBAAgB,aAArBD,qBAAA,CAAuBF,IAAI,CAAC/K,gBAAgB,CAAC,EAAE;UACjD,IAAIV,YAAY,CAAC6E,IAAI,CAAC,EAAEsG,iBAAiB,GAAG,IAAI;QAClD,CAAC,MAAM;UACLpH,4BAA4B,GAAG,IAAI,CAAC/B,0BAA0B;UAC9D,IAAI,CAACA,0BAA0B,GAAG6C,IAAI;QACxC;MACF;IACF;IAEA,IAAIsG,iBAAiB,EAAE;MACrB,IAAI,CAACxF,SAAK,GAAI,CAAC;MACf,IAAI6F,mBAAmB,EAAE,IAAI,CAACxI,MAAM,CAAC,CAAC;MACtC,IAAI,CAACR,iBAAiB,GAAG,KAAK;MAC9B,IAAI,IAAI,CAAClB,YAAY,GAAGC,mBAAY,CAACiC,yBAAyB,EAAE;QAC9DM,eAAe,GAAG,IAAI,CAACxC,YAAY;QACnC,IAAI,CAACA,YAAY,GAAGC,mBAAY,CAACC,MAAM;MACzC;MACAuC,4BAA4B,GAAG,IAAI,CAAC/B,0BAA0B;MAC9D,IAAI,CAACA,0BAA0B,GAAG,IAAI;IACxC;IAEA,IAAI,CAACO,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAACsJ,qBAAqB,CAAChH,IAAI,EAAEkG,MAAM,CAAC;IAExC,MAAM1F,GAAG,GAAGiF,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGzF,IAAI,CAACQ,GAAG;IAE3E,IAAI,CAAC2C,WAAW,CACd3C,GAAG,EAEHoF,WAAW,CAAC5H,IAAI,CAAC,IAAI,EAAEgC,IAAI,EAAEkG,MAAM,CACrC,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAI,CAACW,sBAAsB,CAACjH,IAAI,EAAEkG,MAAM,CAAC;MACzC,IAAIS,mBAAmB,EAAE;QACvB,IAAI,CAAC/G,MAAM,CAAC,CAAC;QACb,IAAI,CAAC8C,OAAO,CAAC,CAAC;MAChB;MACA,IAAI,CAAC5B,SAAK,GAAI,CAAC;MACf,IAAI,CAAC5D,iBAAiB,GAAGqE,qBAAqB;MAC9C,IAAItC,eAAe,EAAE,IAAI,CAACxC,YAAY,GAAGwC,eAAe;IAC1D,CAAC,MAAM,IAAIsC,qBAAqB,IAAI,CAAC,IAAI,CAACrE,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAC+J,sBAAsB,CAACjH,IAAI,EAAEkG,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACe,sBAAsB,CAACjH,IAAI,EAAEkG,MAAM,EAAEb,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAACvI,YAAY,GAAGoJ,MAAM;IAC1B7J,MAAM,CAACsD,OAAO,GAAG+F,UAAU;IAC3B,IAAI,CAACzI,UAAU,GAAGkJ,QAAQ;IAE1B,IAAIjH,4BAA4B,KAAK2G,SAAS,EAAE;MAC9C,IAAI,CAAC1I,0BAA0B,GAAG+B,4BAA4B;IAChE;IAEA,IAAI,CAACvB,iBAAiB,GAAG,KAAK;EAChC;EAEA6B,mBAAmBA,CAAC0H,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAAClK,UAAU,EAAE,IAAI,CAACmK,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC/J,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAMiK,OAAO,GAAG,IAAI,CAAChL,MAAM,CAACiL,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExL,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAEoL;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAChK,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAMiK,OAAO,GAAG,IAAI,CAAChL,MAAM,CAACmL,qBAAqB;IACjD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExL,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAEoL;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAI,cAAcA,CACZzH,IAKa,EACO;IACpB,MAAMqG,KAAK,GAAGrG,IAAI,CAACqG,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEqB,GAAG,KAAI,IAAI,IAClBrB,KAAK,CAACsB,QAAQ,IAAI,IAAI,IACtB3H,IAAI,CAAC/D,KAAK,KAAKoK,KAAK,CAACsB,QAAQ,EAC7B;MAEA,OAAOtB,KAAK,CAACqB,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCC,SAAmB,EACnB3J,MAAgB,EAChB4J,SAAyC,EACzCC,sBAAgC,EAChCC,WAA6C,EAC7CC,QAAuC,EACvC7C,0BAAmC,EACnC;IACA,IAAI,EAACwC,KAAK,YAALA,KAAK,CAAExJ,MAAM,GAAE;IAEpB,IAAIF,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC9B,MAAM,CAACuG,WAAW,EAAE;MAAA,IAAAuF,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGN,KAAK,CAAC,CAAC,CAAC,CAACrH,GAAG,qBAAZ2H,YAAA,CAAclI,KAAK,CAACyE,IAAI;MAC1C,IAAI0D,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAAC7J,IAAI,CAAC6B,cAAc,CAAC,CAAC,EAAE;QACjEjC,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAMkK,WAA+B,GAAG;MACtCJ,WAAW,EAAEA,WAAW;MACxBK,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,cAAc,GAAGR,SAAS,oBAATA,SAAS,CAAE/J,IAAI,CAAC,IAAI,CAAC;IAE5C,MAAMwK,GAAG,GAAGX,KAAK,CAACxJ,MAAM;IACxB,KAAK,IAAIsE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,GAAG,EAAE7F,CAAC,EAAE,EAAE;MAC5B,MAAM3C,IAAI,GAAG6H,KAAK,CAAClF,CAAC,CAAC;MACrB,IAAI,CAAC3C,IAAI,EAAE;MAEX,IAAI8H,SAAS,EAAE,IAAI,CAACW,aAAa,CAAC9F,CAAC,KAAK,CAAC,EAAE0F,WAAW,CAAC;MAEvD,IAAI,CAAC9I,KAAK,CAACS,IAAI,EAAE6F,SAAS,EAAER,0BAA0B,IAAI,CAAC,CAAC;MAE5D6C,QAAQ,YAARA,QAAQ,CAAGlI,IAAI,EAAE2C,CAAC,CAAC;MAEnB,IAAI4F,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAI5F,CAAC,GAAG6F,GAAG,GAAG,CAAC,EAAED,cAAc,CAAC5F,CAAC,EAAE,KAAK,CAAC,CAAC,KACrC,IAAIqF,sBAAsB,EAAEO,cAAc,CAAC5F,CAAC,EAAE,IAAI,CAAC;MAC1D;MAEA,IAAImF,SAAS,EAAE;QAAA,IAAAY,sBAAA;QACb,IAAI,GAAAA,sBAAA,GAAC1I,IAAI,CAAC+G,gBAAgB,aAArB2B,sBAAA,CAAuBrK,MAAM,GAAE;UAClC,IAAI,CAACX,gBAAgB,GAAG,CAAC;QAC3B;QAEA,IAAIiF,CAAC,GAAG,CAAC,KAAK6F,GAAG,EAAE;UACjB,IAAI,CAAC9F,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAAiG,aAAA;UACL,MAAMC,QAAQ,GAAGf,KAAK,CAAClF,CAAC,GAAG,CAAC,CAAC;UAC7B0F,WAAW,CAACC,iBAAiB,GAAG,EAAAK,aAAA,GAAAC,QAAQ,CAACpI,GAAG,qBAAZmI,aAAA,CAAc1I,KAAK,CAACyE,IAAI,KAAI,CAAC;UAE7D,IAAI,CAAC+D,aAAa,CAAC,IAAI,EAAEJ,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAIlK,MAAM,EAAE,IAAI,CAACyB,MAAM,CAAC,CAAC;EAC3B;EAEAiJ,wBAAwBA,CAAC7I,IAAY,EAAE;IACrC,MAAM7B,MAAM,GAAG6B,IAAI,CAACwG,eAAe,IAAIxG,IAAI,CAACwG,eAAe,CAACnI,MAAM,GAAG,CAAC;IACtE,IAAIF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACoB,KAAK,CAACS,IAAI,CAAC;IAChB,IAAI7B,MAAM,EAAE,IAAI,CAACyB,MAAM,CAAC,CAAC;EAC3B;EAEAkJ,UAAUA,CAAC5C,MAA8C,EAAE;IACzD,MAAMlG,IAAI,GAAGkG,MAAM,CAAC6C,IAAI;IAExB,IAAI/I,IAAI,CAACjE,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACiF,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAACzB,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAiH,sBAAsBA,CAACjH,IAAY,EAAEkG,MAAe,EAAE8C,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAElC;IAAiB,CAAC,GAAG/G,IAAI;IAIhD,IAAIiJ,aAAa,YAAbA,aAAa,CAAE5K,MAAM,EAAE;MACzB,IAAI,CAAC6K,cAAc,IAEjBD,aAAa,EACbjJ,IAAI,EACJkG,MAAM,EACN8C,UACF,CAAC;IACH;IACA,IAAIjC,gBAAgB,YAAhBA,gBAAgB,CAAE1I,MAAM,EAAE;MAC5B,IAAI,CAAC6K,cAAc,IAEjBnC,gBAAgB,EAChB/G,IAAI,EACJkG,MAAM,EACN8C,UACF,CAAC;IACH;EACF;EAEAhC,qBAAqBA,CAAChH,IAAY,EAAEkG,MAAc,EAAE;IAClD,MAAMiD,QAAQ,GAAGnJ,IAAI,CAACwG,eAAe;IACrC,IAAI,EAAC2C,QAAQ,YAARA,QAAQ,CAAE9K,MAAM,GAAE;IACvB,IAAI,CAAC6K,cAAc,IAAuBC,QAAQ,EAAEnJ,IAAI,EAAEkG,MAAM,CAAC;EACnE;EAEAzE,wBAAwBA,CACtB2H,YAAoB,EACpBC,wBAAiC,EACjC;IACA,IAAI,IAAI,CAAC1L,iBAAiB,EAAE;MAAA,IAAA2L,cAAA;MAC1B,IAAI,CAACC,kBAAkB,EAAAD,cAAA,GACrB,IAAI,CAACzL,QAAQ,qBAAbyL,cAAA,CAAetF,YAAY,CACzB,IAAI,CAAClH,YAAY,EACjBsM,YAAY,EACZC,wBACF,CACF,CAAC;IACH;IACA,IAAI,CAAC1L,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEA2L,kBAAkBA,CAACC,SAAiB,EAAE;IACpC,MAAMxJ,IAAI,GAAG,IAAI,CAAClD,YAAY;IAC9B,MAAMqM,QAAQ,GAAGnJ,IAAI,CAACiJ,aAAa;IACnC,IAAI,EAACE,QAAQ,YAARA,QAAQ,CAAE9K,MAAM,GAAE;IAEvB,MAAMoL,QAAQ,GAAG,IAAI,CAACzG,QAAQ,GAAgB,CAAC;IAC/C,MAAM7E,MAAM,GAAG,IAAI,CAACP,oBAAoB;IACxC,MAAM8L,oBAAoB,GAAG,IAAI,CAACrM,gBAAgB,CAACsM,IAAI;IACvD,IAAIxL,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAAC+K,cAAc,IAEjBC,QAAQ,EACRnJ,IAAI,EACJ6F,SAAS,EACTA,SAAS,EACT2D,SACF,CAAC;IACD,IAAIC,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAACrM,gBAAgB,CAACsM,IAAI,EAAE;MACnE,IAAI,CAAC3I,KAAK,CAAC,CAAC;IACd;IACA,IAAI7C,MAAM,EAAE,IAAI,CAACyB,MAAM,CAAC,CAAC;EAC3B;EAEAgK,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAAChM,oBAAoB,GAAG,KAAK;EACnC;EAEAiM,aAAaA,CACXhC,KAAe,EACf1J,MAAgB,EAChBkH,0BAAmC,EACnC4C,WAAiD,EACjD;IACA,IAAI,CAACL,SAAS,CACZC,KAAK,EACL,IAAI,EACJ1J,MAAM,WAANA,MAAM,GAAI,KAAK,EACf0H,SAAS,EACTA,SAAS,EACToC,WAAW,EACXpC,SAAS,EACTR,0BACF,CAAC;EACH;EAEAyE,SAASA,CACPC,KAAe,EACf/B,sBAAgC,EAChCF,SAAmB,EACnB3J,MAAgB,EAChB4J,SAAyC,EACzCG,QAAuC,EACvC;IACA,IAAI,CAACN,SAAS,CACZmC,KAAK,EACLjC,SAAS,EACT3J,MAAM,EACN4J,SAAS,WAATA,SAAS,GAAIiC,cAAc,EAC3BhC,sBAAsB,EACtBnC,SAAS,EACTqC,QACF,CAAC;EACH;EAEA+B,wBAAwBA,CAACC,OAAe,EAAkB;IACxD,IAAI,CAAC,IAAI,CAACrM,QAAQ,EAAE,OAAO,IAAI;IAE/B,MAAMsM,YAAY,GAAG,IAAI,CAACtM,QAAQ,CAACuM,aAAa,CAAC,IAAI,CAACtN,YAAY,EAAEgE,KAAK,IACvE,IAAI,CAACjD,QAAQ,CAACwM,eAAe,CAACvJ,KAAK,EAAEoJ,OAAO,CAC9C,CAAC;IACD,IAAIC,YAAY,IAAI,CAAC,EAAE,OAAO,IAAI;IAClC,OAAO,IAAI,CAACtM,QAAQ,CAACwM,eAAe,CAAC,IAAI,CAACzN,OAAO,CAACuN,YAAY,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAC3E;EAEA1B,aAAaA,CAAC6B,OAAgB,EAAEC,IAAwB,EAAE;IACxD,MAAMlO,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAACuG,WAAW,IAAIvG,MAAM,CAACqD,OAAO,EAAE;IAI1C,IAAIrD,MAAM,CAACsD,OAAO,EAAE;MAClB,IAAI,CAACqB,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAACsJ,OAAO,EAAE;MACZ;IACF;IAEA,MAAMlC,SAAS,GAAGmC,IAAI,CAACjC,iBAAiB;IACxC,MAAMkC,eAAe,GAAG,IAAI,CAAC9M,gBAAgB;IAC7C,IAAI0K,SAAS,GAAG,CAAC,IAAIoC,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGrC,SAAS,GAAGoC,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAAC/H,OAAO,CAAC+H,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAAClM,IAAI,CAAC2C,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACwB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAgI,mBAAmBA,CACjBrD,OAAkB,EAClBmC,SAAiB,EACG;IAGpB,IAAInC,OAAO,CAACsD,MAAM,EAAE;IAEpB,IAAI,IAAI,CAACtN,gBAAgB,CAACuN,GAAG,CAACvD,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAACnK,iBAAiB,IACtBtB,gCAAgC,CAACI,IAAI,CAACqL,OAAO,CAACpL,KAAK,CAAC,EACpD;MACA;IACF;IAEA,IAAIuN,SAAS,IAAI,IAAI,CAAC3L,QAAQ,EAAE;MAC9B,MAAMgN,UAAU,GAAG,IAAI,CAAChN,QAAQ,CAACiN,IAAI,CACnC,IAAI,CAAChO,YAAY,EACjBgE,KAAK,IAAIA,KAAK,CAAC7E,KAAK,KAAKoL,OAAO,CAACpL,KACnC,CAAC;MACD,IAAI4O,UAAU,IAAIA,UAAU,CAAC5K,KAAK,GAAGuJ,SAAS,CAACvJ,KAAK,EAAE;QACpD;MACF;IACF;IAEA,IAAI,CAAC5C,gBAAgB,CAAC0N,GAAG,CAAC1D,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAAChL,MAAM,CAAC2O,kBAAkB,CAAC3D,OAAO,CAACpL,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEAsL,aAAaA,CAACF,OAAkB,EAAE4D,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAAChO,iBAAiB;IAC/C,MAAMiO,cAAc,GAAG9D,OAAO,CAACtL,IAAI,KAAK,cAAc;IAItD,MAAMqP,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAAC/N,iBAAiB;IAEzB,IACEkO,aAAa,IACb,IAAI,CAAC7M,IAAI,CAAC2C,UAAU,CAAC,CAAC,IACtB+J,YAAY,MAAiC,EAC7C;MACA,IAAI,CAACvI,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM2I,YAAY,GAAG,IAAI,CAACjK,WAAW,CAAC,CAAC;IACvC,IACEiK,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,IACzCA,YAAY,OAA8B,EAC1C;MACA,IAAI,CAACrK,KAAK,CAAC,CAAC;IACd;IAEA,IAAIsK,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAG,KAAKjE,OAAO,CAACpL,KAAK,IAAI;MAC5B,IAAI,IAAI,CAACI,MAAM,CAAC8B,MAAM,CAACoN,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMf,MAAM,IAAAe,YAAA,GAAGnE,OAAO,CAAC7G,GAAG,qBAAXgL,YAAA,CAAavL,KAAK,CAAC2E,MAAM;QACxC,IAAI6F,MAAM,EAAE;UACV,MAAMgB,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGjB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEa,GAAG,GAAGA,GAAG,CAACpG,OAAO,CAACuG,YAAY,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,IAAI,CAACpP,MAAM,CAACsD,OAAO,EAAE;UACvB2L,GAAG,GAAGA,GAAG,CAACpG,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,IAAIyG,UAAU,GAAG,IAAI,CAACtP,MAAM,CAACuG,WAAW,GACpC,CAAC,GACD,IAAI,CAACrE,IAAI,CAACwG,gBAAgB,CAAC,CAAC;UAEhC,IAAI,IAAI,CAACP,aAAa,GAAgB,CAAC,IAAI,IAAI,CAACnI,MAAM,CAACuG,WAAW,EAAE;YAClE+I,UAAU,IAAI,IAAI,CAACpH,UAAU,CAAC,CAAC;UACjC;UAEA+G,GAAG,GAAGA,GAAG,CAACpG,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAACC,MAAM,CAACwG,UAAU,CAAC,EAAE,CAAC;QAC9D;MACF;IACF,CAAC,MAAM,IAAI,CAACT,gBAAgB,EAAE;MAC5BI,GAAG,GAAG,KAAKjE,OAAO,CAACpL,KAAK,EAAE;IAC5B,CAAC,MAAM;MAILqP,GAAG,GAAG,KAAKjE,OAAO,CAACpL,KAAK,IAAI;IAC9B;IAGA,IAAI,IAAI,CAACwB,YAAY,EAAE,IAAI,CAACwD,MAAM,CAAC,CAAC;IAEpC,IAAI,IAAI,CAACpD,QAAQ,EAAE;MACjB,MAAM;QAAEK,8BAA8B;QAAED;MAA8B,CAAC,GACrE,IAAI;MACN,IAAI,CAACC,8BAA8B,GAAG,CAAC,CAAC;MACxC,IAAI,CAACD,6BAA6B,GAAG,CAAC,CAAC;MACvC,IAAI,CAACqF,MAAM,CAAC,OAAO,EAAE+D,OAAO,CAAC7G,GAAG,CAAC;MACjC,IAAI,CAACoB,OAAO,CAAC0J,GAAG,EAAEH,cAAc,CAAC;MACjC,IAAI,CAAClN,6BAA6B,GAAGA,6BAA6B;MAClE,IAAI,CAACC,8BAA8B,GAAGA,8BAA8B;IACtE,CAAC,MAAM;MACL,IAAI,CAACoF,MAAM,CAAC,OAAO,EAAE+D,OAAO,CAAC7G,GAAG,CAAC;MACjC,IAAI,CAACoB,OAAO,CAAC0J,GAAG,EAAEH,cAAc,CAAC;IACnC;IAEA,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAACxI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI0I,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAACvI,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAwG,cAAcA,CACZnN,IAAkB,EAClBoN,QAA8B,EAC9BnJ,IAAY,EACZkG,MAAe,EACf8C,UAAkB,GAAG,CAAC,EACtBQ,SAAiB,EACjB;IACA,MAAMoC,OAAO,GAAG5L,IAAI,CAACQ,GAAG;IACxB,MAAMgI,GAAG,GAAGW,QAAQ,CAAC9K,MAAM;IAC3B,IAAIwN,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAAC3L,KAAK,CAACyE,IAAI,GAAG,CAAC;IACrD,MAAMqH,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAAC1L,GAAG,CAACwE,IAAI,GAAG,CAAC;IACjD,IAAIsH,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAM/J,YAAY,GAAG,IAAI,CAAChF,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAACwF,OAAO,CAAC1E,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,GAAG,EAAE7F,CAAC,EAAE,EAAE;MAC5B,MAAM0E,OAAO,GAAG8B,QAAQ,CAACxG,CAAC,CAAC;MAE3B,MAAMuJ,WAAW,GAAG,IAAI,CAACxB,mBAAmB,CAACrD,OAAO,EAAEmC,SAAS,CAAC;MAChE,IAAI0C,WAAW,MAA6B,EAAE;QAC5CL,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIxE,OAAO,CAAC7G,GAAG,IAAI0L,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAG9E,OAAO,CAAC7G,GAAG,CAACP,KAAK,CAACyE,IAAI;QAC/C,MAAM0H,cAAc,GAAG/E,OAAO,CAAC7G,GAAG,CAACN,GAAG,CAACwE,IAAI;QAC3C,IAAI3I,IAAI,MAAyB,EAAE;UACjC,IAAI0O,MAAM,GAAG,CAAC;UACd,IAAI9H,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAACpE,IAAI,CAAC2C,UAAU,CAAC,CAAC,KACrBmG,OAAO,CAACtL,IAAI,KAAK,aAAa,IAC7BoQ,gBAAgB,KAAKC,cAAc,CAAC,EACtC;cACA3B,MAAM,GAAGwB,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACLxB,MAAM,GAAG0B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBlK,YAAY,CAACuI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAK6F,GAAG,EAAE;YACjBtG,YAAY,CACVmK,IAAI,CAACC,GAAG,CAACR,aAAa,GAAGE,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGF,aAAa;UAC1B;QACF,CAAC,MAAM,IAAI/P,IAAI,MAAuB,EAAE;UACtC,MAAM0O,MAAM,GACV0B,gBAAgB,IAAIxJ,CAAC,KAAK,CAAC,GAAGmJ,aAAa,GAAGE,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBlK,YAAY,CAACuI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAK6F,GAAG,EAAE;YACjBtG,YAAY,CAACmK,IAAI,CAACE,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGC,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGD,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMtB,MAAM,GACV0B,gBAAgB,IAAIxJ,CAAC,KAAK,CAAC,GAAGoJ,WAAW,GAAG/C,UAAU,GAAGgD,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBlK,YAAY,CAACuI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLwE,MAAM,GAAG,KAAK;QACd,IAAIK,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAI1D,GAAG,KAAK,CAAC,EAAE;UACb,MAAMgE,UAAU,GAAGnF,OAAO,CAAC7G,GAAG,GAC1B6G,OAAO,CAAC7G,GAAG,CAACP,KAAK,CAACyE,IAAI,KAAK2C,OAAO,CAAC7G,GAAG,CAACN,GAAG,CAACwE,IAAI,GAC/C,CAAC/I,WAAW,CAACK,IAAI,CAACqL,OAAO,CAACpL,KAAK,CAAC;UAEpC,MAAMwQ,iBAAiB,GACrBD,UAAU,IACV,CAACnR,WAAW,CAAC2E,IAAI,CAAC,IAClB,CAAC1E,WAAW,CAAC4K,MAAM,CAAC,IACpB,CAAC3K,iBAAiB,CAAC2K,MAAM,CAAC,IAC1B,CAAC1K,cAAc,CAACwE,IAAI,CAAC;UAEvB,IAAIjE,IAAI,MAAyB,EAAE;YACjC,IAAI,CAACwL,aAAa,CAChBF,OAAO,EACNoF,iBAAiB,IAAIzM,IAAI,CAACjE,IAAI,KAAK,kBAAkB,IACnDyQ,UAAU,IAAIpR,UAAU,CAAC8K,MAAM,EAAE;cAAE6C,IAAI,EAAE/I;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAIyM,iBAAiB,IAAI1Q,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAACwL,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACLtL,IAAI,MAAuB,IAC3B,EAAEiE,IAAI,CAACjE,IAAI,KAAK,kBAAkB,IAAIiE,IAAI,CAAC0M,UAAU,CAACrO,MAAM,GAAG,CAAC,CAAC,IACjE2B,IAAI,CAACjE,IAAI,KAAK,WAAW,IACzBiE,IAAI,CAACjE,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAACwL,aAAa,CAChBF,OAAO,EACP1E,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAK6F,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACjB,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAItL,IAAI,MAA0B,IAAI8P,MAAM,IAAIG,QAAQ,EAAE;MACxD,IAAI,CAACtO,gBAAgB,GAAGsO,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAACzQ,OAAO,CAAC0Q,SAAS,EAAE5R,kBAAkB,CAAC;AAEjB;EACjC,IAAA6R,mCAAuB,EAAC3Q,OAAO,CAAC;AAClC;AAAC,IAAA4Q,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKc9Q,OAAO;AAEtB,SAAS6N,cAAcA,CAAgB7H,eAAuB,EAAE+K,IAAa,EAAE;EAC7E,IAAI,CAACpM,KAAK,CAAC,GAAG,EAAE,KAAK,EAAEqB,eAAe,CAAC;EACvC,IAAI,CAAC+K,IAAI,EAAE,IAAI,CAAClM,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}