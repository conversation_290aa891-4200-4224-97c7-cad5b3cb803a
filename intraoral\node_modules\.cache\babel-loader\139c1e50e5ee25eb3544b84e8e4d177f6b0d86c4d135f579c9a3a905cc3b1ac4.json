{"ast": null, "code": "var _jsxFileName = \"D:\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [currentImage, setCurrentImage] = useState(null);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': '🦷',\n    'early-decay': '⚠️',\n    'healthy tooth': '✅'\n  };\n  useEffect(() => {\n    // Simulate receiving detection results\n    if (isAnalyzing) {\n      const mockResults = generateMockResults();\n      setTimeout(() => {\n        handleDetectionResults(mockResults);\n      }, 2000);\n    }\n  }, [isAnalyzing]);\n  const generateMockResults = () => {\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\n    const numDetections = Math.floor(Math.random() * 3) + 1;\n    const results = [];\n    for (let i = 0; i < numDetections; i++) {\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\n      results.push({\n        class: randomClass,\n        confidence: Math.random() * 0.4 + 0.6,\n        // 60-100% confidence\n        bbox: {\n          x: Math.random() * 0.8,\n          y: Math.random() * 0.8,\n          width: Math.random() * 0.3 + 0.1,\n          height: Math.random() * 0.3 + 0.1\n        }\n      });\n    }\n    return results;\n  };\n  const handleDetectionResults = results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  };\n  const generateAnnotatedImage = results => {\n    // In a real implementation, this would draw bounding boxes on the image\n    // For now, we'll create a simple visualization\n    const canvas = document.createElement('canvas');\n    canvas.width = 640;\n    canvas.height = 480;\n    const ctx = canvas.getContext('2d');\n\n    // Draw background\n    ctx.fillStyle = '#f5f5f5';\n    ctx.fillRect(0, 0, 640, 480);\n\n    // Draw detection boxes\n    results.forEach((result, index) => {\n      const {\n        bbox\n      } = result;\n      const x = bbox.x * 640;\n      const y = bbox.y * 480;\n      const width = bbox.width * 640;\n      const height = bbox.height * 480;\n\n      // Draw bounding box\n      ctx.strokeStyle = classColors[result.class];\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\n      const labelWidth = ctx.measureText(label).width + 20;\n      const labelHeight = 25;\n      ctx.fillStyle = classColors[result.class];\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.font = 'bold 14px Arial';\n      ctx.fillText(label, x + 10, y - 8);\n    });\n    setAnnotatedImage(canvas.toDataURL());\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"yolo-detection\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detection-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"YOLOv8 Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"model-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"model-badge\",\n          children: \"Model: best.pt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"classes-badge\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"analysis-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Analyzing image with YOLOv8...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), annotatedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"annotated-image-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Annotated Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"annotated-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detection-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Detection Statistics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon\",\n            children: classIcons[className]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: className\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-count\",\n              children: count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, className, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"detection-history\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Recent Detections\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"history-time\",\n              children: new Date(detection.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"history-count\",\n              children: [detection.results.length, \" detection(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detection-results\",\n            children: detection.results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detection-result\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-class\",\n                children: [classIcons[result.class], \" \", result.class]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"result-confidence\",\n                style: {\n                  color: getConfidenceColor(result.confidence)\n                },\n                children: [(result.confidence * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, detection.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"o+R6tTRtprJHViS6iX3O0kdphXI=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "_s", "detectionHistory", "setDetectionHistory", "currentImage", "setCurrentImage", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "classColors", "classIcons", "mockResults", "generateMockResults", "setTimeout", "handleDetectionResults", "classes", "numDetections", "Math", "floor", "random", "results", "i", "randomClass", "length", "push", "class", "confidence", "bbox", "x", "y", "width", "height", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "for<PERSON>ach", "result", "generateAnnotatedImage", "canvas", "document", "createElement", "ctx", "getContext", "fillStyle", "fillRect", "index", "strokeStyle", "lineWidth", "strokeRect", "label", "toFixed", "labelWidth", "measureText", "labelHeight", "font", "fillText", "toDataURL", "getConfidenceColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "Object", "entries", "map", "count", "detection", "toLocaleTimeString", "style", "color", "_c", "$RefreshReg$"], "sources": ["D:/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [currentImage, setCurrentImage] = useState(null);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ff6b6b',\r\n    'early-decay': '#ffd43b',\r\n    'healthy tooth': '#51cf66'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': '🦷',\r\n    'early-decay': '⚠️',\r\n    'healthy tooth': '✅'\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Simulate receiving detection results\r\n    if (isAnalyzing) {\r\n      const mockResults = generateMockResults();\r\n      setTimeout(() => {\r\n        handleDetectionResults(mockResults);\r\n      }, 2000);\r\n    }\r\n  }, [isAnalyzing]);\r\n\r\n  const generateMockResults = () => {\r\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\r\n    const numDetections = Math.floor(Math.random() * 3) + 1;\r\n    const results = [];\r\n\r\n    for (let i = 0; i < numDetections; i++) {\r\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\r\n      results.push({\r\n        class: randomClass,\r\n        confidence: Math.random() * 0.4 + 0.6, // 60-100% confidence\r\n        bbox: {\r\n          x: Math.random() * 0.8,\r\n          y: Math.random() * 0.8,\r\n          width: Math.random() * 0.3 + 0.1,\r\n          height: Math.random() * 0.3 + 0.1\r\n        }\r\n      });\r\n    }\r\n\r\n    return results;\r\n  };\r\n\r\n  const handleDetectionResults = (results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n    \r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  };\r\n\r\n  const generateAnnotatedImage = (results) => {\r\n    // In a real implementation, this would draw bounding boxes on the image\r\n    // For now, we'll create a simple visualization\r\n    const canvas = document.createElement('canvas');\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n    const ctx = canvas.getContext('2d');\r\n\r\n    // Draw background\r\n    ctx.fillStyle = '#f5f5f5';\r\n    ctx.fillRect(0, 0, 640, 480);\r\n\r\n    // Draw detection boxes\r\n    results.forEach((result, index) => {\r\n      const { bbox } = result;\r\n      const x = bbox.x * 640;\r\n      const y = bbox.y * 480;\r\n      const width = bbox.width * 640;\r\n      const height = bbox.height * 480;\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = classColors[result.class];\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} ${(result.confidence * 100).toFixed(1)}%`;\r\n      const labelWidth = ctx.measureText(label).width + 20;\r\n      const labelHeight = 25;\r\n\r\n      ctx.fillStyle = classColors[result.class];\r\n      ctx.fillRect(x, y - labelHeight, labelWidth, labelHeight);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.font = 'bold 14px Arial';\r\n      ctx.fillText(label, x + 10, y - 8);\r\n    });\r\n\r\n    setAnnotatedImage(canvas.toDataURL());\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#51cf66';\r\n    if (confidence >= 0.6) return '#ffd43b';\r\n    return '#ff6b6b';\r\n  };\r\n\r\n  return (\r\n    <div className=\"yolo-detection\">\r\n      <div className=\"detection-header\">\r\n        <h3>YOLOv8 Analysis</h3>\r\n        <div className=\"model-info\">\r\n          <span className=\"model-badge\">Model: best.pt</span>\r\n          <span className=\"classes-badge\">Classes: 3</span>\r\n        </div>\r\n      </div>\r\n\r\n      {isAnalyzing && (\r\n        <div className=\"analysis-status\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <p>Analyzing image with YOLOv8...</p>\r\n        </div>\r\n      )}\r\n\r\n      {annotatedImage && (\r\n        <div className=\"annotated-image-container\">\r\n          <h4>Annotated Results</h4>\r\n          <div className=\"annotated-image\">\r\n            <img src={annotatedImage} alt=\"Annotated detection\" />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"detection-stats\">\r\n        <h4>Detection Statistics</h4>\r\n        <div className=\"stats-grid\">\r\n          {Object.entries(detectionStats).map(([className, count]) => (\r\n            <div key={className} className=\"stat-item\">\r\n              <div className=\"stat-icon\">{classIcons[className]}</div>\r\n              <div className=\"stat-info\">\r\n                <span className=\"stat-label\">{className}</span>\r\n                <span className=\"stat-count\">{count}</span>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"detection-history\">\r\n        <h4>Recent Detections</h4>\r\n        <div className=\"history-list\">\r\n          {detectionHistory.map((detection) => (\r\n            <div key={detection.id} className=\"history-item\">\r\n              <div className=\"history-header\">\r\n                <span className=\"history-time\">\r\n                  {new Date(detection.timestamp).toLocaleTimeString()}\r\n                </span>\r\n                <span className=\"history-count\">\r\n                  {detection.results.length} detection(s)\r\n                </span>\r\n              </div>\r\n              \r\n              <div className=\"detection-results\">\r\n                {detection.results.map((result, index) => (\r\n                  <div key={index} className=\"detection-result\">\r\n                    <span className=\"result-class\">\r\n                      {classIcons[result.class]} {result.class}\r\n                    </span>\r\n                    <span \r\n                      className=\"result-confidence\"\r\n                      style={{ color: getConfidenceColor(result.confidence) }}\r\n                    >\r\n                      {(result.confidence * 100).toFixed(1)}%\r\n                    </span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE;EACnB,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACd;IACA,IAAIM,WAAW,EAAE;MACf,MAAMa,WAAW,GAAGC,mBAAmB,CAAC,CAAC;MACzCC,UAAU,CAAC,MAAM;QACfC,sBAAsB,CAACH,WAAW,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EAEjB,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMG,OAAO,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,EAAEK,CAAC,EAAE,EAAE;MACtC,MAAMC,WAAW,GAAGP,OAAO,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,OAAO,CAACQ,MAAM,CAAC,CAAC;MACvEH,OAAO,CAACI,IAAI,CAAC;QACXC,KAAK,EAAEH,WAAW;QAClBI,UAAU,EAAET,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAAE;QACvCQ,IAAI,EAAE;UACJC,CAAC,EAAEX,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBU,CAAC,EAAEZ,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBW,KAAK,EAAEb,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCY,MAAM,EAAEd,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOC,OAAO;EAChB,CAAC;EAED,MAAMN,sBAAsB,GAAIM,OAAO,IAAK;IAC1C,MAAMY,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCjB,OAAO,EAAEA,OAAO;MAChBkB,KAAK,EAAEpC,YAAY,IAAI;IACzB,CAAC;IAEDD,mBAAmB,CAACsC,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAGnC;IAAe,CAAC;IACtCc,OAAO,CAACsB,OAAO,CAACC,MAAM,IAAI;MACxBF,QAAQ,CAACE,MAAM,CAAClB,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFlB,iBAAiB,CAACkC,QAAQ,CAAC;;IAE3B;IACAG,sBAAsB,CAACxB,OAAO,CAAC;;IAE/B;IACAvB,SAAS,CAACuB,OAAO,CAAC;EACpB,CAAC;EAED,MAAMwB,sBAAsB,GAAIxB,OAAO,IAAK;IAC1C;IACA;IACA,MAAMyB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACf,KAAK,GAAG,GAAG;IAClBe,MAAM,CAACd,MAAM,GAAG,GAAG;IACnB,MAAMiB,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAD,GAAG,CAACE,SAAS,GAAG,SAAS;IACzBF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;;IAE5B;IACA/B,OAAO,CAACsB,OAAO,CAAC,CAACC,MAAM,EAAES,KAAK,KAAK;MACjC,MAAM;QAAEzB;MAAK,CAAC,GAAGgB,MAAM;MACvB,MAAMf,CAAC,GAAGD,IAAI,CAACC,CAAC,GAAG,GAAG;MACtB,MAAMC,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG,GAAG;MACtB,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK,GAAG,GAAG;MAC9B,MAAMC,MAAM,GAAGJ,IAAI,CAACI,MAAM,GAAG,GAAG;;MAEhC;MACAiB,GAAG,CAACK,WAAW,GAAG5C,WAAW,CAACkC,MAAM,CAAClB,KAAK,CAAC;MAC3CuB,GAAG,CAACM,SAAS,GAAG,CAAC;MACjBN,GAAG,CAACO,UAAU,CAAC3B,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMyB,KAAK,GAAG,GAAGb,MAAM,CAAClB,KAAK,IAAI,CAACkB,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAE+B,OAAO,CAAC,CAAC,CAAC,GAAG;MACxE,MAAMC,UAAU,GAAGV,GAAG,CAACW,WAAW,CAACH,KAAK,CAAC,CAAC1B,KAAK,GAAG,EAAE;MACpD,MAAM8B,WAAW,GAAG,EAAE;MAEtBZ,GAAG,CAACE,SAAS,GAAGzC,WAAW,CAACkC,MAAM,CAAClB,KAAK,CAAC;MACzCuB,GAAG,CAACG,QAAQ,CAACvB,CAAC,EAAEC,CAAC,GAAG+B,WAAW,EAAEF,UAAU,EAAEE,WAAW,CAAC;;MAEzD;MACAZ,GAAG,CAACE,SAAS,GAAG,OAAO;MACvBF,GAAG,CAACa,IAAI,GAAG,iBAAiB;MAC5Bb,GAAG,CAACc,QAAQ,CAACN,KAAK,EAAE5B,CAAC,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC,CAAC;IAEFxB,iBAAiB,CAACwC,MAAM,CAACkB,SAAS,CAAC,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,kBAAkB,GAAItC,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,oBACE/B,OAAA;IAAKsE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BvE,OAAA;MAAKsE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvE,OAAA;QAAAuE,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxB3E,OAAA;QAAKsE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBvE,OAAA;UAAMsE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnD3E,OAAA;UAAMsE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxE,WAAW,iBACVH,OAAA;MAAKsE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvE,OAAA;QAAKsE,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC3E,OAAA;QAAAuE,QAAA,EAAG;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACN,EAEAlE,cAAc,iBACbT,OAAA;MAAKsE,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCvE,OAAA;QAAAuE,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B3E,OAAA;QAAKsE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvE,OAAA;UAAK4E,GAAG,EAAEnE,cAAe;UAACoE,GAAG,EAAC;QAAqB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3E,OAAA;MAAKsE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvE,OAAA;QAAAuE,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B3E,OAAA;QAAKsE,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBO,MAAM,CAACC,OAAO,CAACpE,cAAc,CAAC,CAACqE,GAAG,CAAC,CAAC,CAACV,SAAS,EAAEW,KAAK,CAAC,kBACrDjF,OAAA;UAAqBsE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxCvE,OAAA;YAAKsE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAExD,UAAU,CAACuD,SAAS;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD3E,OAAA;YAAKsE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvE,OAAA;cAAMsE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAED;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C3E,OAAA;cAAMsE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEU;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA,GALEL,SAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3E,OAAA;MAAKsE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvE,OAAA;QAAAuE,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1B3E,OAAA;QAAKsE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BlE,gBAAgB,CAAC2E,GAAG,CAAEE,SAAS,iBAC9BlF,OAAA;UAAwBsE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC9CvE,OAAA;YAAKsE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvE,OAAA;cAAMsE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC3B,IAAIhC,IAAI,CAAC2C,SAAS,CAACzC,SAAS,CAAC,CAAC0C,kBAAkB,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACP3E,OAAA;cAAMsE,SAAS,EAAC,eAAe;cAAAC,QAAA,GAC5BW,SAAS,CAACzD,OAAO,CAACG,MAAM,EAAC,eAC5B;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN3E,OAAA;YAAKsE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BW,SAAS,CAACzD,OAAO,CAACuD,GAAG,CAAC,CAAChC,MAAM,EAAES,KAAK,kBACnCzD,OAAA;cAAiBsE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC3CvE,OAAA;gBAAMsE,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAC3BxD,UAAU,CAACiC,MAAM,CAAClB,KAAK,CAAC,EAAC,GAAC,EAACkB,MAAM,CAAClB,KAAK;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACP3E,OAAA;gBACEsE,SAAS,EAAC,mBAAmB;gBAC7Bc,KAAK,EAAE;kBAAEC,KAAK,EAAEhB,kBAAkB,CAACrB,MAAM,CAACjB,UAAU;gBAAE,CAAE;gBAAAwC,QAAA,GAEvD,CAACvB,MAAM,CAACjB,UAAU,GAAG,GAAG,EAAE+B,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GATClB,KAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAxBEO,SAAS,CAAC5C,EAAE;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBjB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CA1MIH,aAAa;AAAAqF,EAAA,GAAbrF,aAAa;AA4MnB,eAAeA,aAAa;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}