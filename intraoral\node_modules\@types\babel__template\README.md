# Installation
> `npm install --save @types/babel__template`

# Summary
This package contains type definitions for @babel/template (https://github.com/babel/babel/tree/master/packages/babel-template).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template.

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:04 GMT
 * Dependencies: [@babel/parser](https://npmjs.com/package/@babel/parser), [@babel/types](https://npmjs.com/package/@babel/types)

# Credits
These definitions were written by [<PERSON>](https://github.com/yortus), [<PERSON>](https://github.com/marvin<PERSON>meister), [<PERSON>](https://github.com/mgroenhoff), and [ExE Boss](https://github.com/ExE-Boss).
