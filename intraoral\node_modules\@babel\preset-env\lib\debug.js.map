{"version": 3, "names": ["_helperCompilationTargets", "require", "compatData", "logPlugin", "item", "targetVersions", "list", "filteredList", "getInclusionReasons", "support", "startsWith", "proposalName", "slice", "hasOwnProperty", "call", "console", "log", "formattedTargets", "first", "target", "Object", "keys", "exports"], "sources": ["../src/debug.ts"], "sourcesContent": ["import {\n  getInclusionReasons,\n  type Targets,\n  type Target,\n} from \"@babel/helper-compilation-targets\";\nimport compatData from \"@babel/compat-data/plugins\" with { type: \"json\" };\n\n// Outputs a message that shows which target(s) caused an item to be included:\n// transform-foo { \"edge\":\"13\", \"firefox\":\"49\", \"ie\":\"10\" }\nexport const logPlugin = (\n  item: string,\n  targetVersions: Targets,\n  list: { [key: string]: Targets },\n) => {\n  const filteredList = getInclusionReasons(item, targetVersions, list);\n\n  const support = list[item];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // It's needed to keep outputting proposal- in the debug log.\n    if (item.startsWith(\"transform-\")) {\n      const proposalName = `proposal-${item.slice(10)}`;\n      if (\n        proposalName === \"proposal-dynamic-import\" ||\n        Object.hasOwn(compatData, proposalName)\n      ) {\n        item = proposalName;\n      }\n    }\n  }\n\n  if (!support) {\n    console.log(`  ${item}`);\n    return;\n  }\n\n  let formattedTargets = `{`;\n  let first = true;\n  for (const target of Object.keys(filteredList) as Target[]) {\n    if (!first) formattedTargets += `,`;\n    first = false;\n    formattedTargets += ` ${target}`;\n    if (support[target]) formattedTargets += ` < ${support[target]}`;\n  }\n  formattedTargets += ` }`;\n\n  console.log(`  ${item} ${formattedTargets}`);\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AAI2C,MACpCC,UAAU,GAAAD,OAAA,CAAM,4BAA4B;AAI5C,MAAME,SAAS,GAAGA,CACvBC,IAAY,EACZC,cAAuB,EACvBC,IAAgC,KAC7B;EACH,MAAMC,YAAY,GAAG,IAAAC,6CAAmB,EAACJ,IAAI,EAAEC,cAAc,EAAEC,IAAI,CAAC;EAEpE,MAAMG,OAAO,GAAGH,IAAI,CAACF,IAAI,CAAC;EAES;IAEjC,IAAIA,IAAI,CAACM,UAAU,CAAC,YAAY,CAAC,EAAE;MACjC,MAAMC,YAAY,GAAG,YAAYP,IAAI,CAACQ,KAAK,CAAC,EAAE,CAAC,EAAE;MACjD,IACED,YAAY,KAAK,yBAAyB,IAC1CE,cAAA,CAAAC,IAAA,CAAcZ,UAAU,EAAES,YAAY,CAAC,EACvC;QACAP,IAAI,GAAGO,YAAY;MACrB;IACF;EACF;EAEA,IAAI,CAACF,OAAO,EAAE;IACZM,OAAO,CAACC,GAAG,CAAC,KAAKZ,IAAI,EAAE,CAAC;IACxB;EACF;EAEA,IAAIa,gBAAgB,GAAG,GAAG;EAC1B,IAAIC,KAAK,GAAG,IAAI;EAChB,KAAK,MAAMC,MAAM,IAAIC,MAAM,CAACC,IAAI,CAACd,YAAY,CAAC,EAAc;IAC1D,IAAI,CAACW,KAAK,EAAED,gBAAgB,IAAI,GAAG;IACnCC,KAAK,GAAG,KAAK;IACbD,gBAAgB,IAAI,IAAIE,MAAM,EAAE;IAChC,IAAIV,OAAO,CAACU,MAAM,CAAC,EAAEF,gBAAgB,IAAI,MAAMR,OAAO,CAACU,MAAM,CAAC,EAAE;EAClE;EACAF,gBAAgB,IAAI,IAAI;EAExBF,OAAO,CAACC,GAAG,CAAC,KAAKZ,IAAI,IAAIa,gBAAgB,EAAE,CAAC;AAC9C,CAAC;AAACK,OAAA,CAAAnB,SAAA,GAAAA,SAAA", "ignoreList": []}