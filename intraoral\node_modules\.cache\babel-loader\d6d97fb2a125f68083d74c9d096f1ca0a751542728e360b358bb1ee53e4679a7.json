{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': '🦷',\n    'early-decay': '⚠️',\n    'healthy tooth': '✅'\n  };\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\n    results.forEach((result, index) => {\n      // Generate random box coordinates for demo\n      const x = Math.random() * (canvasWidth - 100);\n      const y = Math.random() * (canvasHeight - 100);\n      const width = 80 + Math.random() * 40;\n      const height = 80 + Math.random() * 40;\n\n      // Set box color based on class\n      const color = classColors[result.class] || '#0077B6';\n\n      // Draw bounding box\n      ctx.strokeStyle = color;\n      ctx.lineWidth = 3;\n      ctx.strokeRect(x, y, width, height);\n\n      // Draw label background\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\n      ctx.font = '14px Arial';\n      const textWidth = ctx.measureText(label).width;\n      ctx.fillStyle = color;\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\n\n      // Draw label text\n      ctx.fillStyle = 'white';\n      ctx.fillText(label, x + 5, y - 10);\n    });\n  }, []);\n  const generateAnnotatedImage = useCallback(results => {\n    // Create a canvas to draw the annotated image\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = 640;\n    canvas.height = 480;\n\n    // If we have a current image, use it as background\n    if (currentImage) {\n      const img = new Image();\n      img.onload = () => {\n        // Draw the original image\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n\n        // Draw detection boxes on top\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n\n        // Set the annotated image\n        setAnnotatedImage(canvas.toDataURL());\n      };\n      img.src = currentImage;\n    } else {\n      // Fallback to placeholder if no image\n      ctx.fillStyle = '#f5f5f5';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Draw detection boxes\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\n      setAnnotatedImage(canvas.toDataURL());\n    }\n  }, [currentImage, drawDetectionBoxes]);\n  const handleDetectionResults = useCallback(results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Generate annotated image\n    generateAnnotatedImage(results);\n\n    // Notify parent component\n    onResults(results);\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\n  useEffect(() => {\n    // Simulate receiving detection results\n    if (isAnalyzing) {\n      const mockResults = generateMockResults();\n      setTimeout(() => {\n        handleDetectionResults(mockResults);\n      }, 2000);\n    }\n  }, [isAnalyzing, handleDetectionResults]);\n  const generateMockResults = () => {\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\n    const numDetections = Math.floor(Math.random() * 3) + 1;\n    const results = [];\n    for (let i = 0; i < numDetections; i++) {\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\n      results.push({\n        class: randomClass,\n        confidence: Math.random() * 0.4 + 0.6,\n        // 60-100% confidence\n        bbox: {\n          x: Math.random() * 0.8,\n          y: Math.random() * 0.8,\n          width: Math.random() * 0.3 + 0.1,\n          height: Math.random() * 0.3 + 0.1\n        }\n      });\n    }\n    return results;\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaBrain, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), \"Model: best.pt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), isAnalyzing && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Analyzing image with YOLOv8...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this), annotatedImage && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        scale: 0.95\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Annotated Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: annotatedImage,\n          alt: \"Annotated detection\",\n          className: \"w-full h-auto max-h-64 object-contain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Detection Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mr-3\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaClock, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Detections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), detectionHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: new Date(detection.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n              children: [detection.results.length, \" detection(s)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n            children: detection.results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-2 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center text-sm font-medium text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: classIcons[result.class]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 25\n                }, this), result.class.replace('-', ' ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold\",\n                style: {\n                  color: getConfidenceColor(result.confidence)\n                },\n                children: [(result.confidence * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)]\n        }, detection.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No detections yet. Capture images to start AI analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"WyqNpihpYcSki+hl481bx8dBLOk=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "FaBrain", "FaChartBar", "FaClock", "FaImage", "FaRobot", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "classColors", "classIcons", "console", "log", "drawDetectionBoxes", "ctx", "results", "canvasWidth", "canvasHeight", "for<PERSON>ach", "result", "index", "x", "Math", "random", "y", "width", "height", "color", "class", "strokeStyle", "lineWidth", "strokeRect", "label", "confidence", "toFixed", "font", "textWidth", "measureText", "fillStyle", "fillRect", "fillText", "generateAnnotatedImage", "canvas", "document", "createElement", "getContext", "img", "Image", "onload", "drawImage", "toDataURL", "src", "handleDetectionResults", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "prev", "slice", "newStats", "mockResults", "generateMockResults", "setTimeout", "classes", "numDetections", "floor", "i", "randomClass", "length", "push", "bbox", "getConfidenceColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "scale", "alt", "Object", "entries", "map", "count", "whileHover", "replace", "detection", "toLocaleTimeString", "style", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot } from 'react-icons/fa';\r\nimport './YOLODetection.css';\r\n\r\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage }) => {\r\n  const [detectionHistory, setDetectionHistory] = useState([]);\r\n  const [annotatedImage, setAnnotatedImage] = useState(null);\r\n  const [detectionStats, setDetectionStats] = useState({\r\n    decaycavity: 0,\r\n    'early-decay': 0,\r\n    'healthy tooth': 0\r\n  });\r\n\r\n  const classColors = {\r\n    'decaycavity': '#ff6b6b',\r\n    'early-decay': '#ffd43b',\r\n    'healthy tooth': '#51cf66'\r\n  };\r\n\r\n  const classIcons = {\r\n    'decaycavity': '🦷',\r\n    'early-decay': '⚠️',\r\n    'healthy tooth': '✅'\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Update current image when prop changes\r\n    if (currentImage) {\r\n      console.log('📸 Received new captured image for analysis');\r\n    }\r\n  }, [currentImage]);\r\n\r\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\r\n    results.forEach((result, index) => {\r\n      // Generate random box coordinates for demo\r\n      const x = Math.random() * (canvasWidth - 100);\r\n      const y = Math.random() * (canvasHeight - 100);\r\n      const width = 80 + Math.random() * 40;\r\n      const height = 80 + Math.random() * 40;\r\n\r\n      // Set box color based on class\r\n      const color = classColors[result.class] || '#0077B6';\r\n\r\n      // Draw bounding box\r\n      ctx.strokeStyle = color;\r\n      ctx.lineWidth = 3;\r\n      ctx.strokeRect(x, y, width, height);\r\n\r\n      // Draw label background\r\n      const label = `${result.class} (${(result.confidence * 100).toFixed(1)}%)`;\r\n      ctx.font = '14px Arial';\r\n      const textWidth = ctx.measureText(label).width;\r\n\r\n      ctx.fillStyle = color;\r\n      ctx.fillRect(x, y - 25, textWidth + 10, 20);\r\n\r\n      // Draw label text\r\n      ctx.fillStyle = 'white';\r\n      ctx.fillText(label, x + 5, y - 10);\r\n    });\r\n  }, []);\r\n\r\n  const generateAnnotatedImage = useCallback((results) => {\r\n    // Create a canvas to draw the annotated image\r\n    const canvas = document.createElement('canvas');\r\n    const ctx = canvas.getContext('2d');\r\n\r\n    // Set canvas size\r\n    canvas.width = 640;\r\n    canvas.height = 480;\r\n\r\n    // If we have a current image, use it as background\r\n    if (currentImage) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        // Draw the original image\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n\r\n        // Draw detection boxes on top\r\n        drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n        // Set the annotated image\r\n        setAnnotatedImage(canvas.toDataURL());\r\n      };\r\n      img.src = currentImage;\r\n    } else {\r\n      // Fallback to placeholder if no image\r\n      ctx.fillStyle = '#f5f5f5';\r\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n\r\n      // Draw detection boxes\r\n      drawDetectionBoxes(ctx, results, canvas.width, canvas.height);\r\n\r\n      setAnnotatedImage(canvas.toDataURL());\r\n    }\r\n  }, [currentImage, drawDetectionBoxes]);\r\n\r\n  const handleDetectionResults = useCallback((results) => {\r\n    const newDetection = {\r\n      id: Date.now(),\r\n      timestamp: new Date().toISOString(),\r\n      results: results,\r\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+'\r\n    };\r\n\r\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\r\n\r\n    // Update stats\r\n    const newStats = { ...detectionStats };\r\n    results.forEach(result => {\r\n      newStats[result.class]++;\r\n    });\r\n    setDetectionStats(newStats);\r\n\r\n    // Generate annotated image\r\n    generateAnnotatedImage(results);\r\n\r\n    // Notify parent component\r\n    onResults(results);\r\n  }, [currentImage, detectionStats, onResults, generateAnnotatedImage]);\r\n\r\n  useEffect(() => {\r\n    // Simulate receiving detection results\r\n    if (isAnalyzing) {\r\n      const mockResults = generateMockResults();\r\n      setTimeout(() => {\r\n        handleDetectionResults(mockResults);\r\n      }, 2000);\r\n    }\r\n  }, [isAnalyzing, handleDetectionResults]);\r\n\r\n  const generateMockResults = () => {\r\n    const classes = ['decaycavity', 'early-decay', 'healthy tooth'];\r\n    const numDetections = Math.floor(Math.random() * 3) + 1;\r\n    const results = [];\r\n\r\n    for (let i = 0; i < numDetections; i++) {\r\n      const randomClass = classes[Math.floor(Math.random() * classes.length)];\r\n      results.push({\r\n        class: randomClass,\r\n        confidence: Math.random() * 0.4 + 0.6, // 60-100% confidence\r\n        bbox: {\r\n          x: Math.random() * 0.8,\r\n          y: Math.random() * 0.8,\r\n          width: Math.random() * 0.3 + 0.1,\r\n          height: Math.random() * 0.3 + 0.1\r\n        }\r\n      });\r\n    }\r\n\r\n    return results;\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#51cf66';\r\n    if (confidence >= 0.6) return '#ffd43b';\r\n    return '#ff6b6b';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Model Info */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaBrain className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">AI Analysis</h3>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n            <FaRobot className=\"inline mr-1\" />\r\n            Model: best.pt\r\n          </span>\r\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\r\n            Classes: 3\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Analysis Status */}\r\n      {isAnalyzing && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\"\r\n        >\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"loading-spinner mr-3\"></div>\r\n            <p className=\"text-[#0077B6] font-medium\">Analyzing image with YOLOv8...</p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Annotated Image */}\r\n      {annotatedImage && (\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\"\r\n        >\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n              <FaImage className=\"h-4 w-4\" />\r\n            </div>\r\n            <h4 className=\"text-lg font-semibold text-[#0077B6]\">Annotated Results</h4>\r\n          </div>\r\n          <div className=\"bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200\">\r\n            <img\r\n              src={annotatedImage}\r\n              alt=\"Annotated detection\"\r\n              className=\"w-full h-auto max-h-64 object-contain\"\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Detection Statistics */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartBar className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Detection Statistics</h4>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n          {Object.entries(detectionStats).map(([className, count]) => (\r\n            <motion.div\r\n              key={className}\r\n              whileHover={{ scale: 1.05 }}\r\n              className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"text-2xl mr-3\">{classIcons[className]}</div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-700 capitalize\">{className.replace('-', ' ')}</p>\r\n                  <p className=\"text-2xl font-bold text-[#0077B6]\">{count}</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Detection History */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaClock className=\"h-4 w-4\" />\r\n          </div>\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Detections</h4>\r\n        </div>\r\n\r\n        {detectionHistory.length > 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {detectionHistory.map((detection) => (\r\n              <motion.div\r\n                key={detection.id}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n              >\r\n                <div className=\"flex justify-between items-center mb-3\">\r\n                  <span className=\"text-sm font-medium text-gray-600\">\r\n                    {new Date(detection.timestamp).toLocaleTimeString()}\r\n                  </span>\r\n                  <span className=\"px-2 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n                    {detection.results.length} detection(s)\r\n                  </span>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\r\n                  {detection.results.map((result, index) => (\r\n                    <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded-lg\">\r\n                      <span className=\"flex items-center text-sm font-medium text-gray-700\">\r\n                        <span className=\"mr-2\">{classIcons[result.class]}</span>\r\n                        {result.class.replace('-', ' ')}\r\n                      </span>\r\n                      <span\r\n                        className=\"text-sm font-bold\"\r\n                        style={{ color: getConfidenceColor(result.confidence) }}\r\n                      >\r\n                        {(result.confidence * 100).toFixed(1)}%\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <FaBrain className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\r\n            <p>No detections yet. Capture images to start AI analysis.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC/E,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC;IACnDsB,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,eAAe,EAAE;EACnB,CAAC;EAEDvB,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,YAAY,EAAE;MAChBW,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC,EAAE,CAACZ,YAAY,CAAC,CAAC;EAElB,MAAMa,kBAAkB,GAAGzB,WAAW,CAAC,CAAC0B,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,KAAK;IAClFF,OAAO,CAACG,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACjC;MACA,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIP,WAAW,GAAG,GAAG,CAAC;MAC7C,MAAMQ,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIN,YAAY,GAAG,GAAG,CAAC;MAC9C,MAAMQ,KAAK,GAAG,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;MACrC,MAAMG,MAAM,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;;MAEtC;MACA,MAAMI,KAAK,GAAGlB,WAAW,CAACU,MAAM,CAACS,KAAK,CAAC,IAAI,SAAS;;MAEpD;MACAd,GAAG,CAACe,WAAW,GAAGF,KAAK;MACvBb,GAAG,CAACgB,SAAS,GAAG,CAAC;MACjBhB,GAAG,CAACiB,UAAU,CAACV,CAAC,EAAEG,CAAC,EAAEC,KAAK,EAAEC,MAAM,CAAC;;MAEnC;MACA,MAAMM,KAAK,GAAG,GAAGb,MAAM,CAACS,KAAK,KAAK,CAACT,MAAM,CAACc,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;MAC1EpB,GAAG,CAACqB,IAAI,GAAG,YAAY;MACvB,MAAMC,SAAS,GAAGtB,GAAG,CAACuB,WAAW,CAACL,KAAK,CAAC,CAACP,KAAK;MAE9CX,GAAG,CAACwB,SAAS,GAAGX,KAAK;MACrBb,GAAG,CAACyB,QAAQ,CAAClB,CAAC,EAAEG,CAAC,GAAG,EAAE,EAAEY,SAAS,GAAG,EAAE,EAAE,EAAE,CAAC;;MAE3C;MACAtB,GAAG,CAACwB,SAAS,GAAG,OAAO;MACvBxB,GAAG,CAAC0B,QAAQ,CAACR,KAAK,EAAEX,CAAC,GAAG,CAAC,EAAEG,CAAC,GAAG,EAAE,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,sBAAsB,GAAGrD,WAAW,CAAE2B,OAAO,IAAK;IACtD;IACA,MAAM2B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAM9B,GAAG,GAAG4B,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAH,MAAM,CAACjB,KAAK,GAAG,GAAG;IAClBiB,MAAM,CAAChB,MAAM,GAAG,GAAG;;IAEnB;IACA,IAAI1B,YAAY,EAAE;MAChB,MAAM8C,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB;QACAlC,GAAG,CAACmC,SAAS,CAACH,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;QAErD;QACAb,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAE2B,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;QAE7D;QACArB,iBAAiB,CAACqC,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;MACvC,CAAC;MACDJ,GAAG,CAACK,GAAG,GAAGnD,YAAY;IACxB,CAAC,MAAM;MACL;MACAc,GAAG,CAACwB,SAAS,GAAG,SAAS;MACzBxB,GAAG,CAACyB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEG,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;;MAE/C;MACAb,kBAAkB,CAACC,GAAG,EAAEC,OAAO,EAAE2B,MAAM,CAACjB,KAAK,EAAEiB,MAAM,CAAChB,MAAM,CAAC;MAE7DrB,iBAAiB,CAACqC,MAAM,CAACQ,SAAS,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAAClD,YAAY,EAAEa,kBAAkB,CAAC,CAAC;EAEtC,MAAMuC,sBAAsB,GAAGhE,WAAW,CAAE2B,OAAO,IAAK;IACtD,MAAMsC,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnC3C,OAAO,EAAEA,OAAO;MAChB4C,KAAK,EAAE3D,YAAY,IAAI;IACzB,CAAC;IAEDG,mBAAmB,CAACyD,IAAI,IAAI,CAACP,YAAY,EAAE,GAAGO,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAGxD;IAAe,CAAC;IACtCS,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;MACxB2C,QAAQ,CAAC3C,MAAM,CAACS,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACFrB,iBAAiB,CAACuD,QAAQ,CAAC;;IAE3B;IACArB,sBAAsB,CAAC1B,OAAO,CAAC;;IAE/B;IACAjB,SAAS,CAACiB,OAAO,CAAC;EACpB,CAAC,EAAE,CAACf,YAAY,EAAEM,cAAc,EAAER,SAAS,EAAE2C,sBAAsB,CAAC,CAAC;EAErEtD,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,WAAW,EAAE;MACf,MAAMgE,WAAW,GAAGC,mBAAmB,CAAC,CAAC;MACzCC,UAAU,CAAC,MAAM;QACfb,sBAAsB,CAACW,WAAW,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAAChE,WAAW,EAAEqD,sBAAsB,CAAC,CAAC;EAEzC,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAME,OAAO,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAG7C,IAAI,CAAC8C,KAAK,CAAC9C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,MAAMR,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,EAAE,EAAE;MACtC,MAAMC,WAAW,GAAGJ,OAAO,CAAC5C,IAAI,CAAC8C,KAAK,CAAC9C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG2C,OAAO,CAACK,MAAM,CAAC,CAAC;MACvExD,OAAO,CAACyD,IAAI,CAAC;QACX5C,KAAK,EAAE0C,WAAW;QAClBrC,UAAU,EAAEX,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAAE;QACvCkD,IAAI,EAAE;UACJpD,CAAC,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBC,CAAC,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACtBE,KAAK,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCG,MAAM,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOR,OAAO;EAChB,CAAC;EAED,MAAM2D,kBAAkB,GAAIzC,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,oBACErC,OAAA;IAAK+E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhF,OAAA;MAAK+E,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAC1FhF,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACN,OAAO;YAACqF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhF,OAAA;UAAM+E,SAAS,EAAC,oFAAoF;UAAAC,QAAA,gBAClGhF,OAAA,CAACF,OAAO;YAACiF,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpF,OAAA;UAAM+E,SAAS,EAAC,qFAAqF;UAAAC,QAAA,EAAC;QAEtG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjF,WAAW,iBACVH,OAAA,CAACP,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAE3D,CAAC,EAAE;MAAG,CAAE;MAC/B4D,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAE3D,CAAC,EAAE;MAAE,CAAE;MAC9BmD,SAAS,EAAC,oFAAoF;MAAAC,QAAA,eAE9FhF,OAAA;QAAK+E,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/ChF,OAAA;UAAK+E,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CpF,OAAA;UAAG+E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGA5E,cAAc,iBACbR,OAAA,CAACP,MAAM,CAAC4F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAK,CAAE;MACrCD,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEE,KAAK,EAAE;MAAE,CAAE;MAClCV,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEpDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACH,OAAO;YAACkF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,sEAAsE;QAAAC,QAAA,eACnFhF,OAAA;UACEuD,GAAG,EAAE/C,cAAe;UACpBkF,GAAG,EAAC,qBAAqB;UACzBX,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAGDpF,OAAA;MAAK+E,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACL,UAAU;YAACoF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNpF,OAAA;QAAK+E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDW,MAAM,CAACC,OAAO,CAAClF,cAAc,CAAC,CAACmF,GAAG,CAAC,CAAC,CAACd,SAAS,EAAEe,KAAK,CAAC,kBACrD9F,OAAA,CAACP,MAAM,CAAC4F,GAAG;UAETU,UAAU,EAAE;YAAEN,KAAK,EAAE;UAAK,CAAE;UAC5BV,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAEvHhF,OAAA;YAAK+E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChF,OAAA;cAAK+E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAElE,UAAU,CAACiE,SAAS;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DpF,OAAA;cAAAgF,QAAA,gBACEhF,OAAA;gBAAG+E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAED,SAAS,CAACiB,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FpF,OAAA;gBAAG+E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEc;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAVDL,SAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA;MAAK+E,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDhF,OAAA;QAAK+E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrChF,OAAA;UAAK+E,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EhF,OAAA,CAACJ,OAAO;YAACmF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNpF,OAAA;UAAI+E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,EAEL9E,gBAAgB,CAACqE,MAAM,GAAG,CAAC,gBAC1B3E,OAAA;QAAK+E,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB1E,gBAAgB,CAACuF,GAAG,CAAEI,SAAS,iBAC9BjG,OAAA,CAACP,MAAM,CAAC4F,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAE9D,CAAC,EAAE,CAAC;UAAG,CAAE;UAChC+D,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAE9D,CAAC,EAAE;UAAE,CAAE;UAC9BsD,SAAS,EAAC,6GAA6G;UAAAC,QAAA,gBAEvHhF,OAAA;YAAK+E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDhF,OAAA;cAAM+E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAChD,IAAIrB,IAAI,CAACsC,SAAS,CAACpC,SAAS,CAAC,CAACqC,kBAAkB,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACPpF,OAAA;cAAM+E,SAAS,EAAC,oFAAoF;cAAAC,QAAA,GACjGiB,SAAS,CAAC9E,OAAO,CAACwD,MAAM,EAAC,eAC5B;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENpF,OAAA;YAAK+E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDiB,SAAS,CAAC9E,OAAO,CAAC0E,GAAG,CAAC,CAACtE,MAAM,EAAEC,KAAK,kBACnCxB,OAAA;cAAiB+E,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACtFhF,OAAA;gBAAM+E,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,gBACnEhF,OAAA;kBAAM+E,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAElE,UAAU,CAACS,MAAM,CAACS,KAAK;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvD7D,MAAM,CAACS,KAAK,CAACgE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACPpF,OAAA;gBACE+E,SAAS,EAAC,mBAAmB;gBAC7BoB,KAAK,EAAE;kBAAEpE,KAAK,EAAE+C,kBAAkB,CAACvD,MAAM,CAACc,UAAU;gBAAE,CAAE;gBAAA2C,QAAA,GAEvD,CAACzD,MAAM,CAACc,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAVC5D,KAAK;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GA7BDa,SAAS,CAACvC,EAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BP,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENpF,OAAA;QAAK+E,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7ChF,OAAA,CAACN,OAAO;UAACqF,SAAS,EAAC;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDpF,OAAA;UAAAgF,QAAA,EAAG;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAvSIJ,aAAa;AAAAmG,EAAA,GAAbnG,aAAa;AAySnB,eAAeA,aAAa;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}