{"ast": null, "code": "import { animationControls } from './animation-controls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\n/**\n * Creates `AnimationControls`, which can be used to manually start, stop\n * and sequence animations on one or more components.\n *\n * The returned `AnimationControls` should be passed to the `animate` property\n * of the components you want to animate.\n *\n * These components can then be animated with the `start` method.\n *\n * ```jsx\n * import * as React from 'react'\n * import { motion, useAnimation } from 'framer-motion'\n *\n * export function MyComponent(props) {\n *    const controls = useAnimation()\n *\n *    controls.start({\n *        x: 100,\n *        transition: { duration: 0.5 },\n *    })\n *\n *    return <motion.div animate={controls} />\n * }\n * ```\n *\n * @returns Animation controller with `start` and `stop` methods\n *\n * @public\n */\nfunction useAnimationControls() {\n  const controls = useConstant(animationControls);\n  useIsomorphicLayoutEffect(controls.mount, []);\n  return controls;\n}\nconst useAnimation = useAnimationControls;\nexport { useAnimation, useAnimationControls };", "map": {"version": 3, "names": ["animationControls", "useConstant", "useIsomorphicLayoutEffect", "useAnimationControls", "controls", "mount", "useAnimation"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/animation/hooks/use-animation.mjs"], "sourcesContent": ["import { animationControls } from './animation-controls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\n/**\n * Creates `AnimationControls`, which can be used to manually start, stop\n * and sequence animations on one or more components.\n *\n * The returned `AnimationControls` should be passed to the `animate` property\n * of the components you want to animate.\n *\n * These components can then be animated with the `start` method.\n *\n * ```jsx\n * import * as React from 'react'\n * import { motion, useAnimation } from 'framer-motion'\n *\n * export function MyComponent(props) {\n *    const controls = useAnimation()\n *\n *    controls.start({\n *        x: 100,\n *        transition: { duration: 0.5 },\n *    })\n *\n *    return <motion.div animate={controls} />\n * }\n * ```\n *\n * @returns Animation controller with `start` and `stop` methods\n *\n * @public\n */\nfunction useAnimationControls() {\n    const controls = useConstant(animationControls);\n    useIsomorphicLayoutEffect(controls.mount, []);\n    return controls;\n}\nconst useAnimation = useAnimationControls;\n\nexport { useAnimation, useAnimationControls };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,yBAAyB,QAAQ,uCAAuC;;AAEjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,QAAQ,GAAGH,WAAW,CAACD,iBAAiB,CAAC;EAC/CE,yBAAyB,CAACE,QAAQ,CAACC,KAAK,EAAE,EAAE,CAAC;EAC7C,OAAOD,QAAQ;AACnB;AACA,MAAME,YAAY,GAAGH,oBAAoB;AAEzC,SAASG,YAAY,EAAEH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}