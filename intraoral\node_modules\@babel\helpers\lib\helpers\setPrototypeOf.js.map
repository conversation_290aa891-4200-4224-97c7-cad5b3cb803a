{"version": 3, "names": ["_setPrototypeOf", "o", "p", "exports", "default", "Object", "setPrototypeOf", "bind", "__proto__"], "sources": ["../../src/helpers/setPrototypeOf.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _setPrototypeOf(o: object, p: object) {\n  // @ts-expect-error - assigning to function\n  _setPrototypeOf = Object.setPrototypeOf\n    ? // @ts-expect-error - intentionally omitted argument\n      Object.setPrototypeOf.bind(/* undefined */)\n    : function _setPrototypeOf(o: object, p: object) {\n        (o as any).__proto__ = p;\n        return o;\n      };\n  return _setPrototypeOf(o, p);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CAACC,CAAS,EAAEC,CAAS,EAAE;EAE5DC,OAAA,CAAAC,OAAA,GAAAJ,eAAe,GAAGK,MAAM,CAACC,cAAc,GAEnCD,MAAM,CAACC,cAAc,CAACC,IAAI,CAAgB,CAAC,GAC3C,SAASP,eAAeA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAC5CD,CAAC,CAASO,SAAS,GAAGN,CAAC;IACxB,OAAOD,CAAC;EACV,CAAC;EACL,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B", "ignoreList": []}