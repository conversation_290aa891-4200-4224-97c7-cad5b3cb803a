{"version": 3, "names": ["_assert", "require", "leap", "meta", "util", "_core", "PENDING_LOCATION", "Number", "MAX_VALUE", "getDeclError", "node", "Error", "JSON", "stringify", "catchParamVisitor", "Identifier", "path", "state", "name", "catchParamName", "isReference", "replaceWithOrRemove", "getSafeParam", "<PERSON><PERSON>", "scope", "hasOwnBinding", "skip", "Emitter", "constructor", "contextId", "vars", "pluginPass", "nextTempId", "index", "indexMap", "listing", "returns", "lastReferenceIndex", "marked", "insertedLocs", "finalLoc", "tryEntries", "leapManager", "Map", "Set", "loc", "LeapManager", "l", "t", "numericLiteral", "add", "getInsertedLocs", "getContextId", "cloneNode", "getIndex", "has", "length", "set", "mark", "value", "assert", "strictEqual", "emit", "isExpression", "expressionStatement", "assertStatement", "push", "emitAssign", "lhs", "rhs", "assign", "assignmentExpression", "contextProperty", "computed", "memberExpression", "stringLiteral", "identifier", "clearPendingException", "tryLoc", "assignee", "catchCall", "callExpression", "jump", "toLoc", "newHelpersAvailable", "breakStatement", "jumpIf", "test", "ifStatement", "blockStatement", "jumpIfNot", "negatedTest", "isUnaryExpression", "operator", "argument", "unaryExpression", "makeContextTempVar", "makeTempVar", "id", "generateUidIdentifier", "variableDeclarator", "getContextFunction", "functionExpression", "getDispatchLoop", "self", "cases", "current", "alreadyEnded", "for<PERSON>ach", "stmt", "i", "switchCase", "get", "isCompletionStatement", "returnStatement", "whileStatement", "switchStatement", "getTryLocsList", "lastLocValue", "arrayExpression", "map", "tryEntry", "thisLocValue", "firstLoc", "ok", "ce", "catchEntry", "fe", "finallyEntry", "locs", "afterLoc", "elements", "reverse", "explode", "ignoreResult", "isDeclaration", "isStatement", "explodeStatement", "explodeExpression", "type", "labelId", "before", "after", "head", "isBlockStatement", "containsLeap", "with<PERSON><PERSON>ry", "LabeledEntry", "label", "LoopEntry", "first", "update", "init", "keyIterNextFn", "helper", "addHelper", "runtimeProperty", "keyInfoTmpVar", "left", "emitAbruptCompletion", "target", "getBreakLoc", "getContinueLoc", "disc", "defaultLoc", "condition", "caseLocs", "c", "conditionalExpression", "binaryExpression", "discriminant", "SwitchEntry", "casePath", "key", "elseLoc", "alternate", "handler", "catchLoc", "CatchEntry", "param", "finallyLoc", "finalizer", "FinallyEntry", "TryEntry", "getUnmarkedCurrentLoc", "updateContextPrevLoc", "body", "block", "bodyPath", "safeParam", "traverse", "throwStatement", "explodeClass", "record", "<PERSON><PERSON><PERSON><PERSON>", "explodeViaTempVar", "tempVar", "child<PERSON><PERSON>", "hasLeaping<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "isLiteral", "expr", "finish", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "property", "<PERSON><PERSON><PERSON><PERSON>", "args<PERSON><PERSON>", "newCallee", "newArgs", "has<PERSON><PERSON>ping<PERSON>rg<PERSON>", "some", "<PERSON><PERSON><PERSON><PERSON>", "injectFirstArg", "isMemberExpression", "newObject", "newProperty", "sequenceExpression", "unshift", "arg", "arguments", "newExpression", "objectExpression", "prop<PERSON>ath", "isObjectProperty", "objectProperty", "elemPath", "isSpreadElement", "spreadElement", "lastIndex", "expressions", "exprPath", "prefix", "temp", "updateExpression", "delegate", "ret", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "superClass", "member", "child", "isLast", "replaceWith", "exports"], "sources": ["../../src/regenerator/emit.ts"], "sourcesContent": ["/* eslint-disable no-case-declarations */\nimport assert from \"node:assert\";\nimport * as leap from \"./leap.ts\";\nimport * as meta from \"./meta.ts\";\nimport * as util from \"./util.ts\";\n\nimport type { <PERSON><PERSON><PERSON><PERSON>, PluginPass, Visitor, Scope } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\n// From packages/babel-helpers/src/helpers/regenerator.ts\nconst enum OperatorType {\n  Return = 2,\n  Jump,\n}\n\ntype AbruptCompletion =\n  | {\n      type: OperatorType.Jump;\n      target: t.NumericLiteral;\n    }\n  | {\n      type: OperatorType.Return;\n      value: t.Expression | null;\n    };\n\n// Offsets into this.listing that could be used as targets for branches or\n// jumps are represented as numeric Literal nodes. This representation has\n// the amazingly convenient benefit of allowing the exact value of the\n// location to be determined at any time, even after generating code that\n// refers to the location.\n// We use 'Number.MAX_VALUE' to mark uninitialized location. We can safely do\n// so because no code can realistically have about 1.8e+308 locations before\n// hitting memory limit of the machine it's running on. For comparison, the\n// estimated number of atoms in the observable universe is around 1e+80.\nconst PENDING_LOCATION = Number.MAX_VALUE;\n\nfunction getDeclError(node: t.Node) {\n  return new Error(\n    \"all declarations should have been transformed into \" +\n      \"assignments before the Exploder began its work: \" +\n      JSON.stringify(node),\n  );\n}\n\nconst catchParamVisitor: Visitor = {\n  Identifier: function (path, state: any) {\n    if (path.node.name === state.catchParamName && util.isReference(path)) {\n      util.replaceWithOrRemove(path, state.getSafeParam());\n    }\n  },\n\n  Scope: function (path, state: any) {\n    if (path.scope.hasOwnBinding(state.catchParamName)) {\n      // Don't descend into nested scopes that shadow the catch\n      // parameter with their own declarations.\n      path.skip();\n    }\n  },\n};\n\nexport class Emitter {\n  nextTempId: number;\n  contextId: t.Identifier;\n  index: number;\n  indexMap: Map<number, number>;\n  listing: t.Statement[];\n  returns: Set<number>;\n  lastReferenceIndex: number = 0;\n  marked: boolean[];\n  insertedLocs: Set<t.NumericLiteral>;\n  finalLoc: t.NumericLiteral;\n  tryEntries: leap.TryEntry[];\n  leapManager: leap.LeapManager;\n  scope: Scope;\n  vars: t.VariableDeclarator[];\n\n  pluginPass: PluginPass;\n\n  constructor(\n    contextId: t.Identifier,\n    scope: Scope,\n    vars: t.VariableDeclarator[],\n    pluginPass: PluginPass,\n  ) {\n    this.pluginPass = pluginPass;\n    this.scope = scope;\n    this.vars = vars;\n\n    // Used to generate unique temporary names.\n    this.nextTempId = 0;\n\n    // In order to make sure the context object does not collide with\n    // anything in the local scope, we might have to rename it, so we\n    // refer to it symbolically instead of just assuming that it will be\n    // called \"context\".\n    this.contextId = contextId;\n\n    // An append-only list of Statements that grows each time this.emit is\n    // called.\n    this.listing = [];\n\n    this.index = 0;\n    this.indexMap = new Map([[0, 0]]);\n    this.returns = new Set();\n    this.lastReferenceIndex = 0;\n\n    // A sparse array whose keys correspond to locations in this.listing\n    // that have been marked as branch/jump targets.\n    this.marked = [true];\n\n    this.insertedLocs = new Set();\n\n    // The last location will be marked when this.getDispatchLoop is\n    // called.\n    this.finalLoc = this.loc();\n\n    // A list of all leap.TryEntry statements emitted.\n    this.tryEntries = [];\n\n    // Each time we evaluate the body of a loop, we tell this.leapManager\n    // to enter a nested loop context that determines the meaning of break\n    // and continue statements therein.\n    this.leapManager = new leap.LeapManager(this);\n  }\n\n  loc() {\n    const l = t.numericLiteral(PENDING_LOCATION);\n    this.insertedLocs.add(l);\n    return l;\n  }\n\n  getInsertedLocs() {\n    return this.insertedLocs;\n  }\n\n  getContextId() {\n    return t.cloneNode(this.contextId);\n  }\n\n  getIndex() {\n    if (!this.indexMap.has(this.listing.length)) {\n      this.indexMap.set(this.listing.length, ++this.index);\n    }\n    return this.index;\n  }\n\n  // Sets the exact value of the given location to the offset of the next\n  // Statement emitted.\n  mark(loc: t.NumericLiteral) {\n    if (loc.value === PENDING_LOCATION) {\n      loc.value = this.getIndex();\n    } else {\n      // Locations can be marked redundantly, but their values cannot change\n      // once set the first time.\n      assert.strictEqual(loc.value, this.index);\n    }\n    this.marked[this.listing.length] = true;\n    if (loc.value > this.lastReferenceIndex) {\n      this.lastReferenceIndex = loc.value;\n    }\n    return loc;\n  }\n\n  emit(node: t.Node) {\n    if (t.isExpression(node)) {\n      node = t.expressionStatement(node);\n    }\n    t.assertStatement(node);\n    this.listing.push(node);\n  }\n\n  // Shorthand for emitting assignment statements. This will come in handy\n  // for assignments to temporary variables.\n  emitAssign<T extends t.AssignmentExpression[\"left\"]>(\n    lhs: T,\n    rhs: t.Expression,\n  ) {\n    this.emit(this.assign(lhs, rhs));\n    return lhs;\n  }\n\n  // Shorthand for an assignment statement.\n  assign(lhs: t.AssignmentExpression[\"left\"], rhs: t.Expression) {\n    return t.expressionStatement(\n      t.assignmentExpression(\"=\", t.cloneNode(lhs), rhs),\n    );\n  }\n\n  // Convenience function for generating expressions like context.next,\n  // context.sent, and context.rval.\n  contextProperty(name: string) {\n    const computed = name === \"catch\";\n    return t.memberExpression(\n      this.getContextId(),\n      computed ? t.stringLiteral(name) : t.identifier(name),\n      !!computed,\n    );\n  }\n\n  clearPendingException(\n    tryLoc: t.NumericLiteral,\n    assignee: t.AssignmentExpression[\"left\"],\n  ) {\n    const catchCall = t.callExpression(this.contextProperty(\"catch\"), [\n      t.cloneNode(tryLoc),\n    ]);\n\n    if (assignee) {\n      this.emitAssign(assignee, catchCall);\n    } else {\n      this.emit(catchCall);\n    }\n  }\n\n  // Emits code for an unconditional jump to the given location, even if the\n  // exact value of the location is not yet known.\n  jump(toLoc: t.Expression) {\n    this.emitAssign(\n      this.contextProperty(\n        process.env.BABEL_8_BREAKING ||\n          util.newHelpersAvailable(this.pluginPass)\n          ? \"n\"\n          : \"next\",\n      ),\n      toLoc,\n    );\n    this.emit(t.breakStatement());\n  }\n\n  // Conditional jump.\n  jumpIf(test: t.Expression, toLoc: t.NumericLiteral) {\n    this.emit(\n      t.ifStatement(\n        test,\n        t.blockStatement([\n          this.assign(\n            this.contextProperty(\n              process.env.BABEL_8_BREAKING ||\n                util.newHelpersAvailable(this.pluginPass)\n                ? \"n\"\n                : \"next\",\n            ),\n            toLoc,\n          ),\n          t.breakStatement(),\n        ]),\n      ),\n    );\n  }\n\n  // Conditional jump, with the condition negated.\n  jumpIfNot(test: t.Expression, toLoc: t.NumericLiteral) {\n    let negatedTest;\n    if (t.isUnaryExpression(test) && test.operator === \"!\") {\n      // Avoid double negation.\n      negatedTest = test.argument;\n    } else {\n      negatedTest = t.unaryExpression(\"!\", test);\n    }\n\n    this.emit(\n      t.ifStatement(\n        negatedTest,\n        t.blockStatement([\n          this.assign(\n            this.contextProperty(\n              process.env.BABEL_8_BREAKING ||\n                util.newHelpersAvailable(this.pluginPass)\n                ? \"n\"\n                : \"next\",\n            ),\n            toLoc,\n          ),\n          t.breakStatement(),\n        ]),\n      ),\n    );\n  }\n\n  // Returns a unique MemberExpression that can be used to store and\n  // retrieve temporary values. Since the object of the member expression is\n  // the context object, which is presumed to coexist peacefully with all\n  // other local variables, and since we just increment `nextTempId`\n  // monotonically, uniqueness is assured.\n  makeContextTempVar() {\n    return this.contextProperty(\"t\" + this.nextTempId++);\n  }\n\n  makeTempVar() {\n    const id = this.scope.generateUidIdentifier(\"t\");\n    this.vars.push(t.variableDeclarator(id));\n    return t.cloneNode(id);\n  }\n\n  getContextFunction() {\n    return t.functionExpression(\n      null /*Anonymous*/,\n      [this.getContextId()],\n      t.blockStatement([this.getDispatchLoop()]),\n      false, // Not a generator anymore!\n      false, // Nor an expression.\n    );\n  }\n\n  // Turns this.listing into a loop of the form\n  //\n  //   while (1) switch (context.next) {\n  //   case 0:\n  //   ...\n  //   case n:\n  //     return context.stop();\n  //   }\n  //\n  // Each marked location in this.listing will correspond to one generated\n  // case statement.\n  getDispatchLoop() {\n    const self = this;\n    const cases: t.SwitchCase[] = [];\n    let current;\n\n    // If we encounter a break, continue, or return statement in a switch\n    // case, we can skip the rest of the statements until the next case.\n    let alreadyEnded = false;\n\n    self.listing.forEach(function (stmt, i) {\n      if (self.marked[i]) {\n        cases.push(\n          t.switchCase(t.numericLiteral(self.indexMap.get(i)), (current = [])),\n        );\n        alreadyEnded = false;\n      }\n\n      if (!alreadyEnded) {\n        current.push(stmt);\n        if (t.isCompletionStatement(stmt)) alreadyEnded = true;\n      }\n    });\n\n    // Now that we know how many statements there will be in this.listing,\n    // we can finally resolve this.finalLoc.value.\n    this.finalLoc.value = this.getIndex();\n\n    if (\n      process.env.BABEL_8_BREAKING ||\n      util.newHelpersAvailable(this.pluginPass)\n    ) {\n      if (\n        this.lastReferenceIndex === this.index ||\n        !this.returns.has(this.listing.length)\n      ) {\n        cases.push(\n          t.switchCase(this.finalLoc, [\n            t.returnStatement(\n              t.callExpression(this.contextProperty(\"a\"), [\n                t.numericLiteral(OperatorType.Return),\n              ]),\n            ),\n          ]),\n        );\n      }\n    } else {\n      cases.push(\n        t.switchCase(this.finalLoc, [\n          // Intentionally fall through to the \"end\" case...\n        ]),\n\n        // So that the runtime can jump to the final location without having\n        // to know its offset, we provide the \"end\" case as a synonym.\n        t.switchCase(t.stringLiteral(\"end\"), [\n          // This will check/clear both context.thrown and context.rval.\n          t.returnStatement(t.callExpression(this.contextProperty(\"stop\"), [])),\n        ]),\n      );\n    }\n\n    return t.whileStatement(\n      t.numericLiteral(1),\n      t.switchStatement(\n        process.env.BABEL_8_BREAKING ||\n          util.newHelpersAvailable(this.pluginPass)\n          ? this.contextProperty(\"n\")\n          : t.assignmentExpression(\n              \"=\",\n              this.contextProperty(\"prev\"),\n              this.contextProperty(\"next\"),\n            ),\n        cases,\n      ),\n    );\n  }\n\n  getTryLocsList() {\n    if (this.tryEntries.length === 0) {\n      // To avoid adding a needless [] to the majority of runtime.wrap\n      // argument lists, force the caller to handle this case specially.\n      return null;\n    }\n\n    let lastLocValue = 0;\n\n    const arrayExpression = t.arrayExpression(\n      this.tryEntries.map(function (tryEntry) {\n        const thisLocValue = tryEntry.firstLoc.value;\n        assert.ok(thisLocValue >= lastLocValue, \"try entries out of order\");\n        lastLocValue = thisLocValue;\n\n        const ce = tryEntry.catchEntry;\n        const fe = tryEntry.finallyEntry;\n\n        const locs = [\n          tryEntry.firstLoc,\n          // The null here makes a hole in the array.\n          ce ? ce.firstLoc : null,\n        ];\n\n        if (fe) {\n          locs[2] = fe.firstLoc;\n          locs[3] = fe.afterLoc;\n        }\n\n        return t.arrayExpression(locs.map(loc => loc && t.cloneNode(loc)));\n      }),\n    );\n    if (\n      process.env.BABEL_8_BREAKING ||\n      util.newHelpersAvailable(this.pluginPass)\n    ) {\n      arrayExpression.elements.reverse();\n    }\n    return arrayExpression;\n  }\n\n  // All side effects must be realized in order.\n\n  // If any subexpression harbors a leap, all subexpressions must be\n  // neutered of side effects.\n\n  // No destructive modification of AST nodes.\n  explode(path: NodePath, ignoreResult?: boolean) {\n    const node = path.node;\n    const self = this;\n\n    if (t.isDeclaration(node)) throw getDeclError(node);\n\n    if (path.isStatement()) return self.explodeStatement(path);\n\n    if (path.isExpression()) return self.explodeExpression(path, ignoreResult);\n\n    switch (node.type) {\n      case \"VariableDeclarator\":\n        throw getDeclError(node);\n\n      // These node types should be handled by their parent nodes\n      // (ObjectExpression, SwitchStatement, and TryStatement, respectively).\n      case \"ObjectProperty\":\n      case \"SwitchCase\":\n      case \"CatchClause\":\n        throw new Error(\n          node.type + \" nodes should be handled by their parents\",\n        );\n\n      default:\n        throw new Error(\"unknown Node of type \" + JSON.stringify(node.type));\n    }\n  }\n\n  explodeStatement(path: NodePath<t.Statement>, labelId: t.Identifier = null) {\n    const stmt = path.node;\n    const self = this;\n    let before: t.NumericLiteral,\n      after: t.NumericLiteral,\n      head: t.NumericLiteral;\n\n    // Explode BlockStatement nodes even if they do not contain a yield,\n    // because we don't want or need the curly braces.\n    if (path.isBlockStatement()) {\n      path.get(\"body\").forEach(function (path) {\n        self.explodeStatement(path);\n      });\n      return;\n    }\n\n    if (!meta.containsLeap(stmt)) {\n      // Technically we should be able to avoid emitting the statement\n      // altogether if !meta.hasSideEffects(stmt), but that leads to\n      // confusing generated code (for instance, `while (true) {}` just\n      // disappears) and is probably a more appropriate job for a dedicated\n      // dead code elimination pass.\n      self.emit(stmt);\n      return;\n    }\n\n    switch (path.type) {\n      case \"ExpressionStatement\":\n        self.explodeExpression(path.get(\"expression\"), true);\n        break;\n\n      case \"LabeledStatement\":\n        after = this.loc();\n\n        // Did you know you can break from any labeled block statement or\n        // control structure? Well, you can! Note: when a labeled loop is\n        // encountered, the leap.LabeledEntry created here will immediately\n        // enclose a leap.LoopEntry on the leap manager's stack, and both\n        // entries will have the same label. Though this works just fine, it\n        // may seem a bit redundant. In theory, we could check here to\n        // determine if stmt knows how to handle its own label; for example,\n        // stmt happens to be a WhileStatement and so we know it's going to\n        // establish its own LoopEntry when we explode it (below). Then this\n        // LabeledEntry would be unnecessary. Alternatively, we might be\n        // tempted not to pass stmt.label down into self.explodeStatement,\n        // because we've handled the label here, but that's a mistake because\n        // labeled loops may contain labeled continue statements, which is not\n        // something we can handle in this generic case. All in all, I think a\n        // little redundancy greatly simplifies the logic of this case, since\n        // it's clear that we handle all possible LabeledStatements correctly\n        // here, regardless of whether they interact with the leap manager\n        // themselves. Also remember that labels and break/continue-to-label\n        // statements are rare, and all of this logic happens at transform\n        // time, so it has no additional runtime cost.\n        self.leapManager.withEntry(\n          new leap.LabeledEntry(after, path.node.label),\n          function () {\n            self.explodeStatement(path.get(\"body\"), path.node.label);\n          },\n        );\n\n        self.mark(after);\n\n        break;\n\n      case \"WhileStatement\":\n        before = this.loc();\n        after = this.loc();\n\n        self.mark(before);\n        self.jumpIfNot(self.explodeExpression(path.get(\"test\")), after);\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, before, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n        self.jump(before);\n        self.mark(after);\n\n        break;\n\n      case \"DoWhileStatement\":\n        const first = this.loc();\n        const test = this.loc();\n        after = this.loc();\n\n        self.mark(first);\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, test, labelId),\n          function () {\n            self.explode(path.get(\"body\"));\n          },\n        );\n        self.mark(test);\n        self.jumpIf(self.explodeExpression(path.get(\"test\")), first);\n        self.mark(after);\n\n        break;\n\n      case \"ForStatement\":\n        head = this.loc();\n        const update = this.loc();\n        after = this.loc();\n\n        if (path.node.init) {\n          // We pass true here to indicate that if stmt.init is an expression\n          // then we do not care about its result.\n          self.explode(path.get(\"init\"), true);\n        }\n\n        self.mark(head);\n\n        if (path.node.test) {\n          self.jumpIfNot(self.explodeExpression(path.get(\"test\")), after);\n        } else {\n          // No test means continue unconditionally.\n        }\n\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, update, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n\n        self.mark(update);\n\n        if (path.node.update) {\n          // We pass true here to indicate that if stmt.update is an\n          // expression then we do not care about its result.\n          self.explode(path.get(\"update\"), true);\n        }\n\n        self.jump(head);\n\n        self.mark(after);\n\n        break;\n\n      // @ts-expect-error flow type\n      case \"TypeCastExpression\":\n        return self.explodeExpression((path as any).get(\"expression\"));\n\n      case \"ForInStatement\":\n        head = this.loc();\n        after = this.loc();\n\n        const keyIterNextFn = self.makeTempVar();\n\n        const helper =\n          process.env.BABEL_8_BREAKING ||\n          util.newHelpersAvailable(this.pluginPass)\n            ? this.pluginPass.addHelper(\"regeneratorKeys\")\n            : util.runtimeProperty(this.pluginPass, \"keys\");\n\n        self.emitAssign(\n          keyIterNextFn,\n          t.callExpression(helper, [self.explodeExpression(path.get(\"right\"))]),\n        );\n\n        self.mark(head);\n\n        const keyInfoTmpVar = self.makeTempVar();\n        self.jumpIf(\n          t.memberExpression(\n            t.assignmentExpression(\n              \"=\",\n              keyInfoTmpVar,\n              t.callExpression(t.cloneNode(keyIterNextFn), []),\n            ),\n            t.identifier(\"done\"),\n            false,\n          ),\n          after,\n        );\n\n        self.emitAssign(\n          path.node.left as t.AssignmentExpression[\"left\"],\n          t.memberExpression(\n            t.cloneNode(keyInfoTmpVar),\n            t.identifier(\"value\"),\n            false,\n          ),\n        );\n\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, head, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n\n        self.jump(head);\n\n        self.mark(after);\n\n        break;\n\n      case \"BreakStatement\":\n        self.emitAbruptCompletion({\n          type: OperatorType.Jump,\n          target: self.leapManager.getBreakLoc(path.node.label),\n        });\n\n        break;\n\n      case \"ContinueStatement\":\n        self.emitAbruptCompletion({\n          type: OperatorType.Jump,\n          target: self.leapManager.getContinueLoc(path.node.label),\n        });\n\n        break;\n\n      case \"SwitchStatement\":\n        // Always save the discriminant into a temporary variable in case the\n        // test expressions overwrite values like context.sent.\n        const disc = self.emitAssign(\n          self.makeTempVar(),\n          self.explodeExpression(path.get(\"discriminant\")),\n        );\n\n        after = this.loc();\n        const defaultLoc = this.loc();\n        let condition: t.Expression = defaultLoc;\n        const caseLocs: t.NumericLiteral[] = [];\n\n        // If there are no cases, .cases might be undefined.\n        const cases = path.node.cases || [];\n\n        for (let i = cases.length - 1; i >= 0; --i) {\n          const c = cases[i];\n\n          if (c.test) {\n            condition = t.conditionalExpression(\n              t.binaryExpression(\"===\", t.cloneNode(disc), c.test),\n              (caseLocs[i] = this.loc()),\n              condition,\n            );\n          } else {\n            caseLocs[i] = defaultLoc;\n          }\n        }\n\n        const discriminant = path.get(\"discriminant\");\n        util.replaceWithOrRemove(discriminant, condition);\n        self.jump(self.explodeExpression(discriminant));\n\n        self.leapManager.withEntry(new leap.SwitchEntry(after), function () {\n          path.get(\"cases\").forEach(function (casePath) {\n            const i = casePath.key as number;\n            self.mark(caseLocs[i]);\n\n            casePath.get(\"consequent\").forEach(function (path) {\n              self.explodeStatement(path);\n            });\n          });\n        });\n\n        self.mark(after);\n        if (defaultLoc.value === PENDING_LOCATION) {\n          self.mark(defaultLoc);\n          assert.strictEqual(after.value, defaultLoc.value);\n        }\n\n        break;\n\n      case \"IfStatement\":\n        const elseLoc = path.node.alternate && this.loc();\n        after = this.loc();\n\n        self.jumpIfNot(\n          self.explodeExpression(path.get(\"test\")),\n          elseLoc || after,\n        );\n\n        self.explodeStatement(path.get(\"consequent\"));\n\n        if (elseLoc) {\n          self.jump(after);\n          self.mark(elseLoc);\n          self.explodeStatement(path.get(\"alternate\"));\n        }\n\n        self.mark(after);\n\n        break;\n\n      case \"ReturnStatement\":\n        self.emitAbruptCompletion({\n          type: OperatorType.Return,\n          value: self.explodeExpression(path.get(\"argument\")),\n        });\n\n        break;\n\n      case \"WithStatement\":\n        throw new Error(\"WithStatement not supported in generator functions.\");\n\n      case \"TryStatement\":\n        after = this.loc();\n\n        const handler = path.node.handler;\n\n        const catchLoc = handler && this.loc();\n        const catchEntry =\n          catchLoc && new leap.CatchEntry(catchLoc, handler.param as any);\n\n        const finallyLoc = path.node.finalizer && this.loc();\n        const finallyEntry =\n          finallyLoc && new leap.FinallyEntry(finallyLoc, after);\n\n        const tryEntry = new leap.TryEntry(\n          self.getUnmarkedCurrentLoc(),\n          catchEntry,\n          finallyEntry,\n        );\n\n        self.tryEntries.push(tryEntry);\n        self.updateContextPrevLoc(tryEntry.firstLoc);\n\n        self.leapManager.withEntry(tryEntry, () => {\n          self.explodeStatement(path.get(\"block\"));\n\n          if (catchLoc) {\n            const body = path.node.block.body;\n            if (finallyLoc) {\n              // If we have both a catch block and a finally block, then\n              // because we emit the catch block first, we need to jump over\n              // it to the finally block.\n              self.jump(finallyLoc);\n            } else if (\n              body.length &&\n              body[body.length - 1].type === \"ReturnStatement\"\n            ) {\n              after = null;\n            } else {\n              // If there is no finally block, then we need to jump over the\n              // catch block to the fall-through location.\n              self.jump(after);\n            }\n\n            self.updateContextPrevLoc(self.mark(catchLoc));\n\n            const bodyPath = path.get(\"handler.body\");\n            const safeParam = self.makeTempVar();\n            if (\n              process.env.BABEL_8_BREAKING ||\n              util.newHelpersAvailable(this.pluginPass)\n            ) {\n              this.emitAssign(safeParam, self.contextProperty(\"v\"));\n            } else {\n              self.clearPendingException(tryEntry.firstLoc, safeParam);\n            }\n\n            bodyPath.traverse(catchParamVisitor, {\n              getSafeParam: () => t.cloneNode(safeParam),\n              catchParamName:\n                // @ts-expect-error Assuming `handler.param` is `t.Identifier`\n                handler.param.name,\n            });\n\n            self.leapManager.withEntry(catchEntry, function () {\n              self.explodeStatement(bodyPath);\n            });\n          }\n\n          if (finallyLoc) {\n            self.updateContextPrevLoc(self.mark(finallyLoc));\n\n            self.leapManager.withEntry(finallyEntry, function () {\n              self.explodeStatement(path.get(\"finalizer\"));\n            });\n\n            self.emit(\n              t.returnStatement(\n                t.callExpression(\n                  self.contextProperty(\n                    process.env.BABEL_8_BREAKING ||\n                      util.newHelpersAvailable(this.pluginPass)\n                      ? \"f\"\n                      : \"finish\",\n                  ),\n                  [finallyEntry.firstLoc],\n                ),\n              ),\n            );\n          }\n        });\n\n        if (after) self.mark(after);\n\n        break;\n\n      case \"ThrowStatement\":\n        self.emit(\n          t.throwStatement(self.explodeExpression(path.get(\"argument\"))),\n        );\n\n        break;\n\n      case \"ClassDeclaration\":\n        self.emit(self.explodeClass(path));\n        break;\n\n      default:\n        throw new Error(\n          \"unknown Statement of type \" + JSON.stringify(stmt.type),\n        );\n    }\n  }\n\n  emitAbruptCompletion(record: AbruptCompletion) {\n    const abruptArgs: [t.NumericLiteral | t.StringLiteral, t.Expression?] = [\n      process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this.pluginPass)\n        ? t.numericLiteral(record.type)\n        : t.stringLiteral(\n            record.type === OperatorType.Jump ? \"continue\" : \"return\",\n          ),\n    ];\n\n    if (record.type === OperatorType.Jump) {\n      abruptArgs[1] = this.insertedLocs.has(record.target)\n        ? record.target\n        : t.cloneNode(record.target);\n    } else if (record.type === OperatorType.Return) {\n      if (record.value) {\n        abruptArgs[1] = t.cloneNode(record.value);\n      }\n    }\n\n    this.emit(\n      t.returnStatement(\n        t.callExpression(\n          this.contextProperty(\n            process.env.BABEL_8_BREAKING ||\n              util.newHelpersAvailable(this.pluginPass)\n              ? \"a\"\n              : \"abrupt\",\n          ),\n          abruptArgs,\n        ),\n      ),\n    );\n\n    if (record.type === OperatorType.Return) {\n      this.returns.add(this.listing.length);\n    }\n  }\n\n  // Not all offsets into emitter.listing are potential jump targets. For\n  // example, execution typically falls into the beginning of a try block\n  // without jumping directly there. This method returns the current offset\n  // without marking it, so that a switch case will not necessarily be\n  // generated for this offset (I say \"not necessarily\" because the same\n  // location might end up being marked in the process of emitting other\n  // statements). There's no logical harm in marking such locations as jump\n  // targets, but minimizing the number of switch cases keeps the generated\n  // code shorter.\n  getUnmarkedCurrentLoc() {\n    return t.numericLiteral(this.getIndex());\n  }\n\n  // The context.prev property takes the value of context.next whenever we\n  // evaluate the switch statement discriminant, which is generally good\n  // enough for tracking the last location we jumped to, but sometimes\n  // context.prev needs to be more precise, such as when we fall\n  // successfully out of a try block and into a finally block without\n  // jumping. This method exists to update context.prev to the freshest\n  // available location. If we were implementing a full interpreter, we\n  // would know the location of the current instruction with complete\n  // precision at all times, but we don't have that luxury here, as it would\n  // be costly and verbose to set context.prev before every statement.\n  updateContextPrevLoc(loc: t.NumericLiteral) {\n    if (loc) {\n      if (loc.value === PENDING_LOCATION) {\n        // If an uninitialized location literal was passed in, set its value\n        // to the current this.listing.length.\n        loc.value = this.getIndex();\n      } else {\n        // Otherwise assert that the location matches the current offset.\n        assert.strictEqual(loc.value, this.index);\n      }\n    } else {\n      loc = this.getUnmarkedCurrentLoc();\n    }\n\n    // Make sure context.prev is up to date in case we fell into this try\n    // statement without jumping to it. TODO Consider avoiding this\n    // assignment when we know control must have jumped here.\n    this.emitAssign(\n      this.contextProperty(\n        process.env.BABEL_8_BREAKING ||\n          util.newHelpersAvailable(this.pluginPass)\n          ? \"p\"\n          : \"prev\",\n      ),\n      loc,\n    );\n  }\n\n  // In order to save the rest of explodeExpression from a combinatorial\n  // trainwreck of special cases, explodeViaTempVar is responsible for\n  // deciding when a subexpression needs to be \"exploded,\" which is my\n  // very technical term for emitting the subexpression as an assignment\n  // to a temporary variable and the substituting the temporary variable\n  // for the original subexpression. Think of exploded view diagrams, not\n  // Michael Bay movies. The point of exploding subexpressions is to\n  // control the precise order in which the generated code realizes the\n  // side effects of those subexpressions.\n  explodeViaTempVar(\n    tempVar: t.MemberExpression | t.Identifier,\n    childPath: NodePath<t.Expression>,\n    hasLeapingChildren: boolean,\n    ignoreChildResult?: boolean,\n  ) {\n    assert.ok(\n      !ignoreChildResult || !tempVar,\n      \"Ignoring the result of a child expression but forcing it to \" +\n        \"be assigned to a temporary variable?\",\n    );\n\n    let result = this.explodeExpression(childPath, ignoreChildResult);\n\n    if (ignoreChildResult) {\n      // Side effects already emitted above.\n    } else if (tempVar || (hasLeapingChildren && !t.isLiteral(result))) {\n      // If tempVar was provided, then the result will always be assigned\n      // to it, even if the result does not otherwise need to be assigned\n      // to a temporary variable.  When no tempVar is provided, we have\n      // the flexibility to decide whether a temporary variable is really\n      // necessary.  Unfortunately, in general, a temporary variable is\n      // required whenever any child contains a yield expression, since it\n      // is difficult to prove (at all, let alone efficiently) whether\n      // this result would evaluate to the same value before and after the\n      // yield (see #206).  One narrow case where we can prove it doesn't\n      // matter (and thus we do not need a temporary variable) is when the\n      // result in question is a Literal value.\n      result = this.emitAssign(tempVar || this.makeTempVar(), result);\n    }\n    return result;\n  }\n\n  explodeExpression(\n    path: NodePath<t.Expression>,\n    ignoreResult?: boolean,\n  ): t.Expression {\n    const expr = path.node;\n    if (!expr) {\n      return expr;\n    }\n\n    const self = this;\n    let result; // Used optionally by several cases below.\n    let after;\n\n    function finish(expr: t.Expression) {\n      if (ignoreResult) {\n        self.emit(expr);\n      }\n      return expr;\n    }\n\n    // If the expression does not contain a leap, then we either emit the\n    // expression as a standalone statement or return it whole.\n    if (!meta.containsLeap(expr)) {\n      return finish(expr);\n    }\n\n    // If any child contains a leap (such as a yield or labeled continue or\n    // break statement), then any sibling subexpressions will almost\n    // certainly have to be exploded in order to maintain the order of their\n    // side effects relative to the leaping child(ren).\n    const hasLeapingChildren = meta.containsLeap.onlyChildren(expr);\n\n    // If ignoreResult is true, then we must take full responsibility for\n    // emitting the expression with all its side effects, and we should not\n    // return a result.\n\n    switch (path.type) {\n      case \"MemberExpression\":\n        return finish(\n          t.memberExpression(\n            self.explodeExpression(\n              path.get(\"object\") as NodePath<t.Expression>,\n            ),\n            path.node.computed\n              ? self.explodeViaTempVar(\n                  null,\n                  path.get(\"property\") as NodePath<t.Expression>,\n                  hasLeapingChildren,\n                )\n              : path.node.property,\n            path.node.computed,\n          ),\n        );\n\n      case \"CallExpression\":\n        const calleePath = path.get(\"callee\");\n        const argsPath = path.get(\"arguments\");\n\n        let newCallee;\n        let newArgs;\n\n        const hasLeapingArgs = argsPath.some(argPath =>\n          meta.containsLeap(argPath.node),\n        );\n\n        let injectFirstArg = null;\n\n        if (t.isMemberExpression(calleePath.node)) {\n          if (hasLeapingArgs) {\n            // If the arguments of the CallExpression contained any yield\n            // expressions, then we need to be sure to evaluate the callee\n            // before evaluating the arguments, but if the callee was a member\n            // expression, then we must be careful that the object of the\n            // member expression still gets bound to `this` for the call.\n\n            const newObject = self.explodeViaTempVar(\n              // Assign the exploded callee.object expression to a temporary\n              // variable so that we can use it twice without reevaluating it.\n              self.makeTempVar(),\n              calleePath.get(\"object\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            );\n\n            const newProperty = calleePath.node.computed\n              ? self.explodeViaTempVar(\n                  null,\n                  calleePath.get(\"property\") as NodePath<t.Expression>,\n                  hasLeapingChildren,\n                )\n              : calleePath.node.property;\n\n            injectFirstArg = newObject;\n\n            newCallee = t.memberExpression(\n              t.memberExpression(\n                t.cloneNode(newObject),\n                newProperty,\n                calleePath.node.computed,\n              ),\n              t.identifier(\"call\"),\n              false,\n            );\n          } else {\n            newCallee = self.explodeExpression(\n              calleePath as NodePath<t.Expression>,\n            );\n          }\n        } else {\n          newCallee = self.explodeViaTempVar(\n            null,\n            calleePath as NodePath<t.Expression>,\n            hasLeapingChildren,\n          );\n\n          if (t.isMemberExpression(newCallee)) {\n            // If the callee was not previously a MemberExpression, then the\n            // CallExpression was \"unqualified,\" meaning its `this` object\n            // should be the global object. If the exploded expression has\n            // become a MemberExpression (e.g. a context property, probably a\n            // temporary variable), then we need to force it to be unqualified\n            // by using the (0, object.property)(...) trick; otherwise, it\n            // will receive the object of the MemberExpression as its `this`\n            // object.\n            newCallee = t.sequenceExpression([\n              t.numericLiteral(0),\n              t.cloneNode(newCallee),\n            ]);\n          }\n        }\n\n        if (hasLeapingArgs) {\n          newArgs = argsPath.map(argPath =>\n            self.explodeViaTempVar(\n              null,\n              argPath as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n          );\n          if (injectFirstArg) newArgs.unshift(injectFirstArg);\n\n          newArgs = newArgs.map(arg => t.cloneNode(arg));\n        } else {\n          newArgs = path.node.arguments;\n        }\n\n        return finish(t.callExpression(newCallee, newArgs));\n\n      case \"NewExpression\":\n        return finish(\n          t.newExpression(\n            self.explodeViaTempVar(\n              null,\n              path.get(\"callee\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n            path.get(\"arguments\").map(function (argPath: any) {\n              return self.explodeViaTempVar(null, argPath, hasLeapingChildren);\n            }),\n          ),\n        );\n\n      case \"ObjectExpression\":\n        return finish(\n          t.objectExpression(\n            path.get(\"properties\").map(function (propPath) {\n              if (propPath.isObjectProperty()) {\n                return t.objectProperty(\n                  propPath.node.key,\n                  self.explodeViaTempVar(\n                    null,\n                    // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n                    propPath.get(\"value\"),\n                    hasLeapingChildren,\n                  ),\n                  propPath.node.computed,\n                );\n              } else {\n                return propPath.node;\n              }\n            }),\n          ),\n        );\n\n      case \"ArrayExpression\":\n        return finish(\n          t.arrayExpression(\n            path.get(\"elements\").map(function (elemPath: any) {\n              if (!elemPath.node) {\n                return null;\n              }\n              if (elemPath.isSpreadElement()) {\n                return t.spreadElement(\n                  self.explodeViaTempVar(\n                    null,\n                    elemPath.get(\"argument\"),\n                    hasLeapingChildren,\n                  ),\n                );\n              } else {\n                return self.explodeViaTempVar(\n                  null,\n                  elemPath,\n                  hasLeapingChildren,\n                );\n              }\n            }),\n          ),\n        );\n\n      case \"SequenceExpression\":\n        const lastIndex = path.node.expressions.length - 1;\n\n        path.get(\"expressions\").forEach(function (exprPath: any) {\n          if (exprPath.key === lastIndex) {\n            result = self.explodeExpression(exprPath, ignoreResult);\n          } else {\n            self.explodeExpression(exprPath, true);\n          }\n        });\n\n        return result;\n\n      case \"LogicalExpression\":\n        after = this.loc();\n\n        if (!ignoreResult) {\n          result = self.makeTempVar();\n        }\n\n        const left = self.explodeViaTempVar(\n          result,\n          path.get(\"left\"),\n          hasLeapingChildren,\n        );\n\n        if (path.node.operator === \"&&\") {\n          self.jumpIfNot(left, after);\n        } else {\n          assert.strictEqual(path.node.operator, \"||\");\n          self.jumpIf(left, after);\n        }\n\n        self.explodeViaTempVar(\n          result,\n          path.get(\"right\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n\n        self.mark(after);\n\n        return result;\n\n      case \"ConditionalExpression\":\n        const elseLoc = this.loc();\n        after = this.loc();\n        const test = self.explodeExpression(path.get(\"test\"));\n\n        self.jumpIfNot(test, elseLoc);\n\n        if (!ignoreResult) {\n          result = self.makeTempVar();\n        }\n\n        self.explodeViaTempVar(\n          result,\n          path.get(\"consequent\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n        self.jump(after);\n\n        self.mark(elseLoc);\n        self.explodeViaTempVar(\n          result,\n          path.get(\"alternate\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n\n        self.mark(after);\n\n        return result;\n\n      case \"UnaryExpression\":\n        return finish(\n          t.unaryExpression(\n            path.node.operator,\n            // Can't (and don't need to) break up the syntax of the argument.\n            // Think about delete a[b].\n            self.explodeExpression(path.get(\"argument\")),\n            !!path.node.prefix,\n          ),\n        );\n\n      case \"BinaryExpression\":\n        return finish(\n          t.binaryExpression(\n            path.node.operator,\n            self.explodeViaTempVar(\n              null,\n              path.get(\"left\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n            self.explodeViaTempVar(null, path.get(\"right\"), hasLeapingChildren),\n          ),\n        );\n\n      case \"AssignmentExpression\":\n        if (path.node.operator === \"=\") {\n          // If this is a simple assignment, the left hand side does not need\n          // to be read before the right hand side is evaluated, so we can\n          // avoid the more complicated logic below.\n          return finish(\n            t.assignmentExpression(\n              path.node.operator,\n              // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n              self.explodeExpression(path.get(\"left\")),\n              self.explodeExpression(path.get(\"right\")),\n            ),\n          );\n        }\n\n        // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n        const lhs = self.explodeExpression(path.get(\"left\"));\n        const temp = self.emitAssign(self.makeTempVar(), lhs);\n\n        // For example,\n        //\n        //   x += yield y\n        //\n        // becomes\n        //\n        //   context.t0 = x\n        //   x = context.t0 += yield y\n        //\n        // so that the left-hand side expression is read before the yield.\n        // Fixes https://github.com/facebook/regenerator/issues/345.\n\n        return finish(\n          t.assignmentExpression(\n            \"=\",\n            // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n            t.cloneNode(lhs),\n            t.assignmentExpression(\n              path.node.operator,\n              t.cloneNode(temp),\n              self.explodeExpression(path.get(\"right\")),\n            ),\n          ),\n        );\n\n      case \"UpdateExpression\":\n        return finish(\n          t.updateExpression(\n            path.node.operator,\n            self.explodeExpression(path.get(\"argument\")) as\n              | t.Identifier\n              | t.MemberExpression,\n            path.node.prefix,\n          ),\n        );\n\n      case \"YieldExpression\":\n        after = this.loc();\n        const arg =\n          path.node.argument && self.explodeExpression(path.get(\"argument\"));\n\n        if (arg && path.node.delegate) {\n          if (\n            process.env.BABEL_8_BREAKING ||\n            util.newHelpersAvailable(this.pluginPass)\n          ) {\n            const ret = t.returnStatement(\n              t.callExpression(self.contextProperty(\"d\"), [\n                t.callExpression(\n                  this.pluginPass.addHelper(\"regeneratorValues\"),\n                  [arg],\n                ),\n                after,\n              ]),\n            );\n            ret.loc = expr.loc;\n\n            self.emit(ret);\n            self.mark(after);\n\n            return self.contextProperty(\"v\");\n          } else {\n            const result = self.makeContextTempVar();\n\n            const ret = t.returnStatement(\n              t.callExpression(self.contextProperty(\"delegateYield\"), [\n                arg,\n                t.stringLiteral((result.property as t.Identifier).name),\n                after,\n              ]),\n            );\n            ret.loc = expr.loc;\n\n            self.emit(ret);\n            self.mark(after);\n\n            return result;\n          }\n        }\n\n        self.emitAssign(\n          self.contextProperty(\n            process.env.BABEL_8_BREAKING ||\n              util.newHelpersAvailable(this.pluginPass)\n              ? \"n\"\n              : \"next\",\n          ),\n          after,\n        );\n\n        const ret = t.returnStatement(t.cloneNode(arg) || null);\n        // Preserve the `yield` location so that source mappings for the statements\n        // link back to the yield properly.\n        ret.loc = expr.loc;\n        self.emit(ret);\n        self.mark(after);\n\n        return self.contextProperty(\n          process.env.BABEL_8_BREAKING ||\n            util.newHelpersAvailable(self.pluginPass)\n            ? \"v\"\n            : \"sent\",\n        );\n\n      case \"ClassExpression\":\n        return finish(self.explodeClass(path));\n\n      default:\n        throw new Error(\n          \"unknown Expression of type \" + JSON.stringify(expr.type),\n        );\n    }\n  }\n\n  explodeClass<T extends t.Class>(path: NodePath<T>): T {\n    const explodingChildren = [];\n\n    if (path.node.superClass) {\n      explodingChildren.push(path.get(\"superClass\"));\n    }\n\n    path.get(\"body.body\").forEach((member: any) => {\n      if (member.node.computed) {\n        explodingChildren.push(member.get(\"key\"));\n      }\n    });\n\n    const hasLeapingChildren = explodingChildren.some(child =>\n      meta.containsLeap(child),\n    );\n\n    for (let i = 0; i < explodingChildren.length; i++) {\n      const child = explodingChildren[i];\n      const isLast = i === explodingChildren.length - 1;\n\n      if (isLast) {\n        child.replaceWith(this.explodeExpression(child));\n      } else {\n        child.replaceWith(\n          this.explodeViaTempVar(null, child, hasLeapingChildren),\n        );\n      }\n    }\n\n    return path.node as T;\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AA2BA,MAAMK,gBAAgB,GAAGC,MAAM,CAACC,SAAS;AAEzC,SAASC,YAAYA,CAACC,IAAY,EAAE;EAClC,OAAO,IAAIC,KAAK,CACd,qDAAqD,GACnD,kDAAkD,GAClDC,IAAI,CAACC,SAAS,CAACH,IAAI,CACvB,CAAC;AACH;AAEA,MAAMI,iBAA0B,GAAG;EACjCC,UAAU,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAU,EAAE;IACtC,IAAID,IAAI,CAACN,IAAI,CAACQ,IAAI,KAAKD,KAAK,CAACE,cAAc,IAAIf,IAAI,CAACgB,WAAW,CAACJ,IAAI,CAAC,EAAE;MACrEZ,IAAI,CAACiB,mBAAmB,CAACL,IAAI,EAAEC,KAAK,CAACK,YAAY,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAEDC,KAAK,EAAE,SAAAA,CAAUP,IAAI,EAAEC,KAAU,EAAE;IACjC,IAAID,IAAI,CAACQ,KAAK,CAACC,aAAa,CAACR,KAAK,CAACE,cAAc,CAAC,EAAE;MAGlDH,IAAI,CAACU,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEM,MAAMC,OAAO,CAAC;EAkBnBC,WAAWA,CACTC,SAAuB,EACvBL,KAAY,EACZM,IAA4B,EAC5BC,UAAsB,EACtB;IAAA,KAtBFC,UAAU;IAAA,KACVH,SAAS;IAAA,KACTI,KAAK;IAAA,KACLC,QAAQ;IAAA,KACRC,OAAO;IAAA,KACPC,OAAO;IAAA,KACPC,kBAAkB,GAAW,CAAC;IAAA,KAC9BC,MAAM;IAAA,KACNC,YAAY;IAAA,KACZC,QAAQ;IAAA,KACRC,UAAU;IAAA,KACVC,WAAW;IAAA,KACXlB,KAAK;IAAA,KACLM,IAAI;IAAA,KAEJC,UAAU;IAQR,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACP,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACM,IAAI,GAAGA,IAAI;IAGhB,IAAI,CAACE,UAAU,GAAG,CAAC;IAMnB,IAAI,CAACH,SAAS,GAAGA,SAAS;IAI1B,IAAI,CAACM,OAAO,GAAG,EAAE;IAEjB,IAAI,CAACF,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,IAAIS,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,CAACP,OAAO,GAAG,IAAIQ,GAAG,CAAC,CAAC;IACxB,IAAI,CAACP,kBAAkB,GAAG,CAAC;IAI3B,IAAI,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACC,YAAY,GAAG,IAAIK,GAAG,CAAC,CAAC;IAI7B,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACK,GAAG,CAAC,CAAC;IAG1B,IAAI,CAACJ,UAAU,GAAG,EAAE;IAKpB,IAAI,CAACC,WAAW,GAAG,IAAIxC,IAAI,CAAC4C,WAAW,CAAC,IAAI,CAAC;EAC/C;EAEAD,GAAGA,CAAA,EAAG;IACJ,MAAME,CAAC,GAAGC,WAAC,CAACC,cAAc,CAAC3C,gBAAgB,CAAC;IAC5C,IAAI,CAACiC,YAAY,CAACW,GAAG,CAACH,CAAC,CAAC;IACxB,OAAOA,CAAC;EACV;EAEAI,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACZ,YAAY;EAC1B;EAEAa,YAAYA,CAAA,EAAG;IACb,OAAOJ,WAAC,CAACK,SAAS,CAAC,IAAI,CAACxB,SAAS,CAAC;EACpC;EAEAyB,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAACqB,GAAG,CAAC,IAAI,CAACpB,OAAO,CAACqB,MAAM,CAAC,EAAE;MAC3C,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,IAAI,CAACtB,OAAO,CAACqB,MAAM,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC;IACtD;IACA,OAAO,IAAI,CAACA,KAAK;EACnB;EAIAyB,IAAIA,CAACb,GAAqB,EAAE;IAC1B,IAAIA,GAAG,CAACc,KAAK,KAAKrD,gBAAgB,EAAE;MAClCuC,GAAG,CAACc,KAAK,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;IAC7B,CAAC,MAAM;MAGLM,OAAM,CAACC,WAAW,CAAChB,GAAG,CAACc,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAAC;IAC3C;IACA,IAAI,CAACK,MAAM,CAAC,IAAI,CAACH,OAAO,CAACqB,MAAM,CAAC,GAAG,IAAI;IACvC,IAAIX,GAAG,CAACc,KAAK,GAAG,IAAI,CAACtB,kBAAkB,EAAE;MACvC,IAAI,CAACA,kBAAkB,GAAGQ,GAAG,CAACc,KAAK;IACrC;IACA,OAAOd,GAAG;EACZ;EAEAiB,IAAIA,CAACpD,IAAY,EAAE;IACjB,IAAIsC,WAAC,CAACe,YAAY,CAACrD,IAAI,CAAC,EAAE;MACxBA,IAAI,GAAGsC,WAAC,CAACgB,mBAAmB,CAACtD,IAAI,CAAC;IACpC;IACAsC,WAAC,CAACiB,eAAe,CAACvD,IAAI,CAAC;IACvB,IAAI,CAACyB,OAAO,CAAC+B,IAAI,CAACxD,IAAI,CAAC;EACzB;EAIAyD,UAAUA,CACRC,GAAM,EACNC,GAAiB,EACjB;IACA,IAAI,CAACP,IAAI,CAAC,IAAI,CAACQ,MAAM,CAACF,GAAG,EAAEC,GAAG,CAAC,CAAC;IAChC,OAAOD,GAAG;EACZ;EAGAE,MAAMA,CAACF,GAAmC,EAAEC,GAAiB,EAAE;IAC7D,OAAOrB,WAAC,CAACgB,mBAAmB,CAC1BhB,WAAC,CAACuB,oBAAoB,CAAC,GAAG,EAAEvB,WAAC,CAACK,SAAS,CAACe,GAAG,CAAC,EAAEC,GAAG,CACnD,CAAC;EACH;EAIAG,eAAeA,CAACtD,IAAY,EAAE;IAC5B,MAAMuD,QAAQ,GAAGvD,IAAI,KAAK,OAAO;IACjC,OAAO8B,WAAC,CAAC0B,gBAAgB,CACvB,IAAI,CAACtB,YAAY,CAAC,CAAC,EACnBqB,QAAQ,GAAGzB,WAAC,CAAC2B,aAAa,CAACzD,IAAI,CAAC,GAAG8B,WAAC,CAAC4B,UAAU,CAAC1D,IAAI,CAAC,EACrD,CAAC,CAACuD,QACJ,CAAC;EACH;EAEAI,qBAAqBA,CACnBC,MAAwB,EACxBC,QAAwC,EACxC;IACA,MAAMC,SAAS,GAAGhC,WAAC,CAACiC,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,OAAO,CAAC,EAAE,CAChExB,WAAC,CAACK,SAAS,CAACyB,MAAM,CAAC,CACpB,CAAC;IAEF,IAAIC,QAAQ,EAAE;MACZ,IAAI,CAACZ,UAAU,CAACY,QAAQ,EAAEC,SAAS,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAAC;IACtB;EACF;EAIAE,IAAIA,CAACC,KAAmB,EAAE;IACxB,IAAI,CAAChB,UAAU,CACb,IAAI,CAACK,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC,EACDoD,KACF,CAAC;IACD,IAAI,CAACrB,IAAI,CAACd,WAAC,CAACqC,cAAc,CAAC,CAAC,CAAC;EAC/B;EAGAC,MAAMA,CAACC,IAAkB,EAAEJ,KAAuB,EAAE;IAClD,IAAI,CAACrB,IAAI,CACPd,WAAC,CAACwC,WAAW,CACXD,IAAI,EACJvC,WAAC,CAACyC,cAAc,CAAC,CACf,IAAI,CAACnB,MAAM,CACT,IAAI,CAACE,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC,EACDoD,KACF,CAAC,EACDnC,WAAC,CAACqC,cAAc,CAAC,CAAC,CACnB,CACH,CACF,CAAC;EACH;EAGAK,SAASA,CAACH,IAAkB,EAAEJ,KAAuB,EAAE;IACrD,IAAIQ,WAAW;IACf,IAAI3C,WAAC,CAAC4C,iBAAiB,CAACL,IAAI,CAAC,IAAIA,IAAI,CAACM,QAAQ,KAAK,GAAG,EAAE;MAEtDF,WAAW,GAAGJ,IAAI,CAACO,QAAQ;IAC7B,CAAC,MAAM;MACLH,WAAW,GAAG3C,WAAC,CAAC+C,eAAe,CAAC,GAAG,EAAER,IAAI,CAAC;IAC5C;IAEA,IAAI,CAACzB,IAAI,CACPd,WAAC,CAACwC,WAAW,CACXG,WAAW,EACX3C,WAAC,CAACyC,cAAc,CAAC,CACf,IAAI,CAACnB,MAAM,CACT,IAAI,CAACE,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC,EACDoD,KACF,CAAC,EACDnC,WAAC,CAACqC,cAAc,CAAC,CAAC,CACnB,CACH,CACF,CAAC;EACH;EAOAW,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACxB,eAAe,CAAC,GAAG,GAAG,IAAI,CAACxC,UAAU,EAAE,CAAC;EACtD;EAEAiE,WAAWA,CAAA,EAAG;IACZ,MAAMC,EAAE,GAAG,IAAI,CAAC1E,KAAK,CAAC2E,qBAAqB,CAAC,GAAG,CAAC;IAChD,IAAI,CAACrE,IAAI,CAACoC,IAAI,CAAClB,WAAC,CAACoD,kBAAkB,CAACF,EAAE,CAAC,CAAC;IACxC,OAAOlD,WAAC,CAACK,SAAS,CAAC6C,EAAE,CAAC;EACxB;EAEAG,kBAAkBA,CAAA,EAAG;IACnB,OAAOrD,WAAC,CAACsD,kBAAkB,CACzB,IAAI,EACJ,CAAC,IAAI,CAAClD,YAAY,CAAC,CAAC,CAAC,EACrBJ,WAAC,CAACyC,cAAc,CAAC,CAAC,IAAI,CAACc,eAAe,CAAC,CAAC,CAAC,CAAC,EAC1C,KAAK,EACL,KACF,CAAC;EACH;EAaAA,eAAeA,CAAA,EAAG;IAChB,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAMC,KAAqB,GAAG,EAAE;IAChC,IAAIC,OAAO;IAIX,IAAIC,YAAY,GAAG,KAAK;IAExBH,IAAI,CAACrE,OAAO,CAACyE,OAAO,CAAC,UAAUC,IAAI,EAAEC,CAAC,EAAE;MACtC,IAAIN,IAAI,CAAClE,MAAM,CAACwE,CAAC,CAAC,EAAE;QAClBL,KAAK,CAACvC,IAAI,CACRlB,WAAC,CAAC+D,UAAU,CAAC/D,WAAC,CAACC,cAAc,CAACuD,IAAI,CAACtE,QAAQ,CAAC8E,GAAG,CAACF,CAAC,CAAC,CAAC,EAAGJ,OAAO,GAAG,EAAG,CACrE,CAAC;QACDC,YAAY,GAAG,KAAK;MACtB;MAEA,IAAI,CAACA,YAAY,EAAE;QACjBD,OAAO,CAACxC,IAAI,CAAC2C,IAAI,CAAC;QAClB,IAAI7D,WAAC,CAACiE,qBAAqB,CAACJ,IAAI,CAAC,EAAEF,YAAY,GAAG,IAAI;MACxD;IACF,CAAC,CAAC;IAIF,IAAI,CAACnE,QAAQ,CAACmB,KAAK,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;IAErC,IAEElD,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,EACzC;MACA,IACE,IAAI,CAACM,kBAAkB,KAAK,IAAI,CAACJ,KAAK,IACtC,CAAC,IAAI,CAACG,OAAO,CAACmB,GAAG,CAAC,IAAI,CAACpB,OAAO,CAACqB,MAAM,CAAC,EACtC;QACAiD,KAAK,CAACvC,IAAI,CACRlB,WAAC,CAAC+D,UAAU,CAAC,IAAI,CAACvE,QAAQ,EAAE,CAC1BQ,WAAC,CAACkE,eAAe,CACflE,WAAC,CAACiC,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,GAAG,CAAC,EAAE,CAC1CxB,WAAC,CAACC,cAAc,EAAoB,CAAC,CACtC,CACH,CAAC,CACF,CACH,CAAC;MACH;IACF,CAAC,MAAM;MACLwD,KAAK,CAACvC,IAAI,CACRlB,WAAC,CAAC+D,UAAU,CAAC,IAAI,CAACvE,QAAQ,EAAE,EAE3B,CAAC,EAIFQ,WAAC,CAAC+D,UAAU,CAAC/D,WAAC,CAAC2B,aAAa,CAAC,KAAK,CAAC,EAAE,CAEnC3B,WAAC,CAACkE,eAAe,CAAClE,WAAC,CAACiC,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CACtE,CACH,CAAC;IACH;IAEA,OAAOxB,WAAC,CAACmE,cAAc,CACrBnE,WAAC,CAACC,cAAc,CAAC,CAAC,CAAC,EACnBD,WAAC,CAACoE,eAAe,CAEbhH,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,IAAI,CAACyC,eAAe,CAAC,GAAG,CAAC,GACzBxB,WAAC,CAACuB,oBAAoB,CACpB,GAAG,EACH,IAAI,CAACC,eAAe,CAAC,MAAM,CAAC,EAC5B,IAAI,CAACA,eAAe,CAAC,MAAM,CAC7B,CAAC,EACLiC,KACF,CACF,CAAC;EACH;EAEAY,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC5E,UAAU,CAACe,MAAM,KAAK,CAAC,EAAE;MAGhC,OAAO,IAAI;IACb;IAEA,IAAI8D,YAAY,GAAG,CAAC;IAEpB,MAAMC,eAAe,GAAGvE,WAAC,CAACuE,eAAe,CACvC,IAAI,CAAC9E,UAAU,CAAC+E,GAAG,CAAC,UAAUC,QAAQ,EAAE;MACtC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,QAAQ,CAAChE,KAAK;MAC5CC,OAAM,CAACgE,EAAE,CAACF,YAAY,IAAIJ,YAAY,EAAE,0BAA0B,CAAC;MACnEA,YAAY,GAAGI,YAAY;MAE3B,MAAMG,EAAE,GAAGJ,QAAQ,CAACK,UAAU;MAC9B,MAAMC,EAAE,GAAGN,QAAQ,CAACO,YAAY;MAEhC,MAAMC,IAAI,GAAG,CACXR,QAAQ,CAACE,QAAQ,EAEjBE,EAAE,GAAGA,EAAE,CAACF,QAAQ,GAAG,IAAI,CACxB;MAED,IAAII,EAAE,EAAE;QACNE,IAAI,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACJ,QAAQ;QACrBM,IAAI,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACG,QAAQ;MACvB;MAEA,OAAOlF,WAAC,CAACuE,eAAe,CAACU,IAAI,CAACT,GAAG,CAAC3E,GAAG,IAAIA,GAAG,IAAIG,WAAC,CAACK,SAAS,CAACR,GAAG,CAAC,CAAC,CAAC;IACpE,CAAC,CACH,CAAC;IACD,IAEEzC,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,EACzC;MACAwF,eAAe,CAACY,QAAQ,CAACC,OAAO,CAAC,CAAC;IACpC;IACA,OAAOb,eAAe;EACxB;EAQAc,OAAOA,CAACrH,IAAc,EAAEsH,YAAsB,EAAE;IAC9C,MAAM5H,IAAI,GAAGM,IAAI,CAACN,IAAI;IACtB,MAAM8F,IAAI,GAAG,IAAI;IAEjB,IAAIxD,WAAC,CAACuF,aAAa,CAAC7H,IAAI,CAAC,EAAE,MAAMD,YAAY,CAACC,IAAI,CAAC;IAEnD,IAAIM,IAAI,CAACwH,WAAW,CAAC,CAAC,EAAE,OAAOhC,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAAC;IAE1D,IAAIA,IAAI,CAAC+C,YAAY,CAAC,CAAC,EAAE,OAAOyC,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,EAAEsH,YAAY,CAAC;IAE1E,QAAQ5H,IAAI,CAACiI,IAAI;MACf,KAAK,oBAAoB;QACvB,MAAMlI,YAAY,CAACC,IAAI,CAAC;MAI1B,KAAK,gBAAgB;MACrB,KAAK,YAAY;MACjB,KAAK,aAAa;QAChB,MAAM,IAAIC,KAAK,CACbD,IAAI,CAACiI,IAAI,GAAG,2CACd,CAAC;MAEH;QACE,MAAM,IAAIhI,KAAK,CAAC,uBAAuB,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAACiI,IAAI,CAAC,CAAC;IACxE;EACF;EAEAF,gBAAgBA,CAACzH,IAA2B,EAAE4H,OAAqB,GAAG,IAAI,EAAE;IAC1E,MAAM/B,IAAI,GAAG7F,IAAI,CAACN,IAAI;IACtB,MAAM8F,IAAI,GAAG,IAAI;IACjB,IAAIqC,MAAwB,EAC1BC,KAAuB,EACvBC,IAAsB;IAIxB,IAAI/H,IAAI,CAACgI,gBAAgB,CAAC,CAAC,EAAE;MAC3BhI,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAACJ,OAAO,CAAC,UAAU5F,IAAI,EAAE;QACvCwF,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAAC;MAC7B,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAACb,IAAI,CAAC8I,YAAY,CAACpC,IAAI,CAAC,EAAE;MAM5BL,IAAI,CAAC1C,IAAI,CAAC+C,IAAI,CAAC;MACf;IACF;IAEA,QAAQ7F,IAAI,CAAC2H,IAAI;MACf,KAAK,qBAAqB;QACxBnC,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC;QACpD;MAEF,KAAK,kBAAkB;QACrB8B,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAsBlB2D,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CACxB,IAAIhJ,IAAI,CAACiJ,YAAY,CAACL,KAAK,EAAE9H,IAAI,CAACN,IAAI,CAAC0I,KAAK,CAAC,EAC7C,YAAY;UACV5C,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,EAAEhG,IAAI,CAACN,IAAI,CAAC0I,KAAK,CAAC;QAC1D,CACF,CAAC;QAED5C,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAEF,KAAK,gBAAgB;QACnBD,MAAM,GAAG,IAAI,CAAChG,GAAG,CAAC,CAAC;QACnBiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB2D,IAAI,CAAC9C,IAAI,CAACmF,MAAM,CAAC;QACjBrC,IAAI,CAACd,SAAS,CAACc,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE8B,KAAK,CAAC;QAC/DtC,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CACxB,IAAIhJ,IAAI,CAACmJ,SAAS,CAACP,KAAK,EAAED,MAAM,EAAED,OAAO,CAAC,EAC1C,YAAY;UACVpC,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QACDR,IAAI,CAACtB,IAAI,CAAC2D,MAAM,CAAC;QACjBrC,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAEF,KAAK,kBAAkB;QACrB,MAAMQ,KAAK,GAAG,IAAI,CAACzG,GAAG,CAAC,CAAC;QACxB,MAAM0C,IAAI,GAAG,IAAI,CAAC1C,GAAG,CAAC,CAAC;QACvBiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB2D,IAAI,CAAC9C,IAAI,CAAC4F,KAAK,CAAC;QAChB9C,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CACxB,IAAIhJ,IAAI,CAACmJ,SAAS,CAACP,KAAK,EAAEvD,IAAI,EAAEqD,OAAO,CAAC,EACxC,YAAY;UACVpC,IAAI,CAAC6B,OAAO,CAACrH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QAChC,CACF,CAAC;QACDR,IAAI,CAAC9C,IAAI,CAAC6B,IAAI,CAAC;QACfiB,IAAI,CAAClB,MAAM,CAACkB,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEsC,KAAK,CAAC;QAC5D9C,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAEF,KAAK,cAAc;QACjBC,IAAI,GAAG,IAAI,CAAClG,GAAG,CAAC,CAAC;QACjB,MAAM0G,MAAM,GAAG,IAAI,CAAC1G,GAAG,CAAC,CAAC;QACzBiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB,IAAI7B,IAAI,CAACN,IAAI,CAAC8I,IAAI,EAAE;UAGlBhD,IAAI,CAAC6B,OAAO,CAACrH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;QACtC;QAEAR,IAAI,CAAC9C,IAAI,CAACqF,IAAI,CAAC;QAEf,IAAI/H,IAAI,CAACN,IAAI,CAAC6E,IAAI,EAAE;UAClBiB,IAAI,CAACd,SAAS,CAACc,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE8B,KAAK,CAAC;QACjE,CAAC,MAAM,CAEP;QAEAtC,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CACxB,IAAIhJ,IAAI,CAACmJ,SAAS,CAACP,KAAK,EAAES,MAAM,EAAEX,OAAO,CAAC,EAC1C,YAAY;UACVpC,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QAEDR,IAAI,CAAC9C,IAAI,CAAC6F,MAAM,CAAC;QAEjB,IAAIvI,IAAI,CAACN,IAAI,CAAC6I,MAAM,EAAE;UAGpB/C,IAAI,CAAC6B,OAAO,CAACrH,IAAI,CAACgG,GAAG,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;QACxC;QAEAR,IAAI,CAACtB,IAAI,CAAC6D,IAAI,CAAC;QAEfvC,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAGF,KAAK,oBAAoB;QACvB,OAAOtC,IAAI,CAACkC,iBAAiB,CAAE1H,IAAI,CAASgG,GAAG,CAAC,YAAY,CAAC,CAAC;MAEhE,KAAK,gBAAgB;QACnB+B,IAAI,GAAG,IAAI,CAAClG,GAAG,CAAC,CAAC;QACjBiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB,MAAM4G,aAAa,GAAGjD,IAAI,CAACP,WAAW,CAAC,CAAC;QAExC,MAAMyD,MAAM,GAEVtJ,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACrC,IAAI,CAACA,UAAU,CAAC4H,SAAS,CAAC,iBAAiB,CAAC,GAC5CvJ,IAAI,CAACwJ,eAAe,CAAC,IAAI,CAAC7H,UAAU,EAAE,MAAM,CAAC;QAEnDyE,IAAI,CAACrC,UAAU,CACbsF,aAAa,EACbzG,WAAC,CAACiC,cAAc,CAACyE,MAAM,EAAE,CAAClD,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CACtE,CAAC;QAEDR,IAAI,CAAC9C,IAAI,CAACqF,IAAI,CAAC;QAEf,MAAMc,aAAa,GAAGrD,IAAI,CAACP,WAAW,CAAC,CAAC;QACxCO,IAAI,CAAClB,MAAM,CACTtC,WAAC,CAAC0B,gBAAgB,CAChB1B,WAAC,CAACuB,oBAAoB,CACpB,GAAG,EACHsF,aAAa,EACb7G,WAAC,CAACiC,cAAc,CAACjC,WAAC,CAACK,SAAS,CAACoG,aAAa,CAAC,EAAE,EAAE,CACjD,CAAC,EACDzG,WAAC,CAAC4B,UAAU,CAAC,MAAM,CAAC,EACpB,KACF,CAAC,EACDkE,KACF,CAAC;QAEDtC,IAAI,CAACrC,UAAU,CACbnD,IAAI,CAACN,IAAI,CAACoJ,IAAI,EACd9G,WAAC,CAAC0B,gBAAgB,CAChB1B,WAAC,CAACK,SAAS,CAACwG,aAAa,CAAC,EAC1B7G,WAAC,CAAC4B,UAAU,CAAC,OAAO,CAAC,EACrB,KACF,CACF,CAAC;QAED4B,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CACxB,IAAIhJ,IAAI,CAACmJ,SAAS,CAACP,KAAK,EAAEC,IAAI,EAAEH,OAAO,CAAC,EACxC,YAAY;UACVpC,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QAEDR,IAAI,CAACtB,IAAI,CAAC6D,IAAI,CAAC;QAEfvC,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAEF,KAAK,gBAAgB;QACnBtC,IAAI,CAACuD,oBAAoB,CAAC;UACxBpB,IAAI,GAAmB;UACvBqB,MAAM,EAAExD,IAAI,CAAC9D,WAAW,CAACuH,WAAW,CAACjJ,IAAI,CAACN,IAAI,CAAC0I,KAAK;QACtD,CAAC,CAAC;QAEF;MAEF,KAAK,mBAAmB;QACtB5C,IAAI,CAACuD,oBAAoB,CAAC;UACxBpB,IAAI,GAAmB;UACvBqB,MAAM,EAAExD,IAAI,CAAC9D,WAAW,CAACwH,cAAc,CAAClJ,IAAI,CAACN,IAAI,CAAC0I,KAAK;QACzD,CAAC,CAAC;QAEF;MAEF,KAAK,iBAAiB;QAGpB,MAAMe,IAAI,GAAG3D,IAAI,CAACrC,UAAU,CAC1BqC,IAAI,CAACP,WAAW,CAAC,CAAC,EAClBO,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,cAAc,CAAC,CACjD,CAAC;QAED8B,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAClB,MAAMuH,UAAU,GAAG,IAAI,CAACvH,GAAG,CAAC,CAAC;QAC7B,IAAIwH,SAAuB,GAAGD,UAAU;QACxC,MAAME,QAA4B,GAAG,EAAE;QAGvC,MAAM7D,KAAK,GAAGzF,IAAI,CAACN,IAAI,CAAC+F,KAAK,IAAI,EAAE;QAEnC,KAAK,IAAIK,CAAC,GAAGL,KAAK,CAACjD,MAAM,GAAG,CAAC,EAAEsD,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAC1C,MAAMyD,CAAC,GAAG9D,KAAK,CAACK,CAAC,CAAC;UAElB,IAAIyD,CAAC,CAAChF,IAAI,EAAE;YACV8E,SAAS,GAAGrH,WAAC,CAACwH,qBAAqB,CACjCxH,WAAC,CAACyH,gBAAgB,CAAC,KAAK,EAAEzH,WAAC,CAACK,SAAS,CAAC8G,IAAI,CAAC,EAAEI,CAAC,CAAChF,IAAI,CAAC,EACnD+E,QAAQ,CAACxD,CAAC,CAAC,GAAG,IAAI,CAACjE,GAAG,CAAC,CAAC,EACzBwH,SACF,CAAC;UACH,CAAC,MAAM;YACLC,QAAQ,CAACxD,CAAC,CAAC,GAAGsD,UAAU;UAC1B;QACF;QAEA,MAAMM,YAAY,GAAG1J,IAAI,CAACgG,GAAG,CAAC,cAAc,CAAC;QAC7C5G,IAAI,CAACiB,mBAAmB,CAACqJ,YAAY,EAAEL,SAAS,CAAC;QACjD7D,IAAI,CAACtB,IAAI,CAACsB,IAAI,CAACkC,iBAAiB,CAACgC,YAAY,CAAC,CAAC;QAE/ClE,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CAAC,IAAIhJ,IAAI,CAACyK,WAAW,CAAC7B,KAAK,CAAC,EAAE,YAAY;UAClE9H,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,CAACJ,OAAO,CAAC,UAAUgE,QAAQ,EAAE;YAC5C,MAAM9D,CAAC,GAAG8D,QAAQ,CAACC,GAAa;YAChCrE,IAAI,CAAC9C,IAAI,CAAC4G,QAAQ,CAACxD,CAAC,CAAC,CAAC;YAEtB8D,QAAQ,CAAC5D,GAAG,CAAC,YAAY,CAAC,CAACJ,OAAO,CAAC,UAAU5F,IAAI,EAAE;cACjDwF,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAAC;YAC7B,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFwF,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAChB,IAAIsB,UAAU,CAACzG,KAAK,KAAKrD,gBAAgB,EAAE;UACzCkG,IAAI,CAAC9C,IAAI,CAAC0G,UAAU,CAAC;UACrBxG,OAAM,CAACC,WAAW,CAACiF,KAAK,CAACnF,KAAK,EAAEyG,UAAU,CAACzG,KAAK,CAAC;QACnD;QAEA;MAEF,KAAK,aAAa;QAChB,MAAMmH,OAAO,GAAG9J,IAAI,CAACN,IAAI,CAACqK,SAAS,IAAI,IAAI,CAAClI,GAAG,CAAC,CAAC;QACjDiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB2D,IAAI,CAACd,SAAS,CACZc,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC,EACxC8D,OAAO,IAAIhC,KACb,CAAC;QAEDtC,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,YAAY,CAAC,CAAC;QAE7C,IAAI8D,OAAO,EAAE;UACXtE,IAAI,CAACtB,IAAI,CAAC4D,KAAK,CAAC;UAChBtC,IAAI,CAAC9C,IAAI,CAACoH,OAAO,CAAC;UAClBtE,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9C;QAEAR,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB;MAEF,KAAK,iBAAiB;QACpBtC,IAAI,CAACuD,oBAAoB,CAAC;UACxBpB,IAAI,GAAqB;UACzBhF,KAAK,EAAE6C,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC;QACpD,CAAC,CAAC;QAEF;MAEF,KAAK,eAAe;QAClB,MAAM,IAAIrG,KAAK,CAAC,qDAAqD,CAAC;MAExE,KAAK,cAAc;QACjBmI,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB,MAAMmI,OAAO,GAAGhK,IAAI,CAACN,IAAI,CAACsK,OAAO;QAEjC,MAAMC,QAAQ,GAAGD,OAAO,IAAI,IAAI,CAACnI,GAAG,CAAC,CAAC;QACtC,MAAMiF,UAAU,GACdmD,QAAQ,IAAI,IAAI/K,IAAI,CAACgL,UAAU,CAACD,QAAQ,EAAED,OAAO,CAACG,KAAY,CAAC;QAEjE,MAAMC,UAAU,GAAGpK,IAAI,CAACN,IAAI,CAAC2K,SAAS,IAAI,IAAI,CAACxI,GAAG,CAAC,CAAC;QACpD,MAAMmF,YAAY,GAChBoD,UAAU,IAAI,IAAIlL,IAAI,CAACoL,YAAY,CAACF,UAAU,EAAEtC,KAAK,CAAC;QAExD,MAAMrB,QAAQ,GAAG,IAAIvH,IAAI,CAACqL,QAAQ,CAChC/E,IAAI,CAACgF,qBAAqB,CAAC,CAAC,EAC5B1D,UAAU,EACVE,YACF,CAAC;QAEDxB,IAAI,CAAC/D,UAAU,CAACyB,IAAI,CAACuD,QAAQ,CAAC;QAC9BjB,IAAI,CAACiF,oBAAoB,CAAChE,QAAQ,CAACE,QAAQ,CAAC;QAE5CnB,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CAACzB,QAAQ,EAAE,MAAM;UACzCjB,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,CAAC;UAExC,IAAIiE,QAAQ,EAAE;YACZ,MAAMS,IAAI,GAAG1K,IAAI,CAACN,IAAI,CAACiL,KAAK,CAACD,IAAI;YACjC,IAAIN,UAAU,EAAE;cAId5E,IAAI,CAACtB,IAAI,CAACkG,UAAU,CAAC;YACvB,CAAC,MAAM,IACLM,IAAI,CAAClI,MAAM,IACXkI,IAAI,CAACA,IAAI,CAAClI,MAAM,GAAG,CAAC,CAAC,CAACmF,IAAI,KAAK,iBAAiB,EAChD;cACAG,KAAK,GAAG,IAAI;YACd,CAAC,MAAM;cAGLtC,IAAI,CAACtB,IAAI,CAAC4D,KAAK,CAAC;YAClB;YAEAtC,IAAI,CAACiF,oBAAoB,CAACjF,IAAI,CAAC9C,IAAI,CAACuH,QAAQ,CAAC,CAAC;YAE9C,MAAMW,QAAQ,GAAG5K,IAAI,CAACgG,GAAG,CAAC,cAAc,CAAC;YACzC,MAAM6E,SAAS,GAAGrF,IAAI,CAACP,WAAW,CAAC,CAAC;YACpC,IAEE7F,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,EACzC;cACA,IAAI,CAACoC,UAAU,CAAC0H,SAAS,EAAErF,IAAI,CAAChC,eAAe,CAAC,GAAG,CAAC,CAAC;YACvD,CAAC,MAAM;cACLgC,IAAI,CAAC3B,qBAAqB,CAAC4C,QAAQ,CAACE,QAAQ,EAAEkE,SAAS,CAAC;YAC1D;YAEAD,QAAQ,CAACE,QAAQ,CAAChL,iBAAiB,EAAE;cACnCQ,YAAY,EAAEA,CAAA,KAAM0B,WAAC,CAACK,SAAS,CAACwI,SAAS,CAAC;cAC1C1K,cAAc,EAEZ6J,OAAO,CAACG,KAAK,CAACjK;YAClB,CAAC,CAAC;YAEFsF,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CAACpB,UAAU,EAAE,YAAY;cACjDtB,IAAI,CAACiC,gBAAgB,CAACmD,QAAQ,CAAC;YACjC,CAAC,CAAC;UACJ;UAEA,IAAIR,UAAU,EAAE;YACd5E,IAAI,CAACiF,oBAAoB,CAACjF,IAAI,CAAC9C,IAAI,CAAC0H,UAAU,CAAC,CAAC;YAEhD5E,IAAI,CAAC9D,WAAW,CAACwG,SAAS,CAAClB,YAAY,EAAE,YAAY;cACnDxB,IAAI,CAACiC,gBAAgB,CAACzH,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEFR,IAAI,CAAC1C,IAAI,CACPd,WAAC,CAACkE,eAAe,CACflE,WAAC,CAACiC,cAAc,CACduB,IAAI,CAAChC,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,QACN,CAAC,EACD,CAACiG,YAAY,CAACL,QAAQ,CACxB,CACF,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEF,IAAImB,KAAK,EAAEtC,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAE3B;MAEF,KAAK,gBAAgB;QACnBtC,IAAI,CAAC1C,IAAI,CACPd,WAAC,CAAC+I,cAAc,CAACvF,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,CAAC,CAC/D,CAAC;QAED;MAEF,KAAK,kBAAkB;QACrBR,IAAI,CAAC1C,IAAI,CAAC0C,IAAI,CAACwF,YAAY,CAAChL,IAAI,CAAC,CAAC;QAClC;MAEF;QACE,MAAM,IAAIL,KAAK,CACb,4BAA4B,GAAGC,IAAI,CAACC,SAAS,CAACgG,IAAI,CAAC8B,IAAI,CACzD,CAAC;IACL;EACF;EAEAoB,oBAAoBA,CAACkC,MAAwB,EAAE;IAC7C,MAAMC,UAA+D,GAAG,CACtC9L,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACrEiB,WAAC,CAACC,cAAc,CAACgJ,MAAM,CAACtD,IAAI,CAAC,GAC7B3F,WAAC,CAAC2B,aAAa,CACbsH,MAAM,CAACtD,IAAI,MAAsB,GAAG,UAAU,GAAG,QACnD,CAAC,CACN;IAED,IAAIsD,MAAM,CAACtD,IAAI,MAAsB,EAAE;MACrCuD,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC3J,YAAY,CAACgB,GAAG,CAAC0I,MAAM,CAACjC,MAAM,CAAC,GAChDiC,MAAM,CAACjC,MAAM,GACbhH,WAAC,CAACK,SAAS,CAAC4I,MAAM,CAACjC,MAAM,CAAC;IAChC,CAAC,MAAM,IAAIiC,MAAM,CAACtD,IAAI,MAAwB,EAAE;MAC9C,IAAIsD,MAAM,CAACtI,KAAK,EAAE;QAChBuI,UAAU,CAAC,CAAC,CAAC,GAAGlJ,WAAC,CAACK,SAAS,CAAC4I,MAAM,CAACtI,KAAK,CAAC;MAC3C;IACF;IAEA,IAAI,CAACG,IAAI,CACPd,WAAC,CAACkE,eAAe,CACflE,WAAC,CAACiC,cAAc,CACd,IAAI,CAACT,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,QACN,CAAC,EACDmK,UACF,CACF,CACF,CAAC;IAED,IAAID,MAAM,CAACtD,IAAI,MAAwB,EAAE;MACvC,IAAI,CAACvG,OAAO,CAACc,GAAG,CAAC,IAAI,CAACf,OAAO,CAACqB,MAAM,CAAC;IACvC;EACF;EAWAgI,qBAAqBA,CAAA,EAAG;IACtB,OAAOxI,WAAC,CAACC,cAAc,CAAC,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC;EAC1C;EAYAmI,oBAAoBA,CAAC5I,GAAqB,EAAE;IAC1C,IAAIA,GAAG,EAAE;MACP,IAAIA,GAAG,CAACc,KAAK,KAAKrD,gBAAgB,EAAE;QAGlCuC,GAAG,CAACc,KAAK,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MAC7B,CAAC,MAAM;QAELM,OAAM,CAACC,WAAW,CAAChB,GAAG,CAACc,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAAC;MAC3C;IACF,CAAC,MAAM;MACLY,GAAG,GAAG,IAAI,CAAC2I,qBAAqB,CAAC,CAAC;IACpC;IAKA,IAAI,CAACrH,UAAU,CACb,IAAI,CAACK,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC,EACDc,GACF,CAAC;EACH;EAWAsJ,iBAAiBA,CACfC,OAA0C,EAC1CC,SAAiC,EACjCC,kBAA2B,EAC3BC,iBAA2B,EAC3B;IACA3I,OAAM,CAACgE,EAAE,CACP,CAAC2E,iBAAiB,IAAI,CAACH,OAAO,EAC9B,8DAA8D,GAC5D,sCACJ,CAAC;IAED,IAAII,MAAM,GAAG,IAAI,CAAC9D,iBAAiB,CAAC2D,SAAS,EAAEE,iBAAiB,CAAC;IAEjE,IAAIA,iBAAiB,EAAE,CAEvB,CAAC,MAAM,IAAIH,OAAO,IAAKE,kBAAkB,IAAI,CAACtJ,WAAC,CAACyJ,SAAS,CAACD,MAAM,CAAE,EAAE;MAYlEA,MAAM,GAAG,IAAI,CAACrI,UAAU,CAACiI,OAAO,IAAI,IAAI,CAACnG,WAAW,CAAC,CAAC,EAAEuG,MAAM,CAAC;IACjE;IACA,OAAOA,MAAM;EACf;EAEA9D,iBAAiBA,CACf1H,IAA4B,EAC5BsH,YAAsB,EACR;IACd,MAAMoE,IAAI,GAAG1L,IAAI,CAACN,IAAI;IACtB,IAAI,CAACgM,IAAI,EAAE;MACT,OAAOA,IAAI;IACb;IAEA,MAAMlG,IAAI,GAAG,IAAI;IACjB,IAAIgG,MAAM;IACV,IAAI1D,KAAK;IAET,SAAS6D,MAAMA,CAACD,IAAkB,EAAE;MAClC,IAAIpE,YAAY,EAAE;QAChB9B,IAAI,CAAC1C,IAAI,CAAC4I,IAAI,CAAC;MACjB;MACA,OAAOA,IAAI;IACb;IAIA,IAAI,CAACvM,IAAI,CAAC8I,YAAY,CAACyD,IAAI,CAAC,EAAE;MAC5B,OAAOC,MAAM,CAACD,IAAI,CAAC;IACrB;IAMA,MAAMJ,kBAAkB,GAAGnM,IAAI,CAAC8I,YAAY,CAAC2D,YAAY,CAACF,IAAI,CAAC;IAM/D,QAAQ1L,IAAI,CAAC2H,IAAI;MACf,KAAK,kBAAkB;QACrB,OAAOgE,MAAM,CACX3J,WAAC,CAAC0B,gBAAgB,CAChB8B,IAAI,CAACkC,iBAAiB,CACpB1H,IAAI,CAACgG,GAAG,CAAC,QAAQ,CACnB,CAAC,EACDhG,IAAI,CAACN,IAAI,CAAC+D,QAAQ,GACd+B,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJnL,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,EACpBsF,kBACF,CAAC,GACDtL,IAAI,CAACN,IAAI,CAACmM,QAAQ,EACtB7L,IAAI,CAACN,IAAI,CAAC+D,QACZ,CACF,CAAC;MAEH,KAAK,gBAAgB;QACnB,MAAMqI,UAAU,GAAG9L,IAAI,CAACgG,GAAG,CAAC,QAAQ,CAAC;QACrC,MAAM+F,QAAQ,GAAG/L,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC;QAEtC,IAAIgG,SAAS;QACb,IAAIC,OAAO;QAEX,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,OAAO,IAC1CjN,IAAI,CAAC8I,YAAY,CAACmE,OAAO,CAAC1M,IAAI,CAChC,CAAC;QAED,IAAI2M,cAAc,GAAG,IAAI;QAEzB,IAAIrK,WAAC,CAACsK,kBAAkB,CAACR,UAAU,CAACpM,IAAI,CAAC,EAAE;UACzC,IAAIwM,cAAc,EAAE;YAOlB,MAAMK,SAAS,GAAG/G,IAAI,CAAC2F,iBAAiB,CAGtC3F,IAAI,CAACP,WAAW,CAAC,CAAC,EAClB6G,UAAU,CAAC9F,GAAG,CAAC,QAAQ,CAAC,EACxBsF,kBACF,CAAC;YAED,MAAMkB,WAAW,GAAGV,UAAU,CAACpM,IAAI,CAAC+D,QAAQ,GACxC+B,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJW,UAAU,CAAC9F,GAAG,CAAC,UAAU,CAAC,EAC1BsF,kBACF,CAAC,GACDQ,UAAU,CAACpM,IAAI,CAACmM,QAAQ;YAE5BQ,cAAc,GAAGE,SAAS;YAE1BP,SAAS,GAAGhK,WAAC,CAAC0B,gBAAgB,CAC5B1B,WAAC,CAAC0B,gBAAgB,CAChB1B,WAAC,CAACK,SAAS,CAACkK,SAAS,CAAC,EACtBC,WAAW,EACXV,UAAU,CAACpM,IAAI,CAAC+D,QAClB,CAAC,EACDzB,WAAC,CAAC4B,UAAU,CAAC,MAAM,CAAC,EACpB,KACF,CAAC;UACH,CAAC,MAAM;YACLoI,SAAS,GAAGxG,IAAI,CAACkC,iBAAiB,CAChCoE,UACF,CAAC;UACH;QACF,CAAC,MAAM;UACLE,SAAS,GAAGxG,IAAI,CAAC2F,iBAAiB,CAChC,IAAI,EACJW,UAAU,EACVR,kBACF,CAAC;UAED,IAAItJ,WAAC,CAACsK,kBAAkB,CAACN,SAAS,CAAC,EAAE;YASnCA,SAAS,GAAGhK,WAAC,CAACyK,kBAAkB,CAAC,CAC/BzK,WAAC,CAACC,cAAc,CAAC,CAAC,CAAC,EACnBD,WAAC,CAACK,SAAS,CAAC2J,SAAS,CAAC,CACvB,CAAC;UACJ;QACF;QAEA,IAAIE,cAAc,EAAE;UAClBD,OAAO,GAAGF,QAAQ,CAACvF,GAAG,CAAC4F,OAAO,IAC5B5G,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJiB,OAAO,EACPd,kBACF,CACF,CAAC;UACD,IAAIe,cAAc,EAAEJ,OAAO,CAACS,OAAO,CAACL,cAAc,CAAC;UAEnDJ,OAAO,GAAGA,OAAO,CAACzF,GAAG,CAACmG,GAAG,IAAI3K,WAAC,CAACK,SAAS,CAACsK,GAAG,CAAC,CAAC;QAChD,CAAC,MAAM;UACLV,OAAO,GAAGjM,IAAI,CAACN,IAAI,CAACkN,SAAS;QAC/B;QAEA,OAAOjB,MAAM,CAAC3J,WAAC,CAACiC,cAAc,CAAC+H,SAAS,EAAEC,OAAO,CAAC,CAAC;MAErD,KAAK,eAAe;QAClB,OAAON,MAAM,CACX3J,WAAC,CAAC6K,aAAa,CACbrH,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJnL,IAAI,CAACgG,GAAG,CAAC,QAAQ,CAAC,EAClBsF,kBACF,CAAC,EACDtL,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC,CAACQ,GAAG,CAAC,UAAU4F,OAAY,EAAE;UAChD,OAAO5G,IAAI,CAAC2F,iBAAiB,CAAC,IAAI,EAAEiB,OAAO,EAAEd,kBAAkB,CAAC;QAClE,CAAC,CACH,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAOK,MAAM,CACX3J,WAAC,CAAC8K,gBAAgB,CAChB9M,IAAI,CAACgG,GAAG,CAAC,YAAY,CAAC,CAACQ,GAAG,CAAC,UAAUuG,QAAQ,EAAE;UAC7C,IAAIA,QAAQ,CAACC,gBAAgB,CAAC,CAAC,EAAE;YAC/B,OAAOhL,WAAC,CAACiL,cAAc,CACrBF,QAAQ,CAACrN,IAAI,CAACmK,GAAG,EACjBrE,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EAEJ4B,QAAQ,CAAC/G,GAAG,CAAC,OAAO,CAAC,EACrBsF,kBACF,CAAC,EACDyB,QAAQ,CAACrN,IAAI,CAAC+D,QAChB,CAAC;UACH,CAAC,MAAM;YACL,OAAOsJ,QAAQ,CAACrN,IAAI;UACtB;QACF,CAAC,CACH,CACF,CAAC;MAEH,KAAK,iBAAiB;QACpB,OAAOiM,MAAM,CACX3J,WAAC,CAACuE,eAAe,CACfvG,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,CAACQ,GAAG,CAAC,UAAU0G,QAAa,EAAE;UAChD,IAAI,CAACA,QAAQ,CAACxN,IAAI,EAAE;YAClB,OAAO,IAAI;UACb;UACA,IAAIwN,QAAQ,CAACC,eAAe,CAAC,CAAC,EAAE;YAC9B,OAAOnL,WAAC,CAACoL,aAAa,CACpB5H,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJ+B,QAAQ,CAAClH,GAAG,CAAC,UAAU,CAAC,EACxBsF,kBACF,CACF,CAAC;UACH,CAAC,MAAM;YACL,OAAO9F,IAAI,CAAC2F,iBAAiB,CAC3B,IAAI,EACJ+B,QAAQ,EACR5B,kBACF,CAAC;UACH;QACF,CAAC,CACH,CACF,CAAC;MAEH,KAAK,oBAAoB;QACvB,MAAM+B,SAAS,GAAGrN,IAAI,CAACN,IAAI,CAAC4N,WAAW,CAAC9K,MAAM,GAAG,CAAC;QAElDxC,IAAI,CAACgG,GAAG,CAAC,aAAa,CAAC,CAACJ,OAAO,CAAC,UAAU2H,QAAa,EAAE;UACvD,IAAIA,QAAQ,CAAC1D,GAAG,KAAKwD,SAAS,EAAE;YAC9B7B,MAAM,GAAGhG,IAAI,CAACkC,iBAAiB,CAAC6F,QAAQ,EAAEjG,YAAY,CAAC;UACzD,CAAC,MAAM;YACL9B,IAAI,CAACkC,iBAAiB,CAAC6F,QAAQ,EAAE,IAAI,CAAC;UACxC;QACF,CAAC,CAAC;QAEF,OAAO/B,MAAM;MAEf,KAAK,mBAAmB;QACtB1D,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAElB,IAAI,CAACyF,YAAY,EAAE;UACjBkE,MAAM,GAAGhG,IAAI,CAACP,WAAW,CAAC,CAAC;QAC7B;QAEA,MAAM6D,IAAI,GAAGtD,IAAI,CAAC2F,iBAAiB,CACjCK,MAAM,EACNxL,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,EAChBsF,kBACF,CAAC;QAED,IAAItL,IAAI,CAACN,IAAI,CAACmF,QAAQ,KAAK,IAAI,EAAE;UAC/BW,IAAI,CAACd,SAAS,CAACoE,IAAI,EAAEhB,KAAK,CAAC;QAC7B,CAAC,MAAM;UACLlF,OAAM,CAACC,WAAW,CAAC7C,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAAE,IAAI,CAAC;UAC5CW,IAAI,CAAClB,MAAM,CAACwE,IAAI,EAAEhB,KAAK,CAAC;QAC1B;QAEAtC,IAAI,CAAC2F,iBAAiB,CACpBK,MAAM,EACNxL,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,EACjBsF,kBAAkB,EAClBhE,YACF,CAAC;QAED9B,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB,OAAO0D,MAAM;MAEf,KAAK,uBAAuB;QAC1B,MAAM1B,OAAO,GAAG,IAAI,CAACjI,GAAG,CAAC,CAAC;QAC1BiG,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAClB,MAAM0C,IAAI,GAAGiB,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QAErDR,IAAI,CAACd,SAAS,CAACH,IAAI,EAAEuF,OAAO,CAAC;QAE7B,IAAI,CAACxC,YAAY,EAAE;UACjBkE,MAAM,GAAGhG,IAAI,CAACP,WAAW,CAAC,CAAC;QAC7B;QAEAO,IAAI,CAAC2F,iBAAiB,CACpBK,MAAM,EACNxL,IAAI,CAACgG,GAAG,CAAC,YAAY,CAAC,EACtBsF,kBAAkB,EAClBhE,YACF,CAAC;QACD9B,IAAI,CAACtB,IAAI,CAAC4D,KAAK,CAAC;QAEhBtC,IAAI,CAAC9C,IAAI,CAACoH,OAAO,CAAC;QAClBtE,IAAI,CAAC2F,iBAAiB,CACpBK,MAAM,EACNxL,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC,EACrBsF,kBAAkB,EAClBhE,YACF,CAAC;QAED9B,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB,OAAO0D,MAAM;MAEf,KAAK,iBAAiB;QACpB,OAAOG,MAAM,CACX3J,WAAC,CAAC+C,eAAe,CACf/E,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAGlBW,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,CAAC,EAC5C,CAAC,CAAChG,IAAI,CAACN,IAAI,CAAC8N,MACd,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAO7B,MAAM,CACX3J,WAAC,CAACyH,gBAAgB,CAChBzJ,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAClBW,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,EACJnL,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,EAChBsF,kBACF,CAAC,EACD9F,IAAI,CAAC2F,iBAAiB,CAAC,IAAI,EAAEnL,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,EAAEsF,kBAAkB,CACpE,CACF,CAAC;MAEH,KAAK,sBAAsB;QACzB,IAAItL,IAAI,CAACN,IAAI,CAACmF,QAAQ,KAAK,GAAG,EAAE;UAI9B,OAAO8G,MAAM,CACX3J,WAAC,CAACuB,oBAAoB,CACpBvD,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAElBW,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC,EACxCR,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,CAC1C,CACF,CAAC;QACH;QAGA,MAAM5C,GAAG,GAAGoC,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,MAAMyH,IAAI,GAAGjI,IAAI,CAACrC,UAAU,CAACqC,IAAI,CAACP,WAAW,CAAC,CAAC,EAAE7B,GAAG,CAAC;QAcrD,OAAOuI,MAAM,CACX3J,WAAC,CAACuB,oBAAoB,CACpB,GAAG,EAEHvB,WAAC,CAACK,SAAS,CAACe,GAAG,CAAC,EAChBpB,WAAC,CAACuB,oBAAoB,CACpBvD,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAClB7C,WAAC,CAACK,SAAS,CAACoL,IAAI,CAAC,EACjBjI,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,OAAO,CAAC,CAC1C,CACF,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAO2F,MAAM,CACX3J,WAAC,CAAC0L,gBAAgB,CAChB1N,IAAI,CAACN,IAAI,CAACmF,QAAQ,EAClBW,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,CAAC,EAG5ChG,IAAI,CAACN,IAAI,CAAC8N,MACZ,CACF,CAAC;MAEH,KAAK,iBAAiB;QACpB1F,KAAK,GAAG,IAAI,CAACjG,GAAG,CAAC,CAAC;QAClB,MAAM8K,GAAG,GACP3M,IAAI,CAACN,IAAI,CAACoF,QAAQ,IAAIU,IAAI,CAACkC,iBAAiB,CAAC1H,IAAI,CAACgG,GAAG,CAAC,UAAU,CAAC,CAAC;QAEpE,IAAI2G,GAAG,IAAI3M,IAAI,CAACN,IAAI,CAACiO,QAAQ,EAAE;UAC7B,IAEEvO,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,EACzC;YACA,MAAM6M,GAAG,GAAG5L,WAAC,CAACkE,eAAe,CAC3BlE,WAAC,CAACiC,cAAc,CAACuB,IAAI,CAAChC,eAAe,CAAC,GAAG,CAAC,EAAE,CAC1CxB,WAAC,CAACiC,cAAc,CACd,IAAI,CAAClD,UAAU,CAAC4H,SAAS,CAAC,mBAAmB,CAAC,EAC9C,CAACgE,GAAG,CACN,CAAC,EACD7E,KAAK,CACN,CACH,CAAC;YACD8F,GAAG,CAAC/L,GAAG,GAAG6J,IAAI,CAAC7J,GAAG;YAElB2D,IAAI,CAAC1C,IAAI,CAAC8K,GAAG,CAAC;YACdpI,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;YAEhB,OAAOtC,IAAI,CAAChC,eAAe,CAAC,GAAG,CAAC;UAClC,CAAC,MAAM;YACL,MAAMgI,MAAM,GAAGhG,IAAI,CAACR,kBAAkB,CAAC,CAAC;YAExC,MAAM4I,GAAG,GAAG5L,WAAC,CAACkE,eAAe,CAC3BlE,WAAC,CAACiC,cAAc,CAACuB,IAAI,CAAChC,eAAe,CAAC,eAAe,CAAC,EAAE,CACtDmJ,GAAG,EACH3K,WAAC,CAAC2B,aAAa,CAAE6H,MAAM,CAACK,QAAQ,CAAkB3L,IAAI,CAAC,EACvD4H,KAAK,CACN,CACH,CAAC;YACD8F,GAAG,CAAC/L,GAAG,GAAG6J,IAAI,CAAC7J,GAAG;YAElB2D,IAAI,CAAC1C,IAAI,CAAC8K,GAAG,CAAC;YACdpI,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;YAEhB,OAAO0D,MAAM;UACf;QACF;QAEAhG,IAAI,CAACrC,UAAU,CACbqC,IAAI,CAAChC,eAAe,CAEhBpE,IAAI,CAACgF,mBAAmB,CAAC,IAAI,CAACrD,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC,EACD+G,KACF,CAAC;QAED,MAAM8F,GAAG,GAAG5L,WAAC,CAACkE,eAAe,CAAClE,WAAC,CAACK,SAAS,CAACsK,GAAG,CAAC,IAAI,IAAI,CAAC;QAGvDiB,GAAG,CAAC/L,GAAG,GAAG6J,IAAI,CAAC7J,GAAG;QAClB2D,IAAI,CAAC1C,IAAI,CAAC8K,GAAG,CAAC;QACdpI,IAAI,CAAC9C,IAAI,CAACoF,KAAK,CAAC;QAEhB,OAAOtC,IAAI,CAAChC,eAAe,CAEvBpE,IAAI,CAACgF,mBAAmB,CAACoB,IAAI,CAACzE,UAAU,CAAC,GACvC,GAAG,GACH,MACN,CAAC;MAEH,KAAK,iBAAiB;QACpB,OAAO4K,MAAM,CAACnG,IAAI,CAACwF,YAAY,CAAChL,IAAI,CAAC,CAAC;MAExC;QACE,MAAM,IAAIL,KAAK,CACb,6BAA6B,GAAGC,IAAI,CAACC,SAAS,CAAC6L,IAAI,CAAC/D,IAAI,CAC1D,CAAC;IACL;EACF;EAEAqD,YAAYA,CAAoBhL,IAAiB,EAAK;IACpD,MAAM6N,iBAAiB,GAAG,EAAE;IAE5B,IAAI7N,IAAI,CAACN,IAAI,CAACoO,UAAU,EAAE;MACxBD,iBAAiB,CAAC3K,IAAI,CAAClD,IAAI,CAACgG,GAAG,CAAC,YAAY,CAAC,CAAC;IAChD;IAEAhG,IAAI,CAACgG,GAAG,CAAC,WAAW,CAAC,CAACJ,OAAO,CAAEmI,MAAW,IAAK;MAC7C,IAAIA,MAAM,CAACrO,IAAI,CAAC+D,QAAQ,EAAE;QACxBoK,iBAAiB,CAAC3K,IAAI,CAAC6K,MAAM,CAAC/H,GAAG,CAAC,KAAK,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,MAAMsF,kBAAkB,GAAGuC,iBAAiB,CAAC1B,IAAI,CAAC6B,KAAK,IACrD7O,IAAI,CAAC8I,YAAY,CAAC+F,KAAK,CACzB,CAAC;IAED,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+H,iBAAiB,CAACrL,MAAM,EAAEsD,CAAC,EAAE,EAAE;MACjD,MAAMkI,KAAK,GAAGH,iBAAiB,CAAC/H,CAAC,CAAC;MAClC,MAAMmI,MAAM,GAAGnI,CAAC,KAAK+H,iBAAiB,CAACrL,MAAM,GAAG,CAAC;MAEjD,IAAIyL,MAAM,EAAE;QACVD,KAAK,CAACE,WAAW,CAAC,IAAI,CAACxG,iBAAiB,CAACsG,KAAK,CAAC,CAAC;MAClD,CAAC,MAAM;QACLA,KAAK,CAACE,WAAW,CACf,IAAI,CAAC/C,iBAAiB,CAAC,IAAI,EAAE6C,KAAK,EAAE1C,kBAAkB,CACxD,CAAC;MACH;IACF;IAEA,OAAOtL,IAAI,CAACN,IAAI;EAClB;AACF;AAACyO,OAAA,CAAAxN,OAAA,GAAAA,OAAA", "ignoreList": []}