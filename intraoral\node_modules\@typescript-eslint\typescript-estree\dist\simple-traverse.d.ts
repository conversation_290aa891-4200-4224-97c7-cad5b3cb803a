import type { TSESTree } from './ts-estree';
type SimpleTraverseOptions = {
    enter: (node: TSESTree.Node, parent: TSESTree.Node | undefined) => void;
} | {
    [key: string]: (node: TSESTree.Node, parent: TSESTree.Node | undefined) => void;
};
export declare function simpleTraverse(startingNode: TSESTree.Node, options: SimpleTraverseOptions, setParentPointers?: boolean): void;
export {};
//# sourceMappingURL=simple-traverse.d.ts.map