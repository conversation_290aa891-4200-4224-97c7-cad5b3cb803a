{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\AnalysisResults.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaChartLine, FaExclamationTriangle, FaCheckCircle, FaFileAlt, FaPhone, FaCalendarAlt, FaSearch } from 'react-icons/fa';\nimport './AnalysisResults.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisResults = ({\n  results,\n  isAnalyzing\n}) => {\n  _s();\n  const [analysisSummary, setAnalysisSummary] = useState(null);\n  const [recommendations, setRecommendations] = useState([]);\n  useEffect(() => {\n    if (results && results.length > 0) {\n      generateAnalysisSummary(results);\n      generateRecommendations(results);\n    }\n  }, [results]);\n  const generateAnalysisSummary = detectionResults => {\n    const summary = {\n      totalDetections: detectionResults.length,\n      primaryIssue: null,\n      confidence: 0,\n      severity: 'low'\n    };\n\n    // Find the detection with highest confidence\n    const highestConfidence = detectionResults.reduce((max, current) => current.confidence > max.confidence ? current : max);\n    summary.primaryIssue = highestConfidence.class;\n    summary.confidence = highestConfidence.confidence;\n\n    // Determine severity based on detections\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    if (decayCount > 0) {\n      summary.severity = 'high';\n    } else if (earlyDecayCount > 0) {\n      summary.severity = 'medium';\n    } else {\n      summary.severity = 'low';\n    }\n    setAnalysisSummary(summary);\n  };\n  const generateRecommendations = detectionResults => {\n    const recs = [];\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n    if (decayCount > 0) {\n      recs.push({\n        type: 'urgent',\n        title: 'Immediate Treatment Required',\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\n        icon: '🚨',\n        priority: 1\n      });\n    }\n    if (earlyDecayCount > 0) {\n      recs.push({\n        type: 'warning',\n        title: 'Preventive Care Needed',\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\n        icon: '⚠️',\n        priority: 2\n      });\n    }\n    if (healthyCount > 0) {\n      recs.push({\n        type: 'positive',\n        title: 'Good Oral Health',\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\n        icon: '✅',\n        priority: 3\n      });\n    }\n\n    // Add general recommendations\n    recs.push({\n      type: 'info',\n      title: 'Regular Checkup',\n      description: 'Schedule your next dental checkup within 6 months.',\n      icon: '📅',\n      priority: 4\n    });\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\n  };\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'high':\n        return '#ef4444';\n      case 'medium':\n        return '#f59e0b';\n      case 'low':\n        return '#22c55e';\n      default:\n        return '#0077B6';\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'high':\n        return '🚨';\n      case 'medium':\n        return '⚠️';\n      case 'low':\n        return '✅';\n      default:\n        return 'ℹ️';\n    }\n  };\n  if (isAnalyzing) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        className: \"bg-[rgba(0,119,182,0.05)] p-8 rounded-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Processing analysis results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  }\n  if (!results || results.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] p-8 rounded-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n          className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium mb-2\",\n          children: \"No analysis results available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-sm\",\n          children: \"Start video stream to begin analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Analysis Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [results.length, \" detection(s)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: new Date().toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), analysisSummary && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n        children: \"Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mr-4\",\n            style: {\n              color: getSeverityColor(analysisSummary.severity)\n            },\n            children: getSeverityIcon(analysisSummary.severity)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-semibold text-gray-900 capitalize\",\n                children: analysisSummary.primaryIssue.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: [(analysisSummary.confidence * 100).toFixed(1), \"% confidence\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold uppercase\",\n                style: {\n                  color: getSeverityColor(analysisSummary.severity)\n                },\n                children: [analysisSummary.severity, \" severity\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [analysisSummary.totalDetections, \" total detection(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n        children: \"Detected Conditions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: results.map((result, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mr-4\",\n              children: [result.class === 'decaycavity' && '🦷', result.class === 'early-decay' && '⚠️', result.class === 'healthy tooth' && '✅']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg font-medium text-gray-900 capitalize\",\n                  children: result.class.replace('-', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: [(result.confidence * 100).toFixed(1), \"% confidence\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${result.confidence * 100}%`,\n                    backgroundColor: result.confidence >= 0.8 ? '#22c55e' : result.confidence >= 0.6 ? '#f59e0b' : '#ef4444'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n        children: \"Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: `p-4 rounded-lg border-l-4 ${rec.type === 'urgent' ? 'bg-red-50 border-red-500' : rec.type === 'warning' ? 'bg-yellow-50 border-yellow-500' : rec.type === 'positive' ? 'bg-green-50 border-green-500' : 'bg-blue-50 border-blue-500'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl mr-3\",\n              children: rec.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-semibold text-gray-900 mb-1\",\n                children: rec.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 text-sm\",\n                children: rec.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-[#0077B6] mb-4\",\n        children: \"Next Steps\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), \"Generate Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), \"Contact Dentist\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), \"Schedule Follow-up\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisResults, \"UYQlwBe3O42oXBiuSsTXDpUE2eU=\");\n_c = AnalysisResults;\nexport default AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FaChartLine", "FaExclamationTriangle", "FaCheckCircle", "FaFileAlt", "FaPhone", "FaCalendarAlt", "FaSearch", "jsxDEV", "_jsxDEV", "AnalysisResults", "results", "isAnalyzing", "_s", "analysisSummary", "setAnalysisSummary", "recommendations", "setRecommendations", "length", "generateAnalysisSummary", "generateRecommendations", "detectionResults", "summary", "totalDetections", "primaryIssue", "confidence", "severity", "highestConfidence", "reduce", "max", "current", "class", "decayCount", "filter", "r", "earlyDecayCount", "recs", "healthyCount", "push", "type", "title", "description", "icon", "priority", "sort", "a", "b", "getSeverityColor", "getSeverityIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "Date", "toLocaleTimeString", "y", "style", "color", "replace", "toFixed", "map", "result", "index", "x", "transition", "delay", "width", "backgroundColor", "rec", "button", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/AnalysisResults.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaChartLine, FaExclamationTriangle, FaCheckCircle, FaFileAlt, FaPhone, FaCalendarAlt, FaSearch } from 'react-icons/fa';\r\nimport './AnalysisResults.css';\r\n\r\nconst AnalysisResults = ({ results, isAnalyzing }) => {\r\n  const [analysisSummary, setAnalysisSummary] = useState(null);\r\n  const [recommendations, setRecommendations] = useState([]);\r\n\r\n  useEffect(() => {\r\n    if (results && results.length > 0) {\r\n      generateAnalysisSummary(results);\r\n      generateRecommendations(results);\r\n    }\r\n  }, [results]);\r\n\r\n  const generateAnalysisSummary = (detectionResults) => {\r\n    const summary = {\r\n      totalDetections: detectionResults.length,\r\n      primaryIssue: null,\r\n      confidence: 0,\r\n      severity: 'low'\r\n    };\r\n\r\n    // Find the detection with highest confidence\r\n    const highestConfidence = detectionResults.reduce((max, current) => \r\n      current.confidence > max.confidence ? current : max\r\n    );\r\n\r\n    summary.primaryIssue = highestConfidence.class;\r\n    summary.confidence = highestConfidence.confidence;\r\n\r\n    // Determine severity based on detections\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n\r\n    if (decayCount > 0) {\r\n      summary.severity = 'high';\r\n    } else if (earlyDecayCount > 0) {\r\n      summary.severity = 'medium';\r\n    } else {\r\n      summary.severity = 'low';\r\n    }\r\n\r\n    setAnalysisSummary(summary);\r\n  };\r\n\r\n  const generateRecommendations = (detectionResults) => {\r\n    const recs = [];\r\n\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n\r\n    if (decayCount > 0) {\r\n      recs.push({\r\n        type: 'urgent',\r\n        title: 'Immediate Treatment Required',\r\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\r\n        icon: '🚨',\r\n        priority: 1\r\n      });\r\n    }\r\n\r\n    if (earlyDecayCount > 0) {\r\n      recs.push({\r\n        type: 'warning',\r\n        title: 'Preventive Care Needed',\r\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\r\n        icon: '⚠️',\r\n        priority: 2\r\n      });\r\n    }\r\n\r\n    if (healthyCount > 0) {\r\n      recs.push({\r\n        type: 'positive',\r\n        title: 'Good Oral Health',\r\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\r\n        icon: '✅',\r\n        priority: 3\r\n      });\r\n    }\r\n\r\n    // Add general recommendations\r\n    recs.push({\r\n      type: 'info',\r\n      title: 'Regular Checkup',\r\n      description: 'Schedule your next dental checkup within 6 months.',\r\n      icon: '📅',\r\n      priority: 4\r\n    });\r\n\r\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\r\n  };\r\n\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return '#ef4444';\r\n      case 'medium': return '#f59e0b';\r\n      case 'low': return '#22c55e';\r\n      default: return '#0077B6';\r\n    }\r\n  };\r\n\r\n  const getSeverityIcon = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return '🚨';\r\n      case 'medium': return '⚠️';\r\n      case 'low': return '✅';\r\n      default: return 'ℹ️';\r\n    }\r\n  };\r\n\r\n  if (isAnalyzing) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartLine className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis Results</h3>\r\n        </div>\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-8 rounded-lg text-center\"\r\n        >\r\n          <div className=\"loading-spinner mx-auto mb-4\"></div>\r\n          <p className=\"text-[#0077B6] font-medium\">Processing analysis results...</p>\r\n        </motion.div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!results || results.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartLine className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis Results</h3>\r\n        </div>\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] p-8 rounded-lg text-center\">\r\n          <FaSearch className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\r\n          <p className=\"text-gray-600 font-medium mb-2\">No analysis results available</p>\r\n          <p className=\"text-gray-500 text-sm\">Start video stream to begin analysis</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\r\n            <FaChartLine className=\"h-4 w-4\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis Results</h3>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\r\n            {results.length} detection(s)\r\n          </span>\r\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\r\n            {new Date().toLocaleTimeString()}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Summary Section */}\r\n      {analysisSummary && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\"\r\n        >\r\n          <h4 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Summary</h4>\r\n          <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\r\n            <div className=\"flex items-center\">\r\n              <div className=\"text-3xl mr-4\" style={{ color: getSeverityColor(analysisSummary.severity) }}>\r\n                {getSeverityIcon(analysisSummary.severity)}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2\">\r\n                  <span className=\"text-lg font-semibold text-gray-900 capitalize\">\r\n                    {analysisSummary.primaryIssue.replace('-', ' ')}\r\n                  </span>\r\n                  <span className=\"text-sm font-medium text-gray-600\">\r\n                    {(analysisSummary.confidence * 100).toFixed(1)}% confidence\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\r\n                  <span\r\n                    className=\"text-sm font-bold uppercase\"\r\n                    style={{ color: getSeverityColor(analysisSummary.severity) }}\r\n                  >\r\n                    {analysisSummary.severity} severity\r\n                  </span>\r\n                  <span className=\"text-sm text-gray-500\">\r\n                    {analysisSummary.totalDetections} total detection(s)\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Detected Conditions */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <h4 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Detected Conditions</h4>\r\n        <div className=\"space-y-3\">\r\n          {results.map((result, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ delay: index * 0.1 }}\r\n              className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"text-2xl mr-4\">\r\n                  {result.class === 'decaycavity' && '🦷'}\r\n                  {result.class === 'early-decay' && '⚠️'}\r\n                  {result.class === 'healthy tooth' && '✅'}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"flex justify-between items-center mb-2\">\r\n                    <span className=\"text-lg font-medium text-gray-900 capitalize\">\r\n                      {result.class.replace('-', ' ')}\r\n                    </span>\r\n                    <span className=\"text-sm font-medium text-gray-600\">\r\n                      {(result.confidence * 100).toFixed(1)}% confidence\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                    <div\r\n                      className=\"h-2 rounded-full transition-all duration-300\"\r\n                      style={{\r\n                        width: `${result.confidence * 100}%`,\r\n                        backgroundColor: result.confidence >= 0.8 ? '#22c55e' :\r\n                                       result.confidence >= 0.6 ? '#f59e0b' : '#ef4444'\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recommendations */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <h4 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Recommendations</h4>\r\n        <div className=\"space-y-3\">\r\n          {recommendations.map((rec, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.1 }}\r\n              className={`p-4 rounded-lg border-l-4 ${\r\n                rec.type === 'urgent' ? 'bg-red-50 border-red-500' :\r\n                rec.type === 'warning' ? 'bg-yellow-50 border-yellow-500' :\r\n                rec.type === 'positive' ? 'bg-green-50 border-green-500' :\r\n                'bg-blue-50 border-blue-500'\r\n              }`}\r\n            >\r\n              <div className=\"flex items-start\">\r\n                <div className=\"text-2xl mr-3\">{rec.icon}</div>\r\n                <div>\r\n                  <h5 className=\"font-semibold text-gray-900 mb-1\">{rec.title}</h5>\r\n                  <p className=\"text-gray-700 text-sm\">{rec.description}</p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Next Steps */}\r\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\r\n        <h4 className=\"text-lg font-semibold text-[#0077B6] mb-4\">Next Steps</h4>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-md\"\r\n          >\r\n            <FaFileAlt className=\"mr-2 h-4 w-4\" />\r\n            Generate Report\r\n          </motion.button>\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n          >\r\n            <FaPhone className=\"mr-2 h-4 w-4\" />\r\n            Contact Dentist\r\n          </motion.button>\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center justify-center px-4 py-3 bg-white border-2 border-[#20B2AA] text-[#0077B6] rounded-lg font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300\"\r\n          >\r\n            <FaCalendarAlt className=\"mr-2 h-4 w-4\" />\r\n            Schedule Follow-up\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisResults; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,OAAO,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/H,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAIY,OAAO,IAAIA,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MACjCC,uBAAuB,CAACR,OAAO,CAAC;MAChCS,uBAAuB,CAACT,OAAO,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMQ,uBAAuB,GAAIE,gBAAgB,IAAK;IACpD,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAEF,gBAAgB,CAACH,MAAM;MACxCM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGN,gBAAgB,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAC7DA,OAAO,CAACL,UAAU,GAAGI,GAAG,CAACJ,UAAU,GAAGK,OAAO,GAAGD,GAClD,CAAC;IAEDP,OAAO,CAACE,YAAY,GAAGG,iBAAiB,CAACI,KAAK;IAC9CT,OAAO,CAACG,UAAU,GAAGE,iBAAiB,CAACF,UAAU;;IAEjD;IACA,MAAMO,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IAEtF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBV,OAAO,CAACI,QAAQ,GAAG,MAAM;IAC3B,CAAC,MAAM,IAAIS,eAAe,GAAG,CAAC,EAAE;MAC9Bb,OAAO,CAACI,QAAQ,GAAG,QAAQ;IAC7B,CAAC,MAAM;MACLJ,OAAO,CAACI,QAAQ,GAAG,KAAK;IAC1B;IAEAX,kBAAkB,CAACO,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMF,uBAAuB,GAAIC,gBAAgB,IAAK;IACpD,MAAMe,IAAI,GAAG,EAAE;IAEf,MAAMJ,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACtF,MAAMmB,YAAY,GAAGhB,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,eAAe,CAAC,CAACb,MAAM;IAErF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBI,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,8BAA8B;QACrCC,WAAW,EAAE,iFAAiF;QAC9FC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIR,eAAe,GAAG,CAAC,EAAE;MACvBC,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,wFAAwF;QACrGC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIN,YAAY,GAAG,CAAC,EAAE;MACpBD,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,qEAAqE;QAClFC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACAP,IAAI,CAACE,IAAI,CAAC;MACRC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,oDAAoD;MACjEC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF1B,kBAAkB,CAACmB,IAAI,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,QAAQ,GAAGG,CAAC,CAACH,QAAQ,CAAC,CAAC;EAClE,CAAC;EAED,MAAMI,gBAAgB,GAAIrB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMsB,eAAe,GAAItB,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,KAAK;QAAE,OAAO,GAAG;MACtB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,IAAId,WAAW,EAAE;IACf,oBACEH,OAAA;MAAKwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBzC,OAAA;QAAKwC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzC,OAAA;UAAKwC,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EzC,OAAA,CAACR,WAAW;YAACgD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACN7C,OAAA;UAAIwC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN7C,OAAA,CAACT,MAAM,CAACuD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBR,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBAEhEzC,OAAA;UAAKwC,SAAS,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpD7C,OAAA;UAAGwC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAI,CAAC3C,OAAO,IAAIA,OAAO,CAACO,MAAM,KAAK,CAAC,EAAE;IACpC,oBACET,OAAA;MAAKwC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBzC,OAAA;QAAKwC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzC,OAAA;UAAKwC,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EzC,OAAA,CAACR,WAAW;YAACgD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACN7C,OAAA;UAAIwC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnEzC,OAAA,CAACF,QAAQ;UAAC0C,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D7C,OAAA;UAAGwC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/E7C,OAAA;UAAGwC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7C,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzC,OAAA;MAAKwC,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAC1FzC,OAAA;QAAKwC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzC,OAAA;UAAKwC,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EzC,OAAA,CAACR,WAAW;YAACgD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACN7C,OAAA;UAAIwC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzC,OAAA;UAAMwC,SAAS,EAAC,oFAAoF;UAAAC,QAAA,GACjGvC,OAAO,CAACO,MAAM,EAAC,eAClB;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP7C,OAAA;UAAMwC,SAAS,EAAC,qFAAqF;UAAAC,QAAA,EAClG,IAAIS,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxC,eAAe,iBACdL,OAAA,CAACT,MAAM,CAACuD,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEI,CAAC,EAAE;MAAG,CAAE;MAC/BH,OAAO,EAAE;QAAED,OAAO,EAAE,CAAC;QAAEI,CAAC,EAAE;MAAE,CAAE;MAC9BZ,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAEpDzC,OAAA;QAAIwC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE7C,OAAA;QAAKwC,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEzC,OAAA;UAAKwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzC,OAAA;YAAKwC,SAAS,EAAC,eAAe;YAACa,KAAK,EAAE;cAAEC,KAAK,EAAEhB,gBAAgB,CAACjC,eAAe,CAACY,QAAQ;YAAE,CAAE;YAAAwB,QAAA,EACzFF,eAAe,CAAClC,eAAe,CAACY,QAAQ;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzC,OAAA;cAAKwC,SAAS,EAAC,mEAAmE;cAAAC,QAAA,gBAChFzC,OAAA;gBAAMwC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAC7DpC,eAAe,CAACU,YAAY,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACP7C,OAAA;gBAAMwC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAChD,CAACpC,eAAe,CAACW,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,cACjD;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3EzC,OAAA;gBACEwC,SAAS,EAAC,6BAA6B;gBACvCa,KAAK,EAAE;kBAAEC,KAAK,EAAEhB,gBAAgB,CAACjC,eAAe,CAACY,QAAQ;gBAAE,CAAE;gBAAAwB,QAAA,GAE5DpC,eAAe,CAACY,QAAQ,EAAC,WAC5B;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP7C,OAAA;gBAAMwC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpCpC,eAAe,CAACS,eAAe,EAAC,qBACnC;cAAA;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAGD7C,OAAA;MAAKwC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDzC,OAAA;QAAIwC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClF7C,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBvC,OAAO,CAACuD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB3D,OAAA,CAACT,MAAM,CAACuD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCX,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9BC,UAAU,EAAE;YAAEC,KAAK,EAAEH,KAAK,GAAG;UAAI,CAAE;UACnCnB,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAEvHzC,OAAA;YAAKwC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzC,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,GAC3BiB,MAAM,CAACpC,KAAK,KAAK,aAAa,IAAI,IAAI,EACtCoC,MAAM,CAACpC,KAAK,KAAK,aAAa,IAAI,IAAI,EACtCoC,MAAM,CAACpC,KAAK,KAAK,eAAe,IAAI,GAAG;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBzC,OAAA;gBAAKwC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDzC,OAAA;kBAAMwC,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAC3DiB,MAAM,CAACpC,KAAK,CAACiC,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACP7C,OAAA;kBAAMwC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAChD,CAACiB,MAAM,CAAC1C,UAAU,GAAG,GAAG,EAAEwC,OAAO,CAAC,CAAC,CAAC,EAAC,cACxC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN7C,OAAA;gBAAKwC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDzC,OAAA;kBACEwC,SAAS,EAAC,8CAA8C;kBACxDa,KAAK,EAAE;oBACLU,KAAK,EAAE,GAAGL,MAAM,CAAC1C,UAAU,GAAG,GAAG,GAAG;oBACpCgD,eAAe,EAAEN,MAAM,CAAC1C,UAAU,IAAI,GAAG,GAAG,SAAS,GACtC0C,MAAM,CAAC1C,UAAU,IAAI,GAAG,GAAG,SAAS,GAAG;kBACxD;gBAAE;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAhCDc,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiCA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDzC,OAAA;QAAIwC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9E7C,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBlC,eAAe,CAACkD,GAAG,CAAC,CAACQ,GAAG,EAAEN,KAAK,kBAC9B3D,OAAA,CAACT,MAAM,CAACuD,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAG,CAAE;UAC/BH,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAE,CAAE;UAC9BS,UAAU,EAAE;YAAEC,KAAK,EAAEH,KAAK,GAAG;UAAI,CAAE;UACnCnB,SAAS,EAAE,6BACTyB,GAAG,CAACnC,IAAI,KAAK,QAAQ,GAAG,0BAA0B,GAClDmC,GAAG,CAACnC,IAAI,KAAK,SAAS,GAAG,gCAAgC,GACzDmC,GAAG,CAACnC,IAAI,KAAK,UAAU,GAAG,8BAA8B,GACxD,4BAA4B,EAC3B;UAAAW,QAAA,eAEHzC,OAAA;YAAKwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BzC,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEwB,GAAG,CAAChC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C7C,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAIwC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEwB,GAAG,CAAClC;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjE7C,OAAA;gBAAGwC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEwB,GAAG,CAACjC;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjBDc,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDzC,OAAA;QAAIwC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzE7C,OAAA;QAAKwC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDzC,OAAA,CAACT,MAAM,CAAC2E,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1B5B,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LzC,OAAA,CAACL,SAAS;YAAC6C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB7C,OAAA,CAACT,MAAM,CAAC2E,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1B5B,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LzC,OAAA,CAACJ,OAAO;YAAC4C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChB7C,OAAA,CAACT,MAAM,CAAC2E,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1B5B,SAAS,EAAC,iLAAiL;UAAAC,QAAA,gBAE3LzC,OAAA,CAACH,aAAa;YAAC2C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CAxTIH,eAAe;AAAAqE,EAAA,GAAfrE,eAAe;AA0TrB,eAAeA,eAAe;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}