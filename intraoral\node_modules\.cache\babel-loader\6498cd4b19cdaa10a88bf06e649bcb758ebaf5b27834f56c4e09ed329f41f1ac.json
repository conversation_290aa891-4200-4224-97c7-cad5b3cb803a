{"ast": null, "code": "import { extractEventInfo } from '../events/event-info.mjs';\nimport { addDomEvent } from '../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../events/add-pointer-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { isNodeOrChild } from './utils/is-node-or-child.mjs';\nimport { noop } from '../utils/noop.mjs';\nimport { frame } from '../frameloop/frame.mjs';\nfunction fireSyntheticPointerEvent(name, handler) {\n  if (!handler) return;\n  const syntheticPointerEvent = new PointerEvent(\"pointer\" + name);\n  handler(syntheticPointerEvent, extractEventInfo(syntheticPointerEvent));\n}\nclass PressGesture extends Feature {\n  constructor() {\n    super(...arguments);\n    this.removeStartListeners = noop;\n    this.removeEndListeners = noop;\n    this.removeAccessibleListeners = noop;\n    this.startPointerPress = (startEvent, startInfo) => {\n      if (this.isPressing) return;\n      this.removeEndListeners();\n      const props = this.node.getProps();\n      const endPointerPress = (endEvent, endInfo) => {\n        if (!this.checkPressEnd()) return;\n        const {\n          onTap,\n          onTapCancel,\n          globalTapTarget\n        } = this.node.getProps();\n        frame.update(() => {\n          /**\n           * We only count this as a tap gesture if the event.target is the same\n           * as, or a child of, this component's element\n           */\n          !globalTapTarget && !isNodeOrChild(this.node.current, endEvent.target) ? onTapCancel && onTapCancel(endEvent, endInfo) : onTap && onTap(endEvent, endInfo);\n        });\n      };\n      const removePointerUpListener = addPointerEvent(window, \"pointerup\", endPointerPress, {\n        passive: !(props.onTap || props[\"onPointerUp\"])\n      });\n      const removePointerCancelListener = addPointerEvent(window, \"pointercancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo), {\n        passive: !(props.onTapCancel || props[\"onPointerCancel\"])\n      });\n      this.removeEndListeners = pipe(removePointerUpListener, removePointerCancelListener);\n      this.startPress(startEvent, startInfo);\n    };\n    this.startAccessiblePress = () => {\n      const handleKeydown = keydownEvent => {\n        if (keydownEvent.key !== \"Enter\" || this.isPressing) return;\n        const handleKeyup = keyupEvent => {\n          if (keyupEvent.key !== \"Enter\" || !this.checkPressEnd()) return;\n          fireSyntheticPointerEvent(\"up\", (event, info) => {\n            const {\n              onTap\n            } = this.node.getProps();\n            if (onTap) {\n              frame.update(() => onTap(event, info));\n            }\n          });\n        };\n        this.removeEndListeners();\n        this.removeEndListeners = addDomEvent(this.node.current, \"keyup\", handleKeyup);\n        fireSyntheticPointerEvent(\"down\", (event, info) => {\n          this.startPress(event, info);\n        });\n      };\n      const removeKeydownListener = addDomEvent(this.node.current, \"keydown\", handleKeydown);\n      const handleBlur = () => {\n        if (!this.isPressing) return;\n        fireSyntheticPointerEvent(\"cancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo));\n      };\n      const removeBlurListener = addDomEvent(this.node.current, \"blur\", handleBlur);\n      this.removeAccessibleListeners = pipe(removeKeydownListener, removeBlurListener);\n    };\n  }\n  startPress(event, info) {\n    this.isPressing = true;\n    const {\n      onTapStart,\n      whileTap\n    } = this.node.getProps();\n    /**\n     * Ensure we trigger animations before firing event callback\n     */\n    if (whileTap && this.node.animationState) {\n      this.node.animationState.setActive(\"whileTap\", true);\n    }\n    if (onTapStart) {\n      frame.update(() => onTapStart(event, info));\n    }\n  }\n  checkPressEnd() {\n    this.removeEndListeners();\n    this.isPressing = false;\n    const props = this.node.getProps();\n    if (props.whileTap && this.node.animationState) {\n      this.node.animationState.setActive(\"whileTap\", false);\n    }\n    return !isDragActive();\n  }\n  cancelPress(event, info) {\n    if (!this.checkPressEnd()) return;\n    const {\n      onTapCancel\n    } = this.node.getProps();\n    if (onTapCancel) {\n      frame.update(() => onTapCancel(event, info));\n    }\n  }\n  mount() {\n    const props = this.node.getProps();\n    const removePointerListener = addPointerEvent(props.globalTapTarget ? window : this.node.current, \"pointerdown\", this.startPointerPress, {\n      passive: !(props.onTapStart || props[\"onPointerStart\"])\n    });\n    const removeFocusListener = addDomEvent(this.node.current, \"focus\", this.startAccessiblePress);\n    this.removeStartListeners = pipe(removePointerListener, removeFocusListener);\n  }\n  unmount() {\n    this.removeStartListeners();\n    this.removeEndListeners();\n    this.removeAccessibleListeners();\n  }\n}\nexport { PressGesture };", "map": {"version": 3, "names": ["extractEventInfo", "addDomEvent", "addPointerEvent", "Feature", "pipe", "isDragActive", "isNodeOrChild", "noop", "frame", "fireSyntheticPointerEvent", "name", "handler", "syntheticPointerEvent", "PointerEvent", "PressGesture", "constructor", "arguments", "removeStartListeners", "removeEndListeners", "removeAccessibleListeners", "startPointerPress", "startEvent", "startInfo", "isPressing", "props", "node", "getProps", "endPointerPress", "endEvent", "endInfo", "checkPressEnd", "onTap", "onTapCancel", "globalTapTarget", "update", "current", "target", "removePointerUpListener", "window", "passive", "removePointerCancelListener", "cancelEvent", "cancelInfo", "cancelPress", "startPress", "startAccessiblePress", "handleKeydown", "keydownEvent", "key", "handleKeyup", "keyupEvent", "event", "info", "removeKeydownListener", "handleBlur", "removeBlurListener", "onTapStart", "whileTap", "animationState", "setActive", "mount", "removePointerListener", "removeFocusListener", "unmount"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/gestures/press.mjs"], "sourcesContent": ["import { extractEventInfo } from '../events/event-info.mjs';\nimport { addDomEvent } from '../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../events/add-pointer-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { isNodeOrChild } from './utils/is-node-or-child.mjs';\nimport { noop } from '../utils/noop.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\nfunction fireSyntheticPointerEvent(name, handler) {\n    if (!handler)\n        return;\n    const syntheticPointerEvent = new PointerEvent(\"pointer\" + name);\n    handler(syntheticPointerEvent, extractEventInfo(syntheticPointerEvent));\n}\nclass PressGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removeStartListeners = noop;\n        this.removeEndListeners = noop;\n        this.removeAccessibleListeners = noop;\n        this.startPointerPress = (startEvent, startInfo) => {\n            if (this.isPressing)\n                return;\n            this.removeEndListeners();\n            const props = this.node.getProps();\n            const endPointerPress = (endEvent, endInfo) => {\n                if (!this.checkPressEnd())\n                    return;\n                const { onTap, onTapCancel, globalTapTarget } = this.node.getProps();\n                frame.update(() => {\n                    /**\n                     * We only count this as a tap gesture if the event.target is the same\n                     * as, or a child of, this component's element\n                     */\n                    !globalTapTarget &&\n                        !isNodeOrChild(this.node.current, endEvent.target)\n                        ? onTapCancel && onTapCancel(endEvent, endInfo)\n                        : onTap && onTap(endEvent, endInfo);\n                });\n            };\n            const removePointerUpListener = addPointerEvent(window, \"pointerup\", endPointerPress, { passive: !(props.onTap || props[\"onPointerUp\"]) });\n            const removePointerCancelListener = addPointerEvent(window, \"pointercancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo), { passive: !(props.onTapCancel || props[\"onPointerCancel\"]) });\n            this.removeEndListeners = pipe(removePointerUpListener, removePointerCancelListener);\n            this.startPress(startEvent, startInfo);\n        };\n        this.startAccessiblePress = () => {\n            const handleKeydown = (keydownEvent) => {\n                if (keydownEvent.key !== \"Enter\" || this.isPressing)\n                    return;\n                const handleKeyup = (keyupEvent) => {\n                    if (keyupEvent.key !== \"Enter\" || !this.checkPressEnd())\n                        return;\n                    fireSyntheticPointerEvent(\"up\", (event, info) => {\n                        const { onTap } = this.node.getProps();\n                        if (onTap) {\n                            frame.update(() => onTap(event, info));\n                        }\n                    });\n                };\n                this.removeEndListeners();\n                this.removeEndListeners = addDomEvent(this.node.current, \"keyup\", handleKeyup);\n                fireSyntheticPointerEvent(\"down\", (event, info) => {\n                    this.startPress(event, info);\n                });\n            };\n            const removeKeydownListener = addDomEvent(this.node.current, \"keydown\", handleKeydown);\n            const handleBlur = () => {\n                if (!this.isPressing)\n                    return;\n                fireSyntheticPointerEvent(\"cancel\", (cancelEvent, cancelInfo) => this.cancelPress(cancelEvent, cancelInfo));\n            };\n            const removeBlurListener = addDomEvent(this.node.current, \"blur\", handleBlur);\n            this.removeAccessibleListeners = pipe(removeKeydownListener, removeBlurListener);\n        };\n    }\n    startPress(event, info) {\n        this.isPressing = true;\n        const { onTapStart, whileTap } = this.node.getProps();\n        /**\n         * Ensure we trigger animations before firing event callback\n         */\n        if (whileTap && this.node.animationState) {\n            this.node.animationState.setActive(\"whileTap\", true);\n        }\n        if (onTapStart) {\n            frame.update(() => onTapStart(event, info));\n        }\n    }\n    checkPressEnd() {\n        this.removeEndListeners();\n        this.isPressing = false;\n        const props = this.node.getProps();\n        if (props.whileTap && this.node.animationState) {\n            this.node.animationState.setActive(\"whileTap\", false);\n        }\n        return !isDragActive();\n    }\n    cancelPress(event, info) {\n        if (!this.checkPressEnd())\n            return;\n        const { onTapCancel } = this.node.getProps();\n        if (onTapCancel) {\n            frame.update(() => onTapCancel(event, info));\n        }\n    }\n    mount() {\n        const props = this.node.getProps();\n        const removePointerListener = addPointerEvent(props.globalTapTarget ? window : this.node.current, \"pointerdown\", this.startPointerPress, { passive: !(props.onTapStart || props[\"onPointerStart\"]) });\n        const removeFocusListener = addDomEvent(this.node.current, \"focus\", this.startAccessiblePress);\n        this.removeStartListeners = pipe(removePointerListener, removeFocusListener);\n    }\n    unmount() {\n        this.removeStartListeners();\n        this.removeEndListeners();\n        this.removeAccessibleListeners();\n    }\n}\n\nexport { PressGesture };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,wBAAwB;AAE9C,SAASC,yBAAyBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC9C,IAAI,CAACA,OAAO,EACR;EACJ,MAAMC,qBAAqB,GAAG,IAAIC,YAAY,CAAC,SAAS,GAAGH,IAAI,CAAC;EAChEC,OAAO,CAACC,qBAAqB,EAAEZ,gBAAgB,CAACY,qBAAqB,CAAC,CAAC;AAC3E;AACA,MAAME,YAAY,SAASX,OAAO,CAAC;EAC/BY,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,oBAAoB,GAAGV,IAAI;IAChC,IAAI,CAACW,kBAAkB,GAAGX,IAAI;IAC9B,IAAI,CAACY,yBAAyB,GAAGZ,IAAI;IACrC,IAAI,CAACa,iBAAiB,GAAG,CAACC,UAAU,EAAEC,SAAS,KAAK;MAChD,IAAI,IAAI,CAACC,UAAU,EACf;MACJ,IAAI,CAACL,kBAAkB,CAAC,CAAC;MACzB,MAAMM,KAAK,GAAG,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,CAAC;MAClC,MAAMC,eAAe,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;QAC3C,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC,CAAC,EACrB;QACJ,MAAM;UAAEC,KAAK;UAAEC,WAAW;UAAEC;QAAgB,CAAC,GAAG,IAAI,CAACR,IAAI,CAACC,QAAQ,CAAC,CAAC;QACpElB,KAAK,CAAC0B,MAAM,CAAC,MAAM;UACf;AACpB;AACA;AACA;UACoB,CAACD,eAAe,IACZ,CAAC3B,aAAa,CAAC,IAAI,CAACmB,IAAI,CAACU,OAAO,EAAEP,QAAQ,CAACQ,MAAM,CAAC,GAChDJ,WAAW,IAAIA,WAAW,CAACJ,QAAQ,EAAEC,OAAO,CAAC,GAC7CE,KAAK,IAAIA,KAAK,CAACH,QAAQ,EAAEC,OAAO,CAAC;QAC3C,CAAC,CAAC;MACN,CAAC;MACD,MAAMQ,uBAAuB,GAAGnC,eAAe,CAACoC,MAAM,EAAE,WAAW,EAAEX,eAAe,EAAE;QAAEY,OAAO,EAAE,EAAEf,KAAK,CAACO,KAAK,IAAIP,KAAK,CAAC,aAAa,CAAC;MAAE,CAAC,CAAC;MAC1I,MAAMgB,2BAA2B,GAAGtC,eAAe,CAACoC,MAAM,EAAE,eAAe,EAAE,CAACG,WAAW,EAAEC,UAAU,KAAK,IAAI,CAACC,WAAW,CAACF,WAAW,EAAEC,UAAU,CAAC,EAAE;QAAEH,OAAO,EAAE,EAAEf,KAAK,CAACQ,WAAW,IAAIR,KAAK,CAAC,iBAAiB,CAAC;MAAE,CAAC,CAAC;MACnN,IAAI,CAACN,kBAAkB,GAAGd,IAAI,CAACiC,uBAAuB,EAAEG,2BAA2B,CAAC;MACpF,IAAI,CAACI,UAAU,CAACvB,UAAU,EAAEC,SAAS,CAAC;IAC1C,CAAC;IACD,IAAI,CAACuB,oBAAoB,GAAG,MAAM;MAC9B,MAAMC,aAAa,GAAIC,YAAY,IAAK;QACpC,IAAIA,YAAY,CAACC,GAAG,KAAK,OAAO,IAAI,IAAI,CAACzB,UAAU,EAC/C;QACJ,MAAM0B,WAAW,GAAIC,UAAU,IAAK;UAChC,IAAIA,UAAU,CAACF,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAClB,aAAa,CAAC,CAAC,EACnD;UACJrB,yBAAyB,CAAC,IAAI,EAAE,CAAC0C,KAAK,EAAEC,IAAI,KAAK;YAC7C,MAAM;cAAErB;YAAM,CAAC,GAAG,IAAI,CAACN,IAAI,CAACC,QAAQ,CAAC,CAAC;YACtC,IAAIK,KAAK,EAAE;cACPvB,KAAK,CAAC0B,MAAM,CAAC,MAAMH,KAAK,CAACoB,KAAK,EAAEC,IAAI,CAAC,CAAC;YAC1C;UACJ,CAAC,CAAC;QACN,CAAC;QACD,IAAI,CAAClC,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACA,kBAAkB,GAAGjB,WAAW,CAAC,IAAI,CAACwB,IAAI,CAACU,OAAO,EAAE,OAAO,EAAEc,WAAW,CAAC;QAC9ExC,yBAAyB,CAAC,MAAM,EAAE,CAAC0C,KAAK,EAAEC,IAAI,KAAK;UAC/C,IAAI,CAACR,UAAU,CAACO,KAAK,EAAEC,IAAI,CAAC;QAChC,CAAC,CAAC;MACN,CAAC;MACD,MAAMC,qBAAqB,GAAGpD,WAAW,CAAC,IAAI,CAACwB,IAAI,CAACU,OAAO,EAAE,SAAS,EAAEW,aAAa,CAAC;MACtF,MAAMQ,UAAU,GAAGA,CAAA,KAAM;QACrB,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAChB;QACJd,yBAAyB,CAAC,QAAQ,EAAE,CAACgC,WAAW,EAAEC,UAAU,KAAK,IAAI,CAACC,WAAW,CAACF,WAAW,EAAEC,UAAU,CAAC,CAAC;MAC/G,CAAC;MACD,MAAMa,kBAAkB,GAAGtD,WAAW,CAAC,IAAI,CAACwB,IAAI,CAACU,OAAO,EAAE,MAAM,EAAEmB,UAAU,CAAC;MAC7E,IAAI,CAACnC,yBAAyB,GAAGf,IAAI,CAACiD,qBAAqB,EAAEE,kBAAkB,CAAC;IACpF,CAAC;EACL;EACAX,UAAUA,CAACO,KAAK,EAAEC,IAAI,EAAE;IACpB,IAAI,CAAC7B,UAAU,GAAG,IAAI;IACtB,MAAM;MAAEiC,UAAU;MAAEC;IAAS,CAAC,GAAG,IAAI,CAAChC,IAAI,CAACC,QAAQ,CAAC,CAAC;IACrD;AACR;AACA;IACQ,IAAI+B,QAAQ,IAAI,IAAI,CAAChC,IAAI,CAACiC,cAAc,EAAE;MACtC,IAAI,CAACjC,IAAI,CAACiC,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;IACxD;IACA,IAAIH,UAAU,EAAE;MACZhD,KAAK,CAAC0B,MAAM,CAAC,MAAMsB,UAAU,CAACL,KAAK,EAAEC,IAAI,CAAC,CAAC;IAC/C;EACJ;EACAtB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACZ,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACK,UAAU,GAAG,KAAK;IACvB,MAAMC,KAAK,GAAG,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,CAAC;IAClC,IAAIF,KAAK,CAACiC,QAAQ,IAAI,IAAI,CAAChC,IAAI,CAACiC,cAAc,EAAE;MAC5C,IAAI,CAACjC,IAAI,CAACiC,cAAc,CAACC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC;IACzD;IACA,OAAO,CAACtD,YAAY,CAAC,CAAC;EAC1B;EACAsC,WAAWA,CAACQ,KAAK,EAAEC,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAACtB,aAAa,CAAC,CAAC,EACrB;IACJ,MAAM;MAAEE;IAAY,CAAC,GAAG,IAAI,CAACP,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC5C,IAAIM,WAAW,EAAE;MACbxB,KAAK,CAAC0B,MAAM,CAAC,MAAMF,WAAW,CAACmB,KAAK,EAAEC,IAAI,CAAC,CAAC;IAChD;EACJ;EACAQ,KAAKA,CAAA,EAAG;IACJ,MAAMpC,KAAK,GAAG,IAAI,CAACC,IAAI,CAACC,QAAQ,CAAC,CAAC;IAClC,MAAMmC,qBAAqB,GAAG3D,eAAe,CAACsB,KAAK,CAACS,eAAe,GAAGK,MAAM,GAAG,IAAI,CAACb,IAAI,CAACU,OAAO,EAAE,aAAa,EAAE,IAAI,CAACf,iBAAiB,EAAE;MAAEmB,OAAO,EAAE,EAAEf,KAAK,CAACgC,UAAU,IAAIhC,KAAK,CAAC,gBAAgB,CAAC;IAAE,CAAC,CAAC;IACrM,MAAMsC,mBAAmB,GAAG7D,WAAW,CAAC,IAAI,CAACwB,IAAI,CAACU,OAAO,EAAE,OAAO,EAAE,IAAI,CAACU,oBAAoB,CAAC;IAC9F,IAAI,CAAC5B,oBAAoB,GAAGb,IAAI,CAACyD,qBAAqB,EAAEC,mBAAmB,CAAC;EAChF;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC9C,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,yBAAyB,CAAC,CAAC;EACpC;AACJ;AAEA,SAASL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}