{"ast": null, "code": "/**\n * ValueType for \"auto\"\n */\nconst auto = {\n  test: v => v === \"auto\",\n  parse: v => v\n};\nexport { auto };", "map": {"version": 3, "names": ["auto", "test", "v", "parse"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/render/dom/value-types/type-auto.mjs"], "sourcesContent": ["/**\n * ValueType for \"auto\"\n */\nconst auto = {\n    test: (v) => v === \"auto\",\n    parse: (v) => v,\n};\n\nexport { auto };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,IAAI,GAAG;EACTC,IAAI,EAAGC,CAAC,IAAKA,CAAC,KAAK,MAAM;EACzBC,KAAK,EAAGD,CAAC,IAAKA;AAClB,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}