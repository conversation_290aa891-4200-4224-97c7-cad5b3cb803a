{"ast": null, "code": "var _jsxFileName = \"D:\\\\intraoral\\\\src\\\\components\\\\VideoCall.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { toast } from 'react-toastify';\nimport './VideoCall.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  onConnectionStatus,\n  onStartAnalysis,\n  onImageCaptured\n}) => {\n  _s();\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    facingMode: \"user\"\n  };\n  const startStream = useCallback(() => {\n    setIsStreaming(true);\n    onConnectionStatus(true);\n    toast.success('Video stream started');\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus]);\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    stopAutoCapture();\n    toast.info('Video stream stopped');\n  }, [onConnectionStatus]);\n  const startAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  };\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n  const captureImage = useCallback(() => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n      if (imageSrc) {\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false\n        };\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Send to YOLOv8 analysis\n        sendImageForAnalysis(imageSrc);\n        toast.success('Image captured and sent for analysis');\n      }\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured]);\n  const sendImageForAnalysis = async imageSrc => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n      const results = await response.json();\n      console.log('YOLOv8 Results:', results);\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      toast.error('Failed to analyze image');\n    }\n  };\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-call\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `btn ${isStreaming ? 'btn-danger' : 'btn-success'}`,\n        onClick: isStreaming ? stopStream : startStream,\n        children: isStreaming ? 'Stop Stream' : 'Start Stream'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: captureImage,\n        disabled: !isStreaming || isCapturing,\n        children: isCapturing ? 'Capturing...' : 'Capture Image'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auto-capture-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"auto-capture-toggle\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoCapture,\n            onChange: toggleAutoCapture,\n            disabled: !isStreaming\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), \"Auto Capture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), autoCapture && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: captureInterval,\n          onChange: e => setCaptureInterval(Number(e.target.value)),\n          disabled: !isStreaming,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 3000,\n            children: \"Every 3s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5000,\n            children: \"Every 5s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10000,\n            children: \"Every 10s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-container\",\n      children: [isStreaming ? /*#__PURE__*/_jsxDEV(Webcam, {\n        ref: webcamRef,\n        audio: false,\n        screenshotFormat: \"image/jpeg\",\n        videoConstraints: videoConstraints,\n        className: \"webcam-video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-placeholder\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"placeholder-icon\",\n            children: \"\\uD83D\\uDCF9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Click \\\"Start Stream\\\" to begin video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"capture-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"capture-indicator\",\n          children: \"\\uD83D\\uDCF8 Capturing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"captured-images\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Recent Captures (\", capturedImages.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-grid\",\n        children: capturedImages.map(image => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"captured-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: \"Captured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: new Date(image.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${image.analyzed ? 'analyzed' : 'pending'}`,\n              children: image.analyzed ? '✓ Analyzed' : '⏳ Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"5txE2dTHvfpSxP5SEYkm86FnYh8=\");\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Webcam", "toast", "jsxDEV", "_jsxDEV", "VideoCall", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "_s", "isStreaming", "setIsStreaming", "isCapturing", "setIsCapturing", "capturedImages", "setCapturedImages", "autoCapture", "setAutoCapture", "captureInterval", "setCaptureInterval", "webcamRef", "intervalRef", "videoConstraints", "width", "height", "facingMode", "startStream", "success", "startAutoCapture", "stopStream", "stopAutoCapture", "info", "current", "clearInterval", "setInterval", "captureImage", "imageSrc", "getScreenshot", "newImage", "id", "Date", "now", "src", "timestamp", "toISOString", "analyzed", "prev", "slice", "sendImageForAnalysis", "base64Data", "replace", "blob", "fetch", "then", "res", "formData", "FormData", "append", "response", "method", "body", "ok", "Error", "results", "json", "console", "log", "error", "toggleAutoCapture", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "type", "checked", "onChange", "value", "e", "Number", "target", "ref", "audio", "screenshotFormat", "length", "map", "image", "alt", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/intraoral/src/components/VideoCall.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport Webcam from 'react-webcam';\r\nimport { toast } from 'react-toastify';\r\nimport './VideoCall.css';\r\n\r\nconst VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured }) => {\r\n  const [isStreaming, setIsStreaming] = useState(false);\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const [capturedImages, setCapturedImages] = useState([]);\r\n  const [autoCapture, setAutoCapture] = useState(true);\r\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\r\n  const webcamRef = useRef(null);\r\n  const intervalRef = useRef(null);\r\n\r\n  const videoConstraints = {\r\n    width: 640,\r\n    height: 480,\r\n    facingMode: \"user\"\r\n  };\r\n\r\n  const startStream = useCallback(() => {\r\n    setIsStreaming(true);\r\n    onConnectionStatus(true);\r\n    toast.success('Video stream started');\r\n    \r\n    // Start automatic capture if enabled\r\n    if (autoCapture) {\r\n      startAutoCapture();\r\n    }\r\n  }, [autoCapture, onConnectionStatus]);\r\n\r\n  const stopStream = useCallback(() => {\r\n    setIsStreaming(false);\r\n    onConnectionStatus(false);\r\n    stopAutoCapture();\r\n    toast.info('Video stream stopped');\r\n  }, [onConnectionStatus]);\r\n\r\n  const startAutoCapture = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n    }\r\n    \r\n    intervalRef.current = setInterval(() => {\r\n      if (isStreaming && webcamRef.current) {\r\n        captureImage();\r\n      }\r\n    }, captureInterval);\r\n  };\r\n\r\n  const stopAutoCapture = () => {\r\n    if (intervalRef.current) {\r\n      clearInterval(intervalRef.current);\r\n      intervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  const captureImage = useCallback(() => {\r\n    if (webcamRef.current && isStreaming) {\r\n      setIsCapturing(true);\r\n      const imageSrc = webcamRef.current.getScreenshot();\r\n      \r\n      if (imageSrc) {\r\n        const newImage = {\r\n          id: Date.now(),\r\n          src: imageSrc,\r\n          timestamp: new Date().toISOString(),\r\n          analyzed: false\r\n        };\r\n        \r\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\r\n        \r\n        // Pass the captured image to parent component for YOLOv8 analysis\r\n        if (onImageCaptured) {\r\n          onImageCaptured(imageSrc);\r\n        }\r\n        \r\n        // Send to YOLOv8 analysis\r\n        sendImageForAnalysis(imageSrc);\r\n        \r\n        toast.success('Image captured and sent for analysis');\r\n      }\r\n      \r\n      setIsCapturing(false);\r\n    }\r\n  }, [isStreaming, onImageCaptured]);\r\n\r\n  const sendImageForAnalysis = async (imageSrc) => {\r\n    try {\r\n      onStartAnalysis();\r\n      \r\n      // Convert base64 to blob\r\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\r\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\r\n      \r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('image', blob, 'capture.jpg');\r\n      \r\n      // Send to backend for YOLOv8 analysis\r\n      const response = await fetch('/api/analyze', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Analysis failed');\r\n      }\r\n      \r\n      const results = await response.json();\r\n      console.log('YOLOv8 Results:', results);\r\n      \r\n    } catch (error) {\r\n      console.error('Error sending image for analysis:', error);\r\n      toast.error('Failed to analyze image');\r\n    }\r\n  };\r\n\r\n  const toggleAutoCapture = () => {\r\n    setAutoCapture(!autoCapture);\r\n    if (!autoCapture) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      stopAutoCapture();\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (autoCapture && isStreaming) {\r\n      startAutoCapture();\r\n    } else {\r\n      stopAutoCapture();\r\n    }\r\n  }, [autoCapture, captureInterval, isStreaming]);\r\n\r\n  return (\r\n    <div className=\"video-call\">\r\n      <div className=\"video-controls\">\r\n        <button \r\n          className={`btn ${isStreaming ? 'btn-danger' : 'btn-success'}`}\r\n          onClick={isStreaming ? stopStream : startStream}\r\n        >\r\n          {isStreaming ? 'Stop Stream' : 'Start Stream'}\r\n        </button>\r\n        \r\n        <button \r\n          className=\"btn btn-primary\"\r\n          onClick={captureImage}\r\n          disabled={!isStreaming || isCapturing}\r\n        >\r\n          {isCapturing ? 'Capturing...' : 'Capture Image'}\r\n        </button>\r\n        \r\n        <div className=\"auto-capture-controls\">\r\n          <label className=\"auto-capture-toggle\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={autoCapture}\r\n              onChange={toggleAutoCapture}\r\n              disabled={!isStreaming}\r\n            />\r\n            Auto Capture\r\n          </label>\r\n          \r\n          {autoCapture && (\r\n            <select\r\n              value={captureInterval}\r\n              onChange={(e) => setCaptureInterval(Number(e.target.value))}\r\n              disabled={!isStreaming}\r\n            >\r\n              <option value={3000}>Every 3s</option>\r\n              <option value={5000}>Every 5s</option>\r\n              <option value={10000}>Every 10s</option>\r\n            </select>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"video-container\">\r\n        {isStreaming ? (\r\n          <Webcam\r\n            ref={webcamRef}\r\n            audio={false}\r\n            screenshotFormat=\"image/jpeg\"\r\n            videoConstraints={videoConstraints}\r\n            className=\"webcam-video\"\r\n          />\r\n        ) : (\r\n          <div className=\"video-overlay\">\r\n            <div className=\"video-placeholder\">\r\n              <div className=\"placeholder-icon\">📹</div>\r\n              <p>Click \"Start Stream\" to begin video consultation</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {isCapturing && (\r\n          <div className=\"capture-overlay\">\r\n            <div className=\"capture-indicator\">📸 Capturing...</div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"captured-images\">\r\n        <h3>Recent Captures ({capturedImages.length})</h3>\r\n        <div className=\"image-grid\">\r\n          {capturedImages.map((image) => (\r\n            <div key={image.id} className=\"captured-image\">\r\n              <img src={image.src} alt=\"Captured\" />\r\n              <div className=\"image-info\">\r\n                <span>{new Date(image.timestamp).toLocaleTimeString()}</span>\r\n                <span className={`status ${image.analyzed ? 'analyzed' : 'pending'}`}>\r\n                  {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoCall; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAMuB,SAAS,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMuB,WAAW,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMwB,gBAAgB,GAAG;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,WAAW,GAAG1B,WAAW,CAAC,MAAM;IACpCW,cAAc,CAAC,IAAI,CAAC;IACpBL,kBAAkB,CAAC,IAAI,CAAC;IACxBJ,KAAK,CAACyB,OAAO,CAAC,sBAAsB,CAAC;;IAErC;IACA,IAAIX,WAAW,EAAE;MACfY,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACZ,WAAW,EAAEV,kBAAkB,CAAC,CAAC;EAErC,MAAMuB,UAAU,GAAG7B,WAAW,CAAC,MAAM;IACnCW,cAAc,CAAC,KAAK,CAAC;IACrBL,kBAAkB,CAAC,KAAK,CAAC;IACzBwB,eAAe,CAAC,CAAC;IACjB5B,KAAK,CAAC6B,IAAI,CAAC,sBAAsB,CAAC;EACpC,CAAC,EAAE,CAACzB,kBAAkB,CAAC,CAAC;EAExB,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,WAAW,CAACW,OAAO,EAAE;MACvBC,aAAa,CAACZ,WAAW,CAACW,OAAO,CAAC;IACpC;IAEAX,WAAW,CAACW,OAAO,GAAGE,WAAW,CAAC,MAAM;MACtC,IAAIxB,WAAW,IAAIU,SAAS,CAACY,OAAO,EAAE;QACpCG,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAEjB,eAAe,CAAC;EACrB,CAAC;EAED,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIT,WAAW,CAACW,OAAO,EAAE;MACvBC,aAAa,CAACZ,WAAW,CAACW,OAAO,CAAC;MAClCX,WAAW,CAACW,OAAO,GAAG,IAAI;IAC5B;EACF,CAAC;EAED,MAAMG,YAAY,GAAGnC,WAAW,CAAC,MAAM;IACrC,IAAIoB,SAAS,CAACY,OAAO,IAAItB,WAAW,EAAE;MACpCG,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMuB,QAAQ,GAAGhB,SAAS,CAACY,OAAO,CAACK,aAAa,CAAC,CAAC;MAElD,IAAID,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAG;UACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,GAAG,EAAEN,QAAQ;UACbO,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCC,QAAQ,EAAE;QACZ,CAAC;QAED9B,iBAAiB,CAAC+B,IAAI,IAAI,CAACR,QAAQ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAIvC,eAAe,EAAE;UACnBA,eAAe,CAAC4B,QAAQ,CAAC;QAC3B;;QAEA;QACAY,oBAAoB,CAACZ,QAAQ,CAAC;QAE9BlC,KAAK,CAACyB,OAAO,CAAC,sCAAsC,CAAC;MACvD;MAEAd,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEF,eAAe,CAAC,CAAC;EAElC,MAAMwC,oBAAoB,GAAG,MAAOZ,QAAQ,IAAK;IAC/C,IAAI;MACF7B,eAAe,CAAC,CAAC;;MAEjB;MACA,MAAM0C,UAAU,GAAGb,QAAQ,CAACc,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;MACpE,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAAC,0BAA0BH,UAAU,EAAE,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC;;MAExF;MACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,IAAI,EAAE,aAAa,CAAC;;MAE7C;MACA,MAAMO,QAAQ,GAAG,MAAMN,KAAK,CAAC,cAAc,EAAE;QAC3CO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEL;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAMC,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACrCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,OAAO,CAAC;IAEzC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDjE,KAAK,CAACiE,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnD,cAAc,CAAC,CAACD,WAAW,CAAC;IAC5B,IAAI,CAACA,WAAW,EAAE;MAChBY,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLE,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED/B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX+B,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN/B,SAAS,CAAC,MAAM;IACd,IAAIiB,WAAW,IAAIN,WAAW,EAAE;MAC9BkB,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLE,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACd,WAAW,EAAEE,eAAe,EAAER,WAAW,CAAC,CAAC;EAE/C,oBACEN,OAAA;IAAKiE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBlE,OAAA;MAAKiE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlE,OAAA;QACEiE,SAAS,EAAE,OAAO3D,WAAW,GAAG,YAAY,GAAG,aAAa,EAAG;QAC/D6D,OAAO,EAAE7D,WAAW,GAAGmB,UAAU,GAAGH,WAAY;QAAA4C,QAAA,EAE/C5D,WAAW,GAAG,aAAa,GAAG;MAAc;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAETvE,OAAA;QACEiE,SAAS,EAAC,iBAAiB;QAC3BE,OAAO,EAAEpC,YAAa;QACtByC,QAAQ,EAAE,CAAClE,WAAW,IAAIE,WAAY;QAAA0D,QAAA,EAErC1D,WAAW,GAAG,cAAc,GAAG;MAAe;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAETvE,OAAA;QAAKiE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClE,OAAA;UAAOiE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpClE,OAAA;YACEyE,IAAI,EAAC,UAAU;YACfC,OAAO,EAAE9D,WAAY;YACrB+D,QAAQ,EAAEX,iBAAkB;YAC5BQ,QAAQ,EAAE,CAAClE;UAAY;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAEP3D,WAAW,iBACVZ,OAAA;UACE4E,KAAK,EAAE9D,eAAgB;UACvB6D,QAAQ,EAAGE,CAAC,IAAK9D,kBAAkB,CAAC+D,MAAM,CAACD,CAAC,CAACE,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5DJ,QAAQ,EAAE,CAAClE,WAAY;UAAA4D,QAAA,gBAEvBlE,OAAA;YAAQ4E,KAAK,EAAE,IAAK;YAAAV,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCvE,OAAA;YAAQ4E,KAAK,EAAE,IAAK;YAAAV,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCvE,OAAA;YAAQ4E,KAAK,EAAE,KAAM;YAAAV,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvE,OAAA;MAAKiE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,GAC7B5D,WAAW,gBACVN,OAAA,CAACH,MAAM;QACLmF,GAAG,EAAEhE,SAAU;QACfiE,KAAK,EAAE,KAAM;QACbC,gBAAgB,EAAC,YAAY;QAC7BhE,gBAAgB,EAAEA,gBAAiB;QACnC+C,SAAS,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAEFvE,OAAA;QAAKiE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BlE,OAAA;UAAKiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClE,OAAA;YAAKiE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CvE,OAAA;YAAAkE,QAAA,EAAG;UAAgD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEA/D,WAAW,iBACVR,OAAA;QAAKiE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BlE,OAAA;UAAKiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvE,OAAA;MAAKiE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BlE,OAAA;QAAAkE,QAAA,GAAI,mBAAiB,EAACxD,cAAc,CAACyE,MAAM,EAAC,GAAC;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDvE,OAAA;QAAKiE,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBxD,cAAc,CAAC0E,GAAG,CAAEC,KAAK,iBACxBrF,OAAA;UAAoBiE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC5ClE,OAAA;YAAKsC,GAAG,EAAE+C,KAAK,CAAC/C,GAAI;YAACgD,GAAG,EAAC;UAAU;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCvE,OAAA;YAAKiE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlE,OAAA;cAAAkE,QAAA,EAAO,IAAI9B,IAAI,CAACiD,KAAK,CAAC9C,SAAS,CAAC,CAACgD,kBAAkB,CAAC;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7DvE,OAAA;cAAMiE,SAAS,EAAE,UAAUoB,KAAK,CAAC5C,QAAQ,GAAG,UAAU,GAAG,SAAS,EAAG;cAAAyB,QAAA,EAClEmB,KAAK,CAAC5C,QAAQ,GAAG,YAAY,GAAG;YAAW;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAPEc,KAAK,CAAClD,EAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CA9NIJ,SAAS;AAAAuF,EAAA,GAATvF,SAAS;AAgOf,eAAeA,SAAS;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}