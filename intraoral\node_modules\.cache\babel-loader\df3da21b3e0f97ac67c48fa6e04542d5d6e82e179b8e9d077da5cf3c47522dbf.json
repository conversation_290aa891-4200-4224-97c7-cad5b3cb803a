{"ast": null, "code": "import { createContext } from 'react';\nconst LayoutGroupContext = createContext({});\nexport { LayoutGroupContext };", "map": {"version": 3, "names": ["createContext", "LayoutGroupContext"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\nconst LayoutGroupContext = createContext({});\n\nexport { LayoutGroupContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,kBAAkB,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC;AAE5C,SAASC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}