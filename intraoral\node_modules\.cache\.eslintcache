[{"D:\\intraoral\\src\\index.js": "1", "D:\\intraoral\\src\\App.js": "2", "D:\\intraoral\\src\\components\\YOLODetection.js": "3", "D:\\intraoral\\src\\components\\PatientInfo.js": "4", "D:\\intraoral\\src\\components\\AnalysisResults.js": "5", "D:\\intraoral\\src\\components\\VideoCall.js": "6", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\index.js": "7", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\App.js": "8", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\VideoCall.js": "9", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\YOLODetection.js": "10", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\PatientInfo.js": "11", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\AnalysisResults.js": "12", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Sidebar.jsx": "13", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Navbar.jsx": "14"}, {"size": 264, "mtime": 1750455288629, "results": "15", "hashOfConfig": "16"}, {"size": 3419, "mtime": 1750456579279, "results": "17", "hashOfConfig": "16"}, {"size": 7859, "mtime": 1750456616189, "results": "18", "hashOfConfig": "16"}, {"size": 5261, "mtime": 1750459054072, "results": "19", "hashOfConfig": "16"}, {"size": 8018, "mtime": 1750459054072, "results": "20", "hashOfConfig": "16"}, {"size": 6883, "mtime": 1750456564794, "results": "21", "hashOfConfig": "16"}, {"size": 264, "mtime": 1750455288629, "results": "22", "hashOfConfig": "23"}, {"size": 23234, "mtime": 1750545597871, "results": "24", "hashOfConfig": "23"}, {"size": 10108, "mtime": 1750545597874, "results": "25", "hashOfConfig": "23"}, {"size": 11859, "mtime": 1750545597875, "results": "26", "hashOfConfig": "23"}, {"size": 9602, "mtime": 1750531792859, "results": "27", "hashOfConfig": "23"}, {"size": 17392, "mtime": 1750544379484, "results": "28", "hashOfConfig": "23"}, {"size": 3379, "mtime": 1750542923303, "results": "29", "hashOfConfig": "23"}, {"size": 4035, "mtime": 1750542939690, "results": "30", "hashOfConfig": "23"}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wlzyog", {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "troter", {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\intraoral\\src\\index.js", [], [], "D:\\intraoral\\src\\App.js", ["73", "74"], [], "D:\\intraoral\\src\\components\\YOLODetection.js", ["75", "76"], [], "D:\\intraoral\\src\\components\\PatientInfo.js", [], [], "D:\\intraoral\\src\\components\\AnalysisResults.js", [], [], "D:\\intraoral\\src\\components\\VideoCall.js", ["77", "78", "79"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\index.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\App.js", ["80", "81", "82"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\VideoCall.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\YOLODetection.js", ["83", "84", "85"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\PatientInfo.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\AnalysisResults.js", ["86", "87", "88"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Navbar.jsx", [], [], {"ruleId": "89", "severity": 1, "message": "90", "line": 1, "column": 27, "nodeType": "91", "messageId": "92", "endLine": 1, "endColumn": 36}, {"ruleId": "89", "severity": 1, "message": "93", "line": 12, "column": 23, "nodeType": "91", "messageId": "92", "endLine": 12, "endColumn": 37}, {"ruleId": "89", "severity": 1, "message": "94", "line": 2, "column": 10, "nodeType": "91", "messageId": "92", "endLine": 2, "endColumn": 15}, {"ruleId": "95", "severity": 1, "message": "96", "line": 41, "column": 6, "nodeType": "97", "endLine": 41, "endColumn": 19, "suggestions": "98"}, {"ruleId": "95", "severity": 1, "message": "99", "line": 30, "column": 6, "nodeType": "97", "endLine": 30, "endColumn": 39, "suggestions": "100"}, {"ruleId": "95", "severity": 1, "message": "101", "line": 86, "column": 6, "nodeType": "97", "endLine": 86, "endColumn": 36, "suggestions": "102"}, {"ruleId": "95", "severity": 1, "message": "103", "line": 140, "column": 6, "nodeType": "97", "endLine": 140, "endColumn": 49, "suggestions": "104"}, {"ruleId": "89", "severity": 1, "message": "105", "line": 5, "column": 10, "nodeType": "91", "messageId": "92", "endLine": 5, "endColumn": 17}, {"ruleId": "89", "severity": 1, "message": "106", "line": 5, "column": 57, "nodeType": "91", "messageId": "92", "endLine": 5, "endColumn": 64}, {"ruleId": "89", "severity": 1, "message": "107", "line": 388, "column": 33, "nodeType": "91", "messageId": "92", "endLine": 388, "endColumn": 45}, {"ruleId": "89", "severity": 1, "message": "108", "line": 3, "column": 111, "nodeType": "91", "messageId": "92", "endLine": 3, "endColumn": 124}, {"ruleId": "95", "severity": 1, "message": "109", "line": 15, "column": 9, "nodeType": "110", "endLine": 19, "endColumn": 4}, {"ruleId": "89", "severity": 1, "message": "111", "line": 155, "column": 9, "nodeType": "91", "messageId": "92", "endLine": 155, "endColumn": 27}, {"ruleId": "89", "severity": 1, "message": "112", "line": 3, "column": 23, "nodeType": "91", "messageId": "92", "endLine": 3, "endColumn": 32}, {"ruleId": "89", "severity": 1, "message": "113", "line": 3, "column": 34, "nodeType": "91", "messageId": "92", "endLine": 3, "endColumn": 41}, {"ruleId": "89", "severity": 1, "message": "106", "line": 3, "column": 135, "nodeType": "91", "messageId": "92", "endLine": 3, "endColumn": 142}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setPatientInfo' is assigned a value but never used.", "'toast' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleDetectionResults'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], "React Hook useCallback has a missing dependency: 'startAutoCapture'. Either include it or remove the dependency array.", ["115"], "React Hook useCallback has a missing dependency: 'sendImageForAnalysis'. Either include it or remove the dependency array.", ["116"], "React Hook useEffect has a missing dependency: 'startAutoCapture'. Either include it or remove the dependency array.", ["117"], "'FaTooth' is defined but never used.", "'FaClock' is defined but never used.", "'healthyCount' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "The 'classColors' object makes the dependencies of useCallback Hook (at line 62) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'classColors' in its own useMemo() Hook.", "VariableDeclarator", "'getConfidenceColor' is assigned a value but never used.", "'FaFileAlt' is defined but never used.", "'FaPhone' is defined but never used.", {"desc": "118", "fix": "119"}, {"desc": "120", "fix": "121"}, {"desc": "122", "fix": "123"}, {"desc": "124", "fix": "125"}, "Update the dependencies array to be: [handleDetectionResults, isAnalyzing]", {"range": "126", "text": "127"}, "Update the dependencies array to be: [autoCapture, onConnectionStatus, startAutoCapture]", {"range": "128", "text": "129"}, "Update the dependencies array to be: [isStreaming, onImageCaptured, sendImageForAnalysis]", {"range": "130", "text": "131"}, "Update the dependencies array to be: [autoCapture, captureInterval, isStreaming, startAutoCapture]", {"range": "132", "text": "133"}, [1131, 1144], "[handleDetectionResults, isAnalyzing]", [1003, 1036], "[autoCapture, onConnectionStatus, startAutoCapture]", [2551, 2581], "[isStreaming, onImageCaptured, sendImageForAnalysis]", [3945, 3988], "[autoCapture, captureInterval, isStreaming, startAutoCapture]"]