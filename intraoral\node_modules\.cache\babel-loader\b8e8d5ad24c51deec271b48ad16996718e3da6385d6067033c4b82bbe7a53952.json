{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\AnalysisResults.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaSearch, FaExclamationTriangle, FaCheckCircle, FaInfoCircle, FaCalendar, FaPhone, FaFileAlt } from 'react-icons/fa';\nimport './AnalysisResults.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisResults = ({\n  results,\n  isAnalyzing\n}) => {\n  _s();\n  const [analysisSummary, setAnalysisSummary] = useState(null);\n  const [recommendations, setRecommendations] = useState([]);\n  useEffect(() => {\n    if (results && results.length > 0) {\n      generateAnalysisSummary(results);\n      generateRecommendations(results);\n    }\n  }, [results]);\n  const generateAnalysisSummary = detectionResults => {\n    const summary = {\n      totalDetections: detectionResults.length,\n      primaryIssue: null,\n      confidence: 0,\n      severity: 'low'\n    };\n\n    // Find the detection with highest confidence\n    const highestConfidence = detectionResults.reduce((max, current) => current.confidence > max.confidence ? current : max);\n    summary.primaryIssue = highestConfidence.class;\n    summary.confidence = highestConfidence.confidence;\n\n    // Determine severity based on detections\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    if (decayCount > 0) {\n      summary.severity = 'high';\n    } else if (earlyDecayCount > 0) {\n      summary.severity = 'medium';\n    } else {\n      summary.severity = 'low';\n    }\n    setAnalysisSummary(summary);\n  };\n  const generateRecommendations = detectionResults => {\n    const recs = [];\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n    if (decayCount > 0) {\n      recs.push({\n        type: 'urgent',\n        title: 'Immediate Treatment Required',\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\n        icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 15\n        }, this),\n        priority: 1\n      });\n    }\n    if (earlyDecayCount > 0) {\n      recs.push({\n        type: 'warning',\n        title: 'Preventive Care Needed',\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\n        icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 15\n        }, this),\n        priority: 2\n      });\n    }\n    if (healthyCount > 0) {\n      recs.push({\n        type: 'positive',\n        title: 'Good Oral Health',\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\n        icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 15\n        }, this),\n        priority: 3\n      });\n    }\n\n    // Add general recommendations\n    recs.push({\n      type: 'info',\n      title: 'Regular Checkup',\n      description: 'Schedule your next dental checkup within 6 months.',\n      icon: /*#__PURE__*/_jsxDEV(FaCalendar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 13\n      }, this),\n      priority: 4\n    });\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\n  };\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'high':\n        return '#ef4444';\n      case 'medium':\n        return '#f59e0b';\n      case 'low':\n        return '#10b981';\n      default:\n        return '#0077B6';\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'high':\n        return /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 27\n        }, this);\n      case 'medium':\n        return /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 29\n        }, this);\n      case 'low':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 26\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaInfoCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  if (isAnalyzing) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6] flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), \"Analysis Results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] rounded-lg p-8 flex flex-col items-center justify-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"animate-spin text-[#0077B6] text-2xl mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#333333] font-medium\",\n          children: \"Processing analysis results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  }\n  if (!results || results.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6] flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), \"Analysis Results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-[rgba(0,119,182,0.05)] rounded-lg p-8 flex flex-col items-center justify-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n          className: \"text-4xl text-gray-400 mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg font-medium text-[#333333] mb-2\",\n          children: \"No analysis results available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Start video stream to begin analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-[#0077B6] flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), \"Analysis Results\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 text-sm text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [results.length, \" detection(s)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2022\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: new Date().toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), analysisSummary && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6]\",\n        children: \"Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-4 hover:border-[#20B2AA] transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl\",\n            style: {\n              color: getSeverityColor(analysisSummary.severity)\n            },\n            children: getSeverityIcon(analysisSummary.severity)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-[#333333] capitalize\",\n                children: analysisSummary.primaryIssue.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [(analysisSummary.confidence * 100).toFixed(1), \"% confidence\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-medium px-2 py-1 rounded-full\",\n                style: {\n                  backgroundColor: `${getSeverityColor(analysisSummary.severity)}20`,\n                  color: getSeverityColor(analysisSummary.severity)\n                },\n                children: [analysisSummary.severity.toUpperCase(), \" SEVERITY\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: [analysisSummary.totalDetections, \" total detection(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6]\",\n        children: \"Detected Conditions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: results.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xl\",\n                children: [result.class === 'decaycavity' && '🦷', result.class === 'early-decay' && '⚠️', result.class === 'healthy tooth' && '✅']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-[#333333] capitalize\",\n                  children: result.class.replace('-', ' ')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [(result.confidence * 100).toFixed(1), \"% confidence\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-20 h-2 bg-gray-200 rounded-full overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-full rounded-full transition-all duration-300\",\n                style: {\n                  width: `${result.confidence * 100}%`,\n                  backgroundColor: result.confidence >= 0.8 ? '#10b981' : result.confidence >= 0.6 ? '#f59e0b' : '#ef4444'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6]\",\n        children: \"Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-white border rounded-lg p-3 hover:border-[#20B2AA] transition-colors ${rec.type === 'urgent' ? 'border-red-200 bg-red-50' : rec.type === 'warning' ? 'border-yellow-200 bg-yellow-50' : rec.type === 'positive' ? 'border-green-200 bg-green-50' : 'border-gray-200'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-lg ${rec.type === 'urgent' ? 'text-red-500' : rec.type === 'warning' ? 'text-yellow-500' : rec.type === 'positive' ? 'text-green-500' : 'text-[#0077B6]'}`,\n              children: rec.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-[#333333] mb-1\",\n                children: rec.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-600\",\n                children: rec.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-md font-semibold text-[#0077B6]\",\n        children: \"Next Steps\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaFileAlt, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), \"Generate Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), \"Contact Dentist\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(FaCalendar, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), \"Schedule Follow-up\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisResults, \"UYQlwBe3O42oXBiuSsTXDpUE2eU=\");\n_c = AnalysisResults;\nexport default AnalysisResults;\nvar _c;\n$RefreshReg$(_c, \"AnalysisResults\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaChartLine", "FaSpinner", "FaSearch", "FaExclamationTriangle", "FaCheckCircle", "FaInfoCircle", "FaCalendar", "FaPhone", "FaFileAlt", "jsxDEV", "_jsxDEV", "AnalysisResults", "results", "isAnalyzing", "_s", "analysisSummary", "setAnalysisSummary", "recommendations", "setRecommendations", "length", "generateAnalysisSummary", "generateRecommendations", "detectionResults", "summary", "totalDetections", "primaryIssue", "confidence", "severity", "highestConfidence", "reduce", "max", "current", "class", "decayCount", "filter", "r", "earlyDecayCount", "recs", "healthyCount", "push", "type", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "priority", "sort", "a", "b", "getSeverityColor", "getSeverityIcon", "className", "children", "Date", "toLocaleTimeString", "style", "color", "replace", "toFixed", "backgroundColor", "toUpperCase", "map", "result", "index", "width", "rec", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/AnalysisResults.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { FaChart<PERSON>ine, Fa<PERSON><PERSON><PERSON>, FaSearch, FaExclamationTriangle, FaCheckCircle, FaInfoCircle, FaCalendar, FaPhone, FaFileAlt } from 'react-icons/fa';\r\nimport './AnalysisResults.css';\r\n\r\nconst AnalysisResults = ({ results, isAnalyzing }) => {\r\n  const [analysisSummary, setAnalysisSummary] = useState(null);\r\n  const [recommendations, setRecommendations] = useState([]);\r\n\r\n  useEffect(() => {\r\n    if (results && results.length > 0) {\r\n      generateAnalysisSummary(results);\r\n      generateRecommendations(results);\r\n    }\r\n  }, [results]);\r\n\r\n  const generateAnalysisSummary = (detectionResults) => {\r\n    const summary = {\r\n      totalDetections: detectionResults.length,\r\n      primaryIssue: null,\r\n      confidence: 0,\r\n      severity: 'low'\r\n    };\r\n\r\n    // Find the detection with highest confidence\r\n    const highestConfidence = detectionResults.reduce((max, current) => \r\n      current.confidence > max.confidence ? current : max\r\n    );\r\n\r\n    summary.primaryIssue = highestConfidence.class;\r\n    summary.confidence = highestConfidence.confidence;\r\n\r\n    // Determine severity based on detections\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n\r\n    if (decayCount > 0) {\r\n      summary.severity = 'high';\r\n    } else if (earlyDecayCount > 0) {\r\n      summary.severity = 'medium';\r\n    } else {\r\n      summary.severity = 'low';\r\n    }\r\n\r\n    setAnalysisSummary(summary);\r\n  };\r\n\r\n  const generateRecommendations = (detectionResults) => {\r\n    const recs = [];\r\n\r\n    const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\r\n    const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\r\n    const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\r\n\r\n    if (decayCount > 0) {\r\n      recs.push({\r\n        type: 'urgent',\r\n        title: 'Immediate Treatment Required',\r\n        description: 'Cavities detected require immediate dental treatment to prevent further damage.',\r\n        icon: <FaExclamationTriangle />,\r\n        priority: 1\r\n      });\r\n    }\r\n\r\n    if (earlyDecayCount > 0) {\r\n      recs.push({\r\n        type: 'warning',\r\n        title: 'Preventive Care Needed',\r\n        description: 'Early decay signs detected. Schedule a follow-up appointment for preventive treatment.',\r\n        icon: <FaExclamationTriangle />,\r\n        priority: 2\r\n      });\r\n    }\r\n\r\n    if (healthyCount > 0) {\r\n      recs.push({\r\n        type: 'positive',\r\n        title: 'Good Oral Health',\r\n        description: 'Healthy teeth detected. Continue with regular oral hygiene routine.',\r\n        icon: <FaCheckCircle />,\r\n        priority: 3\r\n      });\r\n    }\r\n\r\n    // Add general recommendations\r\n    recs.push({\r\n      type: 'info',\r\n      title: 'Regular Checkup',\r\n      description: 'Schedule your next dental checkup within 6 months.',\r\n      icon: <FaCalendar />,\r\n      priority: 4\r\n    });\r\n\r\n    setRecommendations(recs.sort((a, b) => a.priority - b.priority));\r\n  };\r\n\r\n  const getSeverityColor = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return '#ef4444';\r\n      case 'medium': return '#f59e0b';\r\n      case 'low': return '#10b981';\r\n      default: return '#0077B6';\r\n    }\r\n  };\r\n\r\n  const getSeverityIcon = (severity) => {\r\n    switch (severity) {\r\n      case 'high': return <FaExclamationTriangle />;\r\n      case 'medium': return <FaExclamationTriangle />;\r\n      case 'low': return <FaCheckCircle />;\r\n      default: return <FaInfoCircle />;\r\n    }\r\n  };\r\n\r\n  if (isAnalyzing) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6] flex items-center gap-2\">\r\n            <FaChartLine />\r\n            Analysis Results\r\n          </h3>\r\n        </div>\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] rounded-lg p-8 flex flex-col items-center justify-center text-center\">\r\n          <FaSpinner className=\"animate-spin text-[#0077B6] text-2xl mb-4\" />\r\n          <p className=\"text-[#333333] font-medium\">Processing analysis results...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!results || results.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <h3 className=\"text-lg font-semibold text-[#0077B6] flex items-center gap-2\">\r\n            <FaChartLine />\r\n            Analysis Results\r\n          </h3>\r\n        </div>\r\n        <div className=\"bg-[rgba(0,119,182,0.05)] rounded-lg p-8 flex flex-col items-center justify-center text-center\">\r\n          <FaSearch className=\"text-4xl text-gray-400 mb-4\" />\r\n          <p className=\"text-lg font-medium text-[#333333] mb-2\">No analysis results available</p>\r\n          <p className=\"text-sm text-gray-500\">Start video stream to begin analysis</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-semibold text-[#0077B6] flex items-center gap-2\">\r\n          <FaChartLine />\r\n          Analysis Results\r\n        </h3>\r\n        <div className=\"flex gap-2 text-sm text-gray-500\">\r\n          <span>{results.length} detection(s)</span>\r\n          <span>•</span>\r\n          <span>{new Date().toLocaleTimeString()}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Summary */}\r\n      {analysisSummary && (\r\n        <div className=\"space-y-3\">\r\n          <h4 className=\"text-md font-semibold text-[#0077B6]\">Summary</h4>\r\n          <div className=\"bg-white border border-gray-200 rounded-lg p-4 hover:border-[#20B2AA] transition-colors\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <div \r\n                className=\"text-2xl\"\r\n                style={{ color: getSeverityColor(analysisSummary.severity) }}\r\n              >\r\n                {getSeverityIcon(analysisSummary.severity)}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex justify-between items-start mb-2\">\r\n                  <span className=\"text-sm font-medium text-[#333333] capitalize\">\r\n                    {analysisSummary.primaryIssue.replace('-', ' ')}\r\n                  </span>\r\n                  <span className=\"text-sm text-gray-500\">\r\n                    {(analysisSummary.confidence * 100).toFixed(1)}% confidence\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center\">\r\n                  <span \r\n                    className=\"text-xs font-medium px-2 py-1 rounded-full\"\r\n                    style={{ \r\n                      backgroundColor: `${getSeverityColor(analysisSummary.severity)}20`,\r\n                      color: getSeverityColor(analysisSummary.severity)\r\n                    }}\r\n                  >\r\n                    {analysisSummary.severity.toUpperCase()} SEVERITY\r\n                  </span>\r\n                  <span className=\"text-xs text-gray-500\">\r\n                    {analysisSummary.totalDetections} total detection(s)\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Detected Conditions */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6]\">Detected Conditions</h4>\r\n        <div className=\"space-y-3\">\r\n          {results.map((result, index) => (\r\n            <div key={index} className=\"bg-white border border-gray-200 rounded-lg p-3 hover:border-[#20B2AA] transition-colors\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"text-xl\">\r\n                    {result.class === 'decaycavity' && '🦷'}\r\n                    {result.class === 'early-decay' && '⚠️'}\r\n                    {result.class === 'healthy tooth' && '✅'}\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-[#333333] capitalize\">\r\n                      {result.class.replace('-', ' ')}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-500\">\r\n                      {(result.confidence * 100).toFixed(1)}% confidence\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"w-20 h-2 bg-gray-200 rounded-full overflow-hidden\">\r\n                  <div \r\n                    className=\"h-full rounded-full transition-all duration-300\"\r\n                    style={{ \r\n                      width: `${result.confidence * 100}%`,\r\n                      backgroundColor: result.confidence >= 0.8 ? '#10b981' : \r\n                                     result.confidence >= 0.6 ? '#f59e0b' : '#ef4444'\r\n                    }}\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recommendations */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6]\">Recommendations</h4>\r\n        <div className=\"space-y-3\">\r\n          {recommendations.map((rec, index) => (\r\n            <div key={index} className={`bg-white border rounded-lg p-3 hover:border-[#20B2AA] transition-colors ${\r\n              rec.type === 'urgent' ? 'border-red-200 bg-red-50' :\r\n              rec.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :\r\n              rec.type === 'positive' ? 'border-green-200 bg-green-50' :\r\n              'border-gray-200'\r\n            }`}>\r\n              <div className=\"flex items-start gap-3\">\r\n                <div className={`text-lg ${\r\n                  rec.type === 'urgent' ? 'text-red-500' :\r\n                  rec.type === 'warning' ? 'text-yellow-500' :\r\n                  rec.type === 'positive' ? 'text-green-500' :\r\n                  'text-[#0077B6]'\r\n                }`}>\r\n                  {rec.icon}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h5 className=\"text-sm font-medium text-[#333333] mb-1\">{rec.title}</h5>\r\n                  <p className=\"text-xs text-gray-600\">{rec.description}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Next Steps */}\r\n      <div className=\"space-y-3\">\r\n        <h4 className=\"text-md font-semibold text-[#0077B6]\">Next Steps</h4>\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#005a8b] hover:to-[#1a9a8f] transition-all duration-300 text-sm\">\r\n            <FaFileAlt className=\"mr-2\" />\r\n            Generate Report\r\n          </button>\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\">\r\n            <FaPhone className=\"mr-2\" />\r\n            Contact Dentist\r\n          </button>\r\n          <button className=\"flex items-center justify-center px-4 py-2 bg-white border border-[#20B2AA] text-[#20B2AA] rounded-lg hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 text-sm\">\r\n            <FaCalendar className=\"mr-2\" />\r\n            Schedule Follow-up\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnalysisResults; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,UAAU,EAAEC,OAAO,EAAEC,SAAS,QAAQ,gBAAgB;AACrJ,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAIa,OAAO,IAAIA,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MACjCC,uBAAuB,CAACR,OAAO,CAAC;MAChCS,uBAAuB,CAACT,OAAO,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMQ,uBAAuB,GAAIE,gBAAgB,IAAK;IACpD,MAAMC,OAAO,GAAG;MACdC,eAAe,EAAEF,gBAAgB,CAACH,MAAM;MACxCM,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGN,gBAAgB,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAC7DA,OAAO,CAACL,UAAU,GAAGI,GAAG,CAACJ,UAAU,GAAGK,OAAO,GAAGD,GAClD,CAAC;IAEDP,OAAO,CAACE,YAAY,GAAGG,iBAAiB,CAACI,KAAK;IAC9CT,OAAO,CAACG,UAAU,GAAGE,iBAAiB,CAACF,UAAU;;IAEjD;IACA,MAAMO,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IAEtF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBV,OAAO,CAACI,QAAQ,GAAG,MAAM;IAC3B,CAAC,MAAM,IAAIS,eAAe,GAAG,CAAC,EAAE;MAC9Bb,OAAO,CAACI,QAAQ,GAAG,QAAQ;IAC7B,CAAC,MAAM;MACLJ,OAAO,CAACI,QAAQ,GAAG,KAAK;IAC1B;IAEAX,kBAAkB,CAACO,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMF,uBAAuB,GAAIC,gBAAgB,IAAK;IACpD,MAAMe,IAAI,GAAG,EAAE;IAEf,MAAMJ,UAAU,GAAGX,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACjF,MAAMiB,eAAe,GAAGd,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,aAAa,CAAC,CAACb,MAAM;IACtF,MAAMmB,YAAY,GAAGhB,gBAAgB,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACH,KAAK,KAAK,eAAe,CAAC,CAACb,MAAM;IAErF,IAAIc,UAAU,GAAG,CAAC,EAAE;MAClBI,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,8BAA8B;QACrCC,WAAW,EAAE,iFAAiF;QAC9FC,IAAI,eAAEjC,OAAA,CAACP,qBAAqB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC/BC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIZ,eAAe,GAAG,CAAC,EAAE;MACvBC,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,wFAAwF;QACrGC,IAAI,eAAEjC,OAAA,CAACP,qBAAqB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC/BC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,IAAIV,YAAY,GAAG,CAAC,EAAE;MACpBD,IAAI,CAACE,IAAI,CAAC;QACRC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,qEAAqE;QAClFC,IAAI,eAAEjC,OAAA,CAACN,aAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACvBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;;IAEA;IACAX,IAAI,CAACE,IAAI,CAAC;MACRC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,oDAAoD;MACjEC,IAAI,eAAEjC,OAAA,CAACJ,UAAU;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF9B,kBAAkB,CAACmB,IAAI,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,QAAQ,GAAGG,CAAC,CAACH,QAAQ,CAAC,CAAC;EAClE,CAAC;EAED,MAAMI,gBAAgB,GAAIzB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM0B,eAAe,GAAI1B,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,oBAAOjB,OAAA,CAACP,qBAAqB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,QAAQ;QAAE,oBAAOrC,OAAA,CAACP,qBAAqB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,KAAK;QAAE,oBAAOrC,OAAA,CAACN,aAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;QAAS,oBAAOrC,OAAA,CAACL,YAAY;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClC;EACF,CAAC;EAED,IAAIlC,WAAW,EAAE;IACf,oBACEH,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAK4C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChD7C,OAAA;UAAI4C,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC1E7C,OAAA,CAACV,WAAW;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNrC,OAAA;QAAK4C,SAAS,EAAC,gGAAgG;QAAAC,QAAA,gBAC7G7C,OAAA,CAACT,SAAS;UAACqD,SAAS,EAAC;QAA2C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnErC,OAAA;UAAG4C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACnC,OAAO,IAAIA,OAAO,CAACO,MAAM,KAAK,CAAC,EAAE;IACpC,oBACET,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAK4C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChD7C,OAAA;UAAI4C,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC1E7C,OAAA,CAACV,WAAW;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNrC,OAAA;QAAK4C,SAAS,EAAC,gGAAgG;QAAAC,QAAA,gBAC7G7C,OAAA,CAACR,QAAQ;UAACoD,SAAS,EAAC;QAA6B;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDrC,OAAA;UAAG4C,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAA6B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFrC,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAoC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7C,OAAA;MAAK4C,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD7C,OAAA;QAAI4C,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC1E7C,OAAA,CAACV,WAAW;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrC,OAAA;QAAK4C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C7C,OAAA;UAAA6C,QAAA,GAAO3C,OAAO,CAACO,MAAM,EAAC,eAAa;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CrC,OAAA;UAAA6C,QAAA,EAAM;QAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACdrC,OAAA;UAAA6C,QAAA,EAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhC,eAAe,iBACdL,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAI4C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAO;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjErC,OAAA;QAAK4C,SAAS,EAAC,yFAAyF;QAAAC,QAAA,eACtG7C,OAAA;UAAK4C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7C,OAAA;YACE4C,SAAS,EAAC,UAAU;YACpBI,KAAK,EAAE;cAAEC,KAAK,EAAEP,gBAAgB,CAACrC,eAAe,CAACY,QAAQ;YAAE,CAAE;YAAA4B,QAAA,EAE5DF,eAAe,CAACtC,eAAe,CAACY,QAAQ;UAAC;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNrC,OAAA;YAAK4C,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB7C,OAAA;cAAK4C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7C,OAAA;gBAAM4C,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC5DxC,eAAe,CAACU,YAAY,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACPrC,OAAA;gBAAM4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpC,CAACxC,eAAe,CAACW,UAAU,GAAG,GAAG,EAAEmC,OAAO,CAAC,CAAC,CAAC,EAAC,cACjD;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNrC,OAAA;cAAK4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD7C,OAAA;gBACE4C,SAAS,EAAC,4CAA4C;gBACtDI,KAAK,EAAE;kBACLI,eAAe,EAAE,GAAGV,gBAAgB,CAACrC,eAAe,CAACY,QAAQ,CAAC,IAAI;kBAClEgC,KAAK,EAAEP,gBAAgB,CAACrC,eAAe,CAACY,QAAQ;gBAClD,CAAE;gBAAA4B,QAAA,GAEDxC,eAAe,CAACY,QAAQ,CAACoC,WAAW,CAAC,CAAC,EAAC,WAC1C;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPrC,OAAA;gBAAM4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpCxC,eAAe,CAACS,eAAe,EAAC,qBACnC;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrC,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAI4C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAmB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7ErC,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB3C,OAAO,CAACoD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBxD,OAAA;UAAiB4C,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eAClH7C,OAAA;YAAK4C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD7C,OAAA;cAAK4C,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC7C,OAAA;gBAAK4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,GACrBU,MAAM,CAACjC,KAAK,KAAK,aAAa,IAAI,IAAI,EACtCiC,MAAM,CAACjC,KAAK,KAAK,aAAa,IAAI,IAAI,EACtCiC,MAAM,CAACjC,KAAK,KAAK,eAAe,IAAI,GAAG;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNrC,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAG4C,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EACzDU,MAAM,CAACjC,KAAK,CAAC4B,OAAO,CAAC,GAAG,EAAE,GAAG;gBAAC;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACJrC,OAAA;kBAAG4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjC,CAACU,MAAM,CAACvC,UAAU,GAAG,GAAG,EAAEmC,OAAO,CAAC,CAAC,CAAC,EAAC,cACxC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrC,OAAA;cAAK4C,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAChE7C,OAAA;gBACE4C,SAAS,EAAC,iDAAiD;gBAC3DI,KAAK,EAAE;kBACLS,KAAK,EAAE,GAAGF,MAAM,CAACvC,UAAU,GAAG,GAAG,GAAG;kBACpCoC,eAAe,EAAEG,MAAM,CAACvC,UAAU,IAAI,GAAG,GAAG,SAAS,GACtCuC,MAAM,CAACvC,UAAU,IAAI,GAAG,GAAG,SAAS,GAAG;gBACxD;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3BEmB,KAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAI4C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAe;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzErC,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBtC,eAAe,CAAC+C,GAAG,CAAC,CAACI,GAAG,EAAEF,KAAK,kBAC9BxD,OAAA;UAAiB4C,SAAS,EAAE,2EAC1Bc,GAAG,CAAC5B,IAAI,KAAK,QAAQ,GAAG,0BAA0B,GAClD4B,GAAG,CAAC5B,IAAI,KAAK,SAAS,GAAG,gCAAgC,GACzD4B,GAAG,CAAC5B,IAAI,KAAK,UAAU,GAAG,8BAA8B,GACxD,iBAAiB,EAChB;UAAAe,QAAA,eACD7C,OAAA;YAAK4C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC7C,OAAA;cAAK4C,SAAS,EAAE,WACdc,GAAG,CAAC5B,IAAI,KAAK,QAAQ,GAAG,cAAc,GACtC4B,GAAG,CAAC5B,IAAI,KAAK,SAAS,GAAG,iBAAiB,GAC1C4B,GAAG,CAAC5B,IAAI,KAAK,UAAU,GAAG,gBAAgB,GAC1C,gBAAgB,EACf;cAAAe,QAAA,EACAa,GAAG,CAACzB;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNrC,OAAA;cAAK4C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB7C,OAAA;gBAAI4C,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAEa,GAAG,CAAC3B;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxErC,OAAA;gBAAG4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEa,GAAG,CAAC1B;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnBEmB,KAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7C,OAAA;QAAI4C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAU;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpErC,OAAA;QAAK4C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD7C,OAAA;UAAQ4C,SAAS,EAAC,2LAA2L;UAAAC,QAAA,gBAC3M7C,OAAA,CAACF,SAAS;YAAC8C,SAAS,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UAAQ4C,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAC3L7C,OAAA,CAACH,OAAO;YAAC+C,SAAS,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA;UAAQ4C,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAC3L7C,OAAA,CAACJ,UAAU;YAACgD,SAAS,EAAC;UAAM;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAhSIH,eAAe;AAAA0D,EAAA,GAAf1D,eAAe;AAkSrB,eAAeA,eAAe;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}