import type * as TSESLint from '../ts-eslint';
import type { ParserServices } from '../ts-estree';
/**
 * Try to retrieve typescript parser service from context
 */
declare function getParserServices<TMessageIds extends string, TOptions extends readonly unknown[]>(context: Readonly<TSESLint.RuleContext<TMessageIds, TOptions>>, allowWithoutFullTypeInformation?: boolean): ParserServices;
export { getParserServices };
//# sourceMappingURL=getParserServices.d.ts.map