const mongoose = require('mongoose');

const LabRequestSchema = new mongoose.Schema({
  studentId: {
    type: String,
    required: true
  },
  studentName: {
    type: String,
    required: true
  },
  patientId: {
    type: String,
    required: true
  },
  patientName: {
    type: String,
    required: true
  },
  labType: {
    type: String,
    enum: ['university', 'outside'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'completed'],
    default: 'pending'
  },
  submitDate: {
    type: Date,
    default: Date.now
  },
  notes: {
    type: String,
    default: ''
  },
  responseDate: {
    type: Date
  },
  responseNotes: {
    type: String,
    default: ''
  },
  responderId: {
    type: String
  },
  responderName: {
    type: String
  }
}, { timestamps: true });

module.exports = mongoose.model('LabRequest', LabRequestSchema);
