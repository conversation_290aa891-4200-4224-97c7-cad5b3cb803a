import { NavLink, useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFileAlt, FaHistory, FaTooth, FaImages, FaCalendarAlt, FaFileSignature, FaClipboardCheck, FaFlask } from 'react-icons/fa';

const PatientNav = ({ selectedChart, setSelectedChart, charts }) => {
  const { nationalId } = useParams();
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/patients/public/${nationalId}`);
        setPatientData(response.data);
      } catch (err) {
        console.error('Error fetching patient data:', err.response?.status, err.response?.data);
        const message = err.response?.status === 404
          ? 'Patient not found'
          : err.response?.data?.message || 'Failed to load patient data';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [nationalId]);



  if (loading) {
    return (
      <div className="bg-white shadow-sm border-b border-gray-100 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="animate-pulse flex items-center space-x-4">
            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !patientData) {
    return (
      <div className="bg-white shadow-sm border-b border-gray-100 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="text-red-500">{error || 'Patient not found'}</div>
        </div>
      </div>
    );
  }

  const renderChartSelector = (isActive) => {
    if (!isActive || !charts || charts.length === 0) return null;
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="ml-2"
      >
        <select
          value={selectedChart?._id || ''}
          onChange={(e) => setSelectedChart(charts.find(chart => chart._id === e.target.value))}
          className="px-3 py-1.5 rounded-lg bg-white border border-gray-200 text-sm text-gray-600 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-200"
        >
          {charts.map(chart => (
            <option key={chart._id} value={chart._id}>
              {chart.title} - {new Date(chart.date).toLocaleDateString()} {chart.isLocked ? '(Locked)' : ''}
            </option>
          ))}
        </select>
      </motion.div>
    );
  };

  return (
    <motion.nav
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)] px-4 py-3 overflow-x-auto w-full"
    >
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <NavLink to={`/patientprofile/${nationalId}`} className="flex items-center w-full md:w-auto hover:opacity-80 transition-opacity">
          <div className="bg-[rgba(0,119,182,0.1)] p-2 rounded-lg mr-3 flex-shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-[#0077B6]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <div className="min-w-0">
            <h2 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Current Patient</h2>
            <h1 className="text-lg font-semibold text-gray-800 truncate">{patientData.fullName}</h1>
          </div>
        </NavLink>

        <div className="w-full md:w-auto mx-auto flex flex-col md:flex-row items-center gap-2">
          <ul className="flex flex-wrap justify-center gap-1 md:gap-2 w-full overflow-x-auto py-2">


            {/* Sheets */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/sheets`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaFileAlt className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Patient Examination Sheet</span>
              </NavLink>
            </li>

            {/* History */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/history`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaHistory className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">History</span>
              </NavLink>
            </li>

            {/* Tooth Chart */}
            <li className="flex items-center">
              <NavLink
                to={`/patientprofile/${nationalId}/toothchart`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaTooth className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Tooth Chart</span>
              </NavLink>
              {renderChartSelector(window.location.pathname.includes('/toothchart'))}
            </li>



            {/* Gallery */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/gallery`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaImages className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Gallery</span>
              </NavLink>
            </li>



            {/* Appointments */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/appointments`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#20B2AA]/10 text-[#20B2AA]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaCalendarAlt className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Appointments</span>
              </NavLink>
            </li>

            {/* Consent */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/consent`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#28A745]/10 text-[#28A745]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaFileSignature className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Consent</span>
              </NavLink>
            </li>

            {/* Review Steps */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/reviewsteps`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#0077B6]/10 text-[#0077B6]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaClipboardCheck className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Review Steps</span>
              </NavLink>
            </li>

            {/* Lab */}
            <li>
              <NavLink
                to={`/patientprofile/${nationalId}/lab`}
                className={({ isActive }) =>
                  `flex flex-col items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? 'bg-[#FF6B35]/10 text-[#FF6B35]' : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <FaFlask className="h-5 w-5" />
                <span className="text-xs mt-1 font-medium">Lab</span>
              </NavLink>
            </li>
          </ul>

        </div>

        <div className="hidden md:block w-48"></div>
      </div>
    </motion.nav>
  );
};

export default PatientNav;