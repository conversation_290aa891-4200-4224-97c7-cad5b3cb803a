import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./types";
/**
 * Creates a deep copy of a process coverage.
 *
 * @param processCov Process coverage to clone.
 * @return Cloned process coverage.
 */
export declare function cloneProcessCov(processCov: Readonly<ProcessCov>): ProcessCov;
/**
 * Creates a deep copy of a script coverage.
 *
 * @param scriptCov Script coverage to clone.
 * @return Cloned script coverage.
 */
export declare function cloneScript<PERSON>ov(scriptCov: Readonly<ScriptCov>): ScriptCov;
/**
 * Creates a deep copy of a function coverage.
 *
 * @param functionCov Function coverage to clone.
 * @return Cloned function coverage.
 */
export declare function cloneFunctionCov(functionCov: Readonly<FunctionCov>): FunctionCov;
/**
 * Creates a deep copy of a function coverage.
 *
 * @param rangeCov Range coverage to clone.
 * @return Cloned range coverage.
 */
export declare function cloneRangeCov(rangeCov: Readonly<RangeCov>): Range<PERSON>ov;
