{"name": "@babel/plugin-syntax-import-assertions", "version": "7.27.1", "description": "Allow parsing of the module assertion attributes in the import statement", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-assertions"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}