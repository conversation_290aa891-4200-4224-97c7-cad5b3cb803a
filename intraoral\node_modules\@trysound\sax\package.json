{"version": "0.2.0", "name": "@trysound/sax", "description": "An evented streaming XML parser in JavaScript", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "repository": "git://github.com/svg/sax.git", "license": "ISC", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/TrySound"}], "main": "lib/sax.js", "files": ["lib"], "engines": {"node": ">=10.13.0"}, "scripts": {"test": "tap test/*.js --cov -j4", "lint": "standard -F test/*.js lib/*.js"}, "devDependencies": {"standard": "^8.6.0", "tap": "^10.5.1"}}