# `@typescript-eslint/experimental-utils`

Utilities for working with TypeScript + ESLint together.

[![NPM Version](https://img.shields.io/npm/v/@typescript-eslint/experimental-utils.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/experimental-utils)
[![NPM Downloads](https://img.shields.io/npm/dm/@typescript-eslint/experimental-utils.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/experimental-utils)

## Note

**This package is purely a re-export of `@typescript-eslint/utils`.**
You should switch to importing from that non-experimental package instead.

```diff
- import { RuleCreator } from '@typescript-eslint/experimental-utils';
+ import { RuleCreator } from '@typescript-eslint/utils';
```

> ⚠ A future major version of this old package will `console.warn` to ask you to switch.

## Contributing

[See the contributing guide here](https://typescript-eslint.io).
